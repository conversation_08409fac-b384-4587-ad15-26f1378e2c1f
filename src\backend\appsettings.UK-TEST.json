{
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=7dffe635-86be-480a-b6ee-74ece547337e;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=1cb887fb-36be-4d33-93e0-f469b25e99a9"
  },
  "AzureOpenAI": {
    "Endpoint": "https://aiutility-apim-test.ajgco.com"
  },
  "AzureStorage": {
    "Endpoints": {
      "Default": "https://corpdlstestr7kesy.blob.core.windows.net/"
    },
    "ExpirationPolicy": 30 //Days
  },
  "AzureDocIntel": {
    "Endpoint": "https://gallagherai-doc-int-8w3b.cognitiveservices.azure.com/"
  },
  "AzureSearch": {
    "Endpoints": {
      "Default": "https://corp-gallagheraiuk-srch-test-7ghrl.search.windows.net"
    }
  },
  "AzureKeyVault": {
    "Endpoint": "https://corp-kv-test-c75n80.vault.azure.net/"
  },
  "Conversion": {
    "Endpoint": "https://corp-aigo-app-yuxp-test.azurewebsites.net/forms/libreoffice/convert"
  },
  "Translation": {
    "Endpoint": "https://gallagherai-doc-translate-np04.cognitiveservices.azure.com/"
  }
}