﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Blob Storage Service.
    /// </summary>
    public interface IStorageService
    {
        /// <summary>
        /// Gets all workspaces from Blob Storage starting with the prefix.
        /// </summary>
        Task<List<Workspace>> GetWorkspaces(string prefix, CancellationToken ct);

        /// <summary>
        /// Gets information about a workspace from Blob Storage.
        /// </summary>
        Task<Workspace> GetWorkspace(string prefix, CancellationToken ct);

        /// <summary>
        /// Creates a new workspace in Blob Storage.
        /// </summary>
        Task<Workspace> CreateWorkspaceAsync(string oid, Workspace item);

        /// <summary>
        /// Updates an existing workspace in Blob Storage.
        /// </summary>
        Task<Workspace> UpdateWorkspaceAsync(string oid, Workspace item);

        /// <summary>
        /// Refreshes the last modified date of an object and it's children in Blob Storage.
        /// </summary>
        Task Refresh(string prefix);

        /// <summary>
        /// Deletes an object from Blob Storage.
        /// </summary>
        Task DeleteBlobAsync(string prefix, bool isFile = true);

        /// <summary>
        /// Gets all documents in a workspace from Blob Storage.
        /// </summary>
        Task<List<Document>> GetDocuments(string prefix, CancellationToken ct, bool fetchAll = false);

        /// <summary>
        /// Downloads a document from Blob Storage.
        /// </summary>
        Task<Stream> GetDocumentStreamAsync(string document, CancellationToken ct);

        /// <summary>
        /// Uploads a document to Blob Storage.
        /// </summary>
        Task<Document> UploadDocumentsAsync(string oid,
                                            string workspaceId,
                                            string fileName,
                                            Stream documentStream,
                                            CancellationToken ct,
                                            string original = ".pdf");
    }
}
