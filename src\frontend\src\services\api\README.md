# API Services with Redux Toolkit Query

This directory contains the API services implemented with Redux Toolkit Query (RTK Query) for the application.

## Structure

- `api.ts`: Base RTK Query configuration
- `chatApi.ts`: API for the chat
- `documentsApi.ts`: API for documents
- `settingsApi.ts`: API for settings (uses IndexedDB)
- `translationApi.ts`: API for translation
- `workspacesApi.ts`: API for workspaces

## Features

- **Automatic cache management**: RTK Query automatically handles response caching
- **Loading and error states**: Provides loading and error states for API calls
- **Cache invalidation**: Allows cache invalidation when performing mutations
- **Full typing**: Complete TypeScript support for type safety

## API Services Overview

### Base Configuration (`../app/api.ts`)

The base API configuration provides:
- **Authentication**: Automatic token injection and refresh
- **Error Handling**: 401 error interception with token refresh
- **Cache Management**: Organized tag-based cache invalidation
- **Type Safety**: Full TypeScript integration

### Service Architecture

Each API service follows a consistent pattern:
1. **Endpoints Definition**: Using RTK Query's `injectEndpoints`
2. **Type Safety**: Comprehensive TypeScript interfaces
3. **Cache Tags**: Proper cache invalidation strategies
4. **Error Handling**: Consistent error response handling

## Detailed API Services

### Workspaces API (`workspacesApi.ts`)

**Endpoints:**
- `getWorkspaces` - Fetch all workspaces
- `getWorkspace` - Fetch single workspace by ID
- `createWorkspace` - Create new workspace
- `updateWorkspace` - Update existing workspace
- `deleteWorkspace` - Delete workspace

**Cache Strategy:**
- Provides `Workspaces` tag for list invalidation
- Individual workspace tags for granular updates

### Documents API (`documentsApi.ts`)

**Endpoints:**
- `uploadDocument` - Upload document to workspace
- `downloadDocument` - Download document from workspace
- `deleteDocument` - Delete document from workspace

**Special Features:**
- **File Upload**: Handles FormData for document uploads
- **Blob Handling**: Proper blob response handling for downloads
- **Streaming**: Support for large file operations

### Chat API (`chatApi.ts`)

**Endpoints:**
- `sendChatMessage` - Send chat message (with streaming support)

**Special Features:**
- **Streaming Support**: Custom implementation for real-time chat
- **Manual Fetch**: Uses custom queryFn for streaming responses
- **Utility Function**: `streamChatResponse` for component-level streaming

### Translation API (`translationApi.ts`)

**Endpoints:**
- `translateText` - Translate text content
- `translateDocument` - Translate document files
- `getTranslatedDocuments` - Fetch translated documents list
- `downloadTranslatedDocument` - Download translated document

**Features:**
- **File Translation**: Handles document translation workflows
- **Progress Tracking**: Support for translation progress monitoring

### Settings API (`settingsApi.ts`)

**Endpoints:**
- `getSettings` - Fetch user settings from IndexedDB
- `updateSettings` - Update user settings in IndexedDB

**Special Features:**
- **IndexedDB Integration**: Local storage for user preferences
- **Offline Support**: Works without network connectivity
- **Encryption**: Secure storage of sensitive settings

## Usage Examples

### Basic Query Usage

To perform API queries, use the hooks generated by RTK Query:

```tsx
import { useGetWorkspacesQuery } from '../services/api/workspacesApi';

const MyComponent = () => {
  const { data: workspaces, isLoading, error } = useGetWorkspacesQuery();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.toString()}</div>;

  return (
    <div>
      <h1>Workspaces</h1>
      <ul>
        {workspaces?.map((workspace) => (
          <li key={workspace.id}>{workspace.name}</li>
        ))}
      </ul>
    </div>
  );
};
```

### Mutations (Create, Update, Delete)

To perform mutations, use the mutation hooks generated by RTK Query:

```tsx
import { useCreateWorkspaceMutation } from '../services/api/workspacesApi';

const MyComponent = () => {
  const [createWorkspace, { isLoading }] = useCreateWorkspaceMutation();

  const handleCreateWorkspace = async () => {
    try {
      const result = await createWorkspace({
        name: 'New workspace',
        description: 'Description'
      }).unwrap();

      console.log('Workspace created:', result);
    } catch (error) {
      console.error('Error creating workspace:', error);
    }
  };

  return (
    <button onClick={handleCreateWorkspace} disabled={isLoading}>
      {isLoading ? 'Creating...' : 'Create workspace'}
    </button>
  );
};
```

### Response Streaming

For response streaming (such as in chat), helper functions are used:

```tsx
import { streamChatResponse, ChatRequest } from '../services/api/chatApi';

const handleSendMessage = async (message: string) => {
  const chatRequest: ChatRequest = {
    workspace_id: workspaceId,
    settings: {},
    messages: [
      {
        role: 'user',
        content: [{ type: 'text', value: message }]
      }
    ]
  };

  try {
    await streamChatResponse(
      chatRequest,
      token,
      (chunk) => {
        // Process each response chunk
        console.log('Chunk received:', chunk);
      }
    );
  } catch (error) {
    console.error('Streaming error:', error);
  }
};
```

### File Operations

#### Document Upload
```tsx
import { useUploadDocumentMutation } from '../services/api/documentsApi';

const DocumentUpload = ({ workspaceId }: { workspaceId: string }) => {
  const [uploadDocument, { isLoading, error }] = useUploadDocumentMutation();

  const handleFileUpload = async (file: File) => {
    try {
      await uploadDocument({ workspaceId, file }).unwrap();
      console.log('Document uploaded successfully');
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  return (
    <input
      type="file"
      onChange={(e) => {
        const file = e.target.files?.[0];
        if (file) handleFileUpload(file);
      }}
      disabled={isLoading}
    />
  );
};
```

#### Document Download
```tsx
import { useLazyDownloadDocumentQuery } from '../services/api/documentsApi';

const DocumentDownload = () => {
  const [downloadDocument] = useLazyDownloadDocumentQuery();

  const handleDownload = async (workspaceId: string, fileName: string) => {
    try {
      const blob = await downloadDocument({ workspaceId, fileName }).unwrap();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.click();

      // Cleanup
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  return (
    <button onClick={() => handleDownload('workspace-id', 'document.pdf')}>
      Download Document
    </button>
  );
};
```

### Settings Management (IndexedDB)

```tsx
import { useGetSettingsQuery, useUpdateSettingsMutation } from '../services/api/settingsApi';

const SettingsComponent = () => {
  const { data: settings, isLoading } = useGetSettingsQuery();
  const [updateSettings] = useUpdateSettingsMutation();

  const handleSettingChange = async (key: string, value: any) => {
    try {
      await updateSettings({ [key]: value }).unwrap();
    } catch (error) {
      console.error('Failed to update setting:', error);
    }
  };

  if (isLoading) return <div>Loading settings...</div>;

  return (
    <div>
      <h2>Settings</h2>
      {/* Settings UI components */}
    </div>
  );
};
```

## Advanced Features

### Cache Management

#### Manual Cache Invalidation
```tsx
import { api } from '../app/api';
import { useAppDispatch } from '../app/hooks';

const MyComponent = () => {
  const dispatch = useAppDispatch();

  const invalidateWorkspaces = () => {
    dispatch(api.util.invalidateTags(['Workspaces']));
  };

  return <button onClick={invalidateWorkspaces}>Refresh Data</button>;
};
```

#### Selective Cache Updates
```tsx
// Update specific workspace in cache
dispatch(api.util.updateQueryData('getWorkspaces', undefined, (draft) => {
  const workspace = draft.find(w => w.id === updatedWorkspace.id);
  if (workspace) {
    Object.assign(workspace, updatedWorkspace);
  }
}));
```

### Error Handling Patterns

#### Component-Level Error Handling
```tsx
const { data, error, isLoading } = useGetWorkspacesQuery();

if (error) {
  if ('status' in error) {
    // RTK Query error with status
    if (error.status === 401) {
      return <div>Authentication required</div>;
    }
    if (error.status === 403) {
      return <div>Access denied</div>;
    }
  }
  return <div>An error occurred</div>;
}
```

#### Global Error Handling
```tsx
// In your error boundary or global error handler
const handleApiError = (error: any) => {
  if (error?.status === 401) {
    // Redirect to login
    window.location.href = '/login';
  } else if (error?.status >= 500) {
    // Show server error message
    showToast('Server error. Please try again later.');
  }
};
```

## Authentication

API calls automatically include the authentication token from the Redux store. The token is updated using the `useApiWithAuth` hook, which must be used in components that make API calls.

```tsx
import useApiWithAuth from '../hooks/useApiWithAuth';

const MyComponent = () => {
  const { isAuthenticated, refreshToken } = useApiWithAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      refreshToken();
    }
  }, [isAuthenticated, refreshToken]);

  if (!isAuthenticated) {
    return <div>Authenticating...</div>;
  }

  // Rest of the component...
};
```

## Performance Best Practices

### 1. Conditional Queries
```tsx
// Skip query when data is not needed
const { data } = useGetWorkspaceQuery(workspaceId, {
  skip: !workspaceId || !isAuthenticated
});
```

### 2. Polling for Real-time Data
```tsx
// Poll every 30 seconds
const { data } = useGetWorkspacesQuery(undefined, {
  pollingInterval: 30000
});
```

### 3. Prefetching
```tsx
import { api } from '../app/api';

// Prefetch data on hover
const handleMouseEnter = () => {
  dispatch(api.util.prefetch('getWorkspace', workspaceId, { force: false }));
};
```

### 4. Optimistic Updates
```tsx
const [updateWorkspace] = useUpdateWorkspaceMutation({
  onQueryStarted: async ({ id, workspace }, { dispatch, queryFulfilled }) => {
    // Optimistic update
    const patchResult = dispatch(
      api.util.updateQueryData('getWorkspaces', undefined, (draft) => {
        const existingWorkspace = draft.find(w => w.id === id);
        if (existingWorkspace) {
          Object.assign(existingWorkspace, workspace);
        }
      })
    );

    try {
      await queryFulfilled;
    } catch {
      // Rollback on error
      patchResult.undo();
    }
  }
});
```

## Troubleshooting

### Common Issues

1. **Authentication Errors (401)**
   - Ensure `useApiWithAuth` is used in components making API calls
   - Check token expiration and refresh logic

2. **Cache Not Updating**
   - Verify proper tag invalidation in mutations
   - Check if cache tags are correctly defined

3. **TypeScript Errors**
   - Ensure API types match backend response structure
   - Update type definitions when backend changes

4. **Performance Issues**
   - Use `skip` option for conditional queries
   - Implement proper cache invalidation strategies
   - Consider using `selectFromResult` for data transformation

### Debug Tools

1. **Redux DevTools**: Monitor RTK Query actions and state
2. **Network Tab**: Inspect actual API calls and responses
3. **RTK Query DevTools**: Use the built-in query inspector
