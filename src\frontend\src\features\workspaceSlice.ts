import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Workspace, DocumentRecord } from "../interfaces";

const initialState: Workspace = {
    id: '',
    name: '',
    description: '',
    documents: [],
};

const WorkspaceSlice = createSlice({
    name: 'workspace',
    initialState,
    reducers: {
        setWorkspace(state, action: PayloadAction<{ id: string; name: string, description: string, documents: DocumentRecord[] }>) {
            state.id = action.payload.id;
            state.name = action.payload.name;
            state.description = action.payload.description;
            state.documents = action.payload.documents;
        },
        setWorkspaceProperty(state, action: PayloadAction<{ property: keyof Workspace, value: any }>) {
            const { property, value } = action.payload;
            state[property] = value;
        },
        clearWorkspace(state) {
            state.id = '';
            state.name = '';
            state.description = '';
            state.documents = [];
        }
    },
});

export const { setWorkspace, setWorkspaceProperty, clearWorkspace } = WorkspaceSlice.actions;
export default WorkspaceSlice.reducer;
