import { useEffect, useState } from "react";

const useThemeSwitcher = () => {
  const [theme, setTheme] = useState<"light" | "dark">("light");

  useEffect(() => {
    const themeId = "markdown-theme";

    const getThemeHref = (isDarkMode: boolean) => {
      // Verifies if the mode is localhost or cloud based
      const isLocalEnv = import.meta.env.MODE === "localhost";

      const basePath = isLocalEnv
        ? "/node_modules/github-markdown-css" // Localhost styles route
        : "/assets/styles"; // Cloud styles route (staticly generated by the build in pipelines)

      return isDarkMode
        ? `${basePath}/github-markdown-dark.css`
        : `${basePath}/github-markdown-light.css`;
    };

    const loadMarkdownTheme = (isDarkMode: boolean) => {
      let themeLink = document.getElementById(themeId) as HTMLLinkElement;

      if (themeLink) {
        themeLink.href = getThemeHref(isDarkMode);
      } else {
        themeLink = document.createElement("link");
        themeLink.id = themeId;
        themeLink.rel = "stylesheet";
        themeLink.href = getThemeHref(isDarkMode);
        document.head.appendChild(themeLink);
      }
    };

    const savedTheme = localStorage.getItem("theme");
    const isDarkMode = savedTheme === "dark";

    if (isDarkMode) {
      document.documentElement.classList.add("dark");
      setTheme("dark");
    } else {
      document.documentElement.classList.remove("dark");
      setTheme("light");
    }

    loadMarkdownTheme(isDarkMode);

    const handleThemeChange = () => {
      const newTheme = document.documentElement.classList.contains("dark") ? "dark" : "light";
      setTheme(newTheme);
      loadMarkdownTheme(newTheme === "dark");
    };

    window.addEventListener("themechange", handleThemeChange);

    return () => {
      window.removeEventListener("themechange", handleThemeChange);
    };
  }, []);

  return { theme };
};

export default useThemeSwitcher;