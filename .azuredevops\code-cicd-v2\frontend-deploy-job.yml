# frontend-deploy-job.yml

parameters:
  - name: environment
  - name: api_url
  - name: scopes
  - name: client_id
  - name: authority
  - name: region
  - name: connectionString
  - name: service_connection
  - name: deploy_agent_pool
  - name: app_name
  - name: job_name_suffix
    default: ''
  - name: dependsOn
    default: ''
  
jobs:
  - job: 'DeployJob_${{ parameters.environment }}_${{ parameters.job_name_suffix }}'
    pool: ${{ parameters.deploy_agent_pool }}
    displayName: 'Deploy Frontend to ${{ parameters.environment }}'
    ${{ if parameters.dependsOn }}:
      dependsOn: ${{ parameters.dependsOn }}
    steps:
      - task: DownloadBuildArtifacts@1
        displayName: 'Download Build Artifacts'
        inputs:
          artifactName: 'frontend'
          downloadPath: '$(Agent.BuildDirectory)'
          buildType: 'current'
          downloadType: 'single'
          continueOnError: false
      
      - task: ExtractFiles@1
        displayName: 'Extract Frontend'
        inputs:
          archiveFilePatterns: '$(Agent.BuildDirectory)/**/*.zip' 
          destinationFolder: '$(Agent.TempDirectory)/frontend'

      - task: PowerShell@2
        displayName: 'Configure Environment Variables'
        inputs:
          targetType: 'inline'
          workingDirectory: '$(Agent.BuildDirectory)/frontend'
          script: |
            $path = "$(Agent.TempDirectory)/frontend"
            $pattern = "\[!!(.*?)!!\]"
            $envs = Get-ChildItem env:

            Get-ChildItem -Path $path -Recurse -Filter "*.js" -File | 
            ForEach-Object {
                $content = Get-Content $_.FullName
                $results = $content | Select-String $pattern -AllMatches
  
                foreach ($tag in $results.Matches.Value) {
                    $cleanTag = $tag.replace('[!!', '').replace('!!]', '').replace('.', '_').ToUpper()
                    Write-Host "Processing tag: $cleanTag"

                    $data = ($envs | Where-Object { $_.Name -like $cleanTag }).Value

                    if ([string]::IsNullOrEmpty($data)) {
                        Write-Host "##vso[task.logissue type=error]$cleanTag was not initialized"
                        exit 1
                    }

                    (Get-Content $_.FullName).replace($tag, $data) | Set-Content $_.FullName
                }
            }
        env:
          API_URL: ${{ parameters.api_url }}
          Scopes: ${{ parameters.scopes }}
          Client_ID: ${{ parameters.client_id }}
          Authority: ${{ parameters.authority }}
          Region: ${{ parameters.region }}
          ConnectionString: ${{ parameters.connectionString}}
      
      - task: ArchiveFiles@2
        displayName: 'Archive frontend'
        inputs:
          rootFolderOrFile: '$(Agent.TempDirectory)/frontend'
          includeRootFolder: false
          archiveFile: '$(Build.ArtifactStagingDirectory)/frontend.zip'
          replaceExistingArchive: true

      - task: AzureWebApp@1
        displayName: 'Deploy'
        inputs:
          azureSubscription: ${{ parameters.service_connection }}
          appType: 'webAppLinux'
          appName: ${{ parameters.app_name }}
          package: '$(Build.ArtifactStagingDirectory)/frontend.zip'