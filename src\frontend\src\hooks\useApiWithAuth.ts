import { useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { setToken } from '../features/tokenSlice';
import { useAppDispatch, useAppSelector } from '../app/hooks';
import { RootState } from '../store';

/**
 * Custom hook to ensure API calls have a valid authentication token
 * This hook should be used in components that make API calls
 * It will automatically refresh the token if needed
 */
const useApiWithAuth = () => {
  const { activeAccount, acquireToken } = useAuth();
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  // Get current token from Redux store
  const currentToken = useAppSelector((state: RootState) => state.token.token);

  // Function to refresh the token
  const refreshToken = useCallback(async () => {
    if (!activeAccount) return;

    try {
      // Force a new token acquisition instead of using cached result
      const authResult = await acquireToken();
      if (authResult) {
        dispatch(setToken(authResult.accessToken));
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
    }
  }, [activeAccount, acquireToken, dispatch]);

  // Initial token refresh on component mount
  useEffect(() => {
    if (activeAccount && !currentToken) {
      refreshToken();
    }
  }, [activeAccount, currentToken, refreshToken]);

  return {
    isAuthenticated: !!activeAccount && !!currentToken,
    refreshToken
  };
};

export default useApiWithAuth;
