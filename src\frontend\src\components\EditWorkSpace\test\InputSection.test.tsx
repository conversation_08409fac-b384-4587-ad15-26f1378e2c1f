import { render, screen, fireEvent } from '@testing-library/react';
import InputSection from '../InputSection';
import { describe, it, expect, vi } from 'vitest';
import { MANAGE_WORKSPACE_DESCRIPTION_LIMIT, MANAGE_WORKSPACE_NAME_LIMIT } from '../../../constants/constants';

describe('InputSection', () => {
  const mockInputChange = vi.fn();

  const defaultProps = {
    workspaceIdFromUrl: '',
    name: '',
    description: '',
    inputChange: mockInputChange,
  };

  it('renders correctly for creating a workspace', () => {
    render(<InputSection {...defaultProps} />);
    expect(screen.getByText('Create Workspace')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Workspace name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Workspace description')).toBeInTheDocument();
  });

  it('renders correctly for editing a workspace', () => {
    render(<InputSection {...defaultProps} workspaceIdFromUrl="123" />);
    expect(screen.getByText('Edit Workspace')).toBeInTheDocument();
  });

  it('calls inputChange on name input change', () => {
    render(<InputSection {...defaultProps} />);
    const nameInput = screen.getByPlaceholderText('Workspace name');
    fireEvent.change(nameInput, { target: { value: 'New Workspace' } });
    expect(mockInputChange).toHaveBeenCalled();
  });

  it('calls inputChange on description input change', () => {
    render(<InputSection {...defaultProps} />);
    const descriptionInput = screen.getByPlaceholderText('Workspace description');
    fireEvent.change(descriptionInput, { target: { value: 'New Description' } });
    expect(mockInputChange).toHaveBeenCalled();
  });

  it('respects character limits for name and description', () => {
    render(<InputSection {...defaultProps} />);
    const nameInput = screen.getByPlaceholderText('Workspace name');
    const descriptionInput = screen.getByPlaceholderText('Workspace description');
    expect(nameInput).toHaveAttribute('maxLength', `${MANAGE_WORKSPACE_NAME_LIMIT}`);
    expect(descriptionInput).toHaveAttribute('maxLength', `${MANAGE_WORKSPACE_DESCRIPTION_LIMIT}`);
  });
});