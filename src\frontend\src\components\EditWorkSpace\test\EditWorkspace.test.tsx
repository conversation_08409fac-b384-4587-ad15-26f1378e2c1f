import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi } from 'vitest';
import EditWorkspace from '../EditWorkspace';
import workspaceReducer from '../../../features/workspaceSlice';

// Mock window.alert
Object.defineProperty(window, 'alert', {
  writable: true,
  value: vi.fn(),
});

// Mock react-router hooks
vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ search: '' }),
}));

// Mock the app hooks to prevent Redux dependency issues
vi.mock('../../../app/hooks', () => ({
  useAppDispatch: () => vi.fn(),
  useAppSelector: vi.fn(() => ({
    id: '',
    name: '',
    description: '',
    documents: [],
  })),
}));

// Mock external dependencies
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    acquireToken: vi.fn().mockResolvedValue({ accessToken: 'mock-token' }),
    activeAccount: { username: '<EMAIL>' },
    getAuthResult: vi.fn(),
  }),
}));

vi.mock('../../../hooks/useToast', () => ({
  default: () => ({
    toasts: [],
    triggerToast: vi.fn(),
    closeToast: vi.fn(),
  }),
}));

vi.mock('../../../services/workspacesService', () => ({
  fetchWorkspaceById: vi.fn(),
  createWorkspace: vi.fn(),
  editWorkspace: vi.fn(),
}));

vi.mock('../../../services/documentsService', () => ({
  uploadDocument: vi.fn(),
}));

vi.mock('../../../utils/processResponseStream', () => ({
  processResponseStream: vi.fn(),
}));

vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn(() => ({
    getRootProps: () => ({ 'data-testid': 'dropzone' }),
    getInputProps: () => ({ 'data-testid': 'file-input' }),
    isDragActive: false,
  })),
}));

// Mock child components
vi.mock('../BottomButtons', () => ({
  default: ({ save, workspaceIdFromUrl }: any) => (
    <div data-testid="bottom-buttons">
      <button onClick={save} data-testid="save-button">
        {workspaceIdFromUrl ? 'Update' : 'Create'}
      </button>
    </div>
  ),
}));

vi.mock('../InputSection', () => ({
  default: ({ name, description }: any) => (
    <div data-testid="input-section">
      <input data-testid="workspace-name" value={name} readOnly />
      <textarea data-testid="workspace-description" value={description} readOnly />
    </div>
  ),
}));

vi.mock('../../DocsContainer/DocumentsList', () => ({
  default: () => <div data-testid="documents-list">Documents List</div>,
}));

vi.mock('../../Tooltip/Tooltip', () => ({
  default: ({ children }: any) => <div data-testid="tooltip">{children}</div>,
}));

vi.mock('../../Modal/ToastModal/Toast', () => ({
  default: ({ text }: any) => <div data-testid="toast">{text}</div>,
}));

describe('EditWorkspace', () => {
  // Mock store for Redux Provider (simplified since we're mocking useAppSelector)
  const mockStore = configureStore({
    reducer: {
      workspace: workspaceReducer,
    },
    preloadedState: {
      workspace: {
        id: '',
        name: '',
        description: '',
        documents: [],
      },
    },
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <EditWorkspace />
        </MemoryRouter>
      </Provider>
    );
  };

  describe('Component Rendering', () => {
    it('should render the main heading', () => {
      renderComponent();
      expect(screen.getByText('Document Workspaces')).toBeInTheDocument();
    });

    it('should render the Quick Start guide link with correct attributes', () => {
      renderComponent();
      const quickStartLink = screen.getByText('Quick Start guide');

      expect(quickStartLink).toBeInTheDocument();
      expect(quickStartLink).toHaveAttribute('href', expect.stringContaining('sharepoint.com'));
      expect(quickStartLink).toHaveAttribute('target', '_blank');
      expect(quickStartLink).toHaveClass('text-gallagher-dark-300', 'dark:text-sky-400', 'underline');
    });

    it('should render InputSection and BottomButtons components', () => {
      renderComponent();
      expect(screen.getByTestId('input-section')).toBeInTheDocument();
      expect(screen.getByTestId('bottom-buttons')).toBeInTheDocument();
    });

    it('should apply dark theme classes correctly', () => {
      renderComponent();
      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('dark:bg-zinc-800');
    });
  });

  describe('New Workspace Creation', () => {
    it('should not show documents section when creating new workspace', () => {
      renderComponent();
      expect(screen.queryByText('Documents uploaded:')).not.toBeInTheDocument();
      expect(screen.queryByTestId('documents-list')).not.toBeInTheDocument();
    });

    it('should show Create button for new workspace', () => {
      renderComponent();
      expect(screen.getByText('Create')).toBeInTheDocument();
    });
  });

  describe('Theme Support', () => {
    it('should apply light theme classes by default', () => {
      renderComponent();

      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('bg-white');
    });

    it('should support dark theme classes', () => {
      renderComponent();

      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('dark:bg-zinc-800');

      const heading = screen.getByText('Document Workspaces');
      expect(heading).toHaveClass('dark:text-gray-300');
    });
  });
});
