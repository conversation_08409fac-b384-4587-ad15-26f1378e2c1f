# *****************************************
#          Primary Storage Account 
# *****************************************

module "storage_account" {
  source                                   = "app.terraform.io/ajg/storage-account/azurerm"
  version                                  = "3.1.1"
  resource_group                           = var.azurerm_resource_group_name
  azurerm_location                         = var.azurerm_resource_group_location
  azurerm_private_dns_zone_blob_ids        = [var.blob_dns_zone_id]
  azurerm_private_dns_zone_web_ids         = [var.static_app_dns_zone_id]
  azurerm_private_endpoint_subnet_id       = var.azurerm_private_endpoint_subnet_id
  azurerm_log_anlytics_workspace_id        = var.azurerm_log_analytics_workspace_id
  region                                   = var.azurerm_resource_group_location
  environment                              = var.environment
  division                                 = "corp"
  storage_account_account_replication_type = var.storageaccount_settings.replication_type
  storage_account_account_tier             = var.storageaccount_settings.account_tier
  storage_account_account_kind             = var.storageaccount_settings.account_kind
  storage_account_is_hns_enabled           = true
  storage_account_blob_properties = {
    # change_feed_enabled             = true
    container_delete_retention_policy = { days = 7 }
    container_versioning_enabled      = true
    delete_retention_policy           = { days = 7 }
    last_access_time_enabled          = true
  }
  tags = merge(var.tags, { "environment" = var.environment })
}

resource "azurerm_storage_data_lake_gen2_filesystem" "gen2_filesystem" {
  name               = "user-content"
  storage_account_id = module.storage_account.resource_id
  ace {
    scope       = "access"
    type        = "user"
    id          = var.current_principal_id # the princpal_id of the current user
    permissions = "rwx"
  }
}

resource "azurerm_storage_data_lake_gen2_filesystem" "usertranslate_filesystem" {
  name               = "user-translation"
  storage_account_id = module.storage_account.resource_id
  ace {
    scope       = "access"
    type        = "user"
    id          = var.current_principal_id # the princpal_id of the current user
    permissions = "rwx"
  }
}

# *****************************************
#      Additional Regional Storage 
# *****************************************

resource "random_string" "rand" {
  for_each = toset(var.additional_storage_regions)
  length   = 6
  special  = false
  upper    = false
}

resource "azurerm_storage_account" "asr" {
  for_each                        = toset(var.additional_storage_regions)
  account_replication_type        = var.storageaccount_settings.replication_type
  account_tier                    = var.storageaccount_settings.account_tier
  location                        = each.key
  name                            = "corpdls${var.environment}${random_string.rand[each.key].result}"
  resource_group_name             = var.azurerm_resource_group_name
  access_tier                     = "Hot"
  account_kind                    = var.storageaccount_settings.account_kind
  allow_nested_items_to_be_public = false
  https_traffic_only_enabled      = true
  is_hns_enabled                  = true
  min_tls_version                 = "TLS1_2"
  public_network_access_enabled   = false
  tags                            = merge(var.tags, { "environment" = var.environment })

  blob_properties {
    #change_feed_enabled          = true
    last_access_time_enabled = true

    delete_retention_policy {
      days = 7
    }

    container_delete_retention_policy {
      days = 7
    }
  }
}

data "azurerm_monitor_diagnostic_categories" "sad" {
  for_each    = azurerm_storage_account.asr
  resource_id = each.value.id
}

resource "azurerm_private_endpoint" "lake" {
  for_each            = azurerm_storage_account.asr
  location            = var.azurerm_resource_group_location
  name                = "${each.value.name}-dfs-pe"
  resource_group_name = var.azurerm_resource_group_name
  subnet_id           = var.azurerm_private_endpoint_subnet_id
  tags                = merge(var.tags, { "environment" = var.environment })

  private_service_connection {
    name                           = "pe-dfs-${each.value.name}"
    private_connection_resource_id = each.value.id
    is_manual_connection           = false
    subresource_names              = ["dfs"]
  }
  private_dns_zone_group {
    name                 = "default"
    private_dns_zone_ids = ["/subscriptions/9b2f9fb3-2e3c-4a93-833a-9bdda2da2ecd/resourceGroups/dns-main-rg/providers/Microsoft.Network/privateDnsZones/privatelink.dfs.core.windows.net"]
  }

  depends_on = [azurerm_storage_account.asr]
}

resource "azurerm_private_endpoint" "blob" {
  for_each            = azurerm_storage_account.asr
  location            = var.azurerm_resource_group_location
  name                = "${each.value.name}-blob-pe"
  resource_group_name = var.azurerm_resource_group_name
  subnet_id           = var.azurerm_private_endpoint_subnet_id
  tags                = merge(var.tags, { "environment" = var.environment })

  private_service_connection {
    is_manual_connection           = false
    name                           = "pe-blob-${each.value.name}"
    private_connection_resource_id = each.value.id
    subresource_names              = ["blob"]
  }

  private_dns_zone_group {
    name                 = "default"
    private_dns_zone_ids = [var.blob_dns_zone_id]
  }

  depends_on = [azurerm_storage_account.asr]
}

resource "azurerm_storage_data_lake_gen2_filesystem" "add_gen2_filesystem" {
  for_each           = azurerm_storage_account.asr
  name               = "user-content"
  storage_account_id = each.value.id
  ace {
    scope       = "access"
    type        = "user"
    id          = var.current_principal_id # the princpal_id of the current user
    permissions = "rwx"
  }
}

resource "azurerm_storage_data_lake_gen2_filesystem" "add_usertranslate_filesystem" {
  for_each           = azurerm_storage_account.asr
  name               = "user-translation"
  storage_account_id = each.value.id
  ace {
    scope       = "access"
    type        = "user"
    id          = var.current_principal_id # the princpal_id of the current user
    permissions = "rwx"
  }
}