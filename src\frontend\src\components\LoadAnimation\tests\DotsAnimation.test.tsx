import { render } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import DotsAnimation from '../DotsAnimation';

describe('DotsAnimation', () => {
  it('renders four dots', () => {
    const { container } = render(<DotsAnimation />);
    const dots = container.querySelectorAll('.dot');
    expect(dots.length).toBe(4);
  });

  it('renders dots with correct class names', () => {
    const { container } = render(<DotsAnimation />);
    expect(container.querySelector('.dot1')).toBeInTheDocument();
    expect(container.querySelector('.dot2')).toBeInTheDocument();
    expect(container.querySelector('.dot3')).toBeInTheDocument();
    expect(container.querySelector('.dot4')).toBeInTheDocument();
  });
});
