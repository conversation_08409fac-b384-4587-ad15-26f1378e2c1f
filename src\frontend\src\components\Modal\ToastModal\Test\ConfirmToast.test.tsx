import { render, screen, fireEvent } from "@testing-library/react";
import ConfirmToast from "../ConfirmToast";
import { describe, it, expect, vi } from "vitest";

describe("ConfirmToast", () => {
  const defaultProps = {
    text: 'Are you sure you want to delete this workspace?',
    onConfirm: vi.fn(),
    onCancel: vi.fn(),
  };

  const renderComponent = (props = {}) =>
    render(<ConfirmToast position={"top-left"} {...defaultProps} {...props} />);

  it("renders the confirmation message", () => {
    renderComponent();
    expect(
      screen.getByText('Are you sure you want to delete this workspace?')).toBeInTheDocument();
  });

  it("calls onConfirm when 'OK' button is clicked", () => {
    renderComponent();
    fireEvent.click(screen.getByText("OK"));
    expect(defaultProps.onConfirm).toHaveBeenCalled();
  });

  it("calls onCancel when 'Cancel' button is clicked", () => {
    renderComponent();
    fireEvent.click(screen.getByText("Cancel"));
    expect(defaultProps.onCancel).toHaveBeenCalled();
  });
});