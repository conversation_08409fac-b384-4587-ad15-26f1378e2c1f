resource "azurerm_monitor_autoscale_setting" "autoscale_backend_settings" {
  count               = (var.environment == "prod") && (var.region_instance == "us") ? 1 : 0
  name                = "gallagher_ai_autoscale_settings"
  enabled             = true
  resource_group_name = var.azurerm_resource_group_name
  location            = var.azurerm_resource_group_location
  target_resource_id  = module.appserviceplan.id

  profile {
    name = "Default Autoscale"

    capacity {
      default = 1
      minimum = 1
      maximum = 8
    }

    rule {
      metric_trigger {
        metric_name              = "CpuPercentage"
        metric_resource_id       = module.appserviceplan.id
        divide_by_instance_count = true
        time_grain               = "PT1M"
        statistic                = "Average"
        time_window              = "PT10M"
        time_aggregation         = "Average"
        operator                 = "GreaterThanOrEqual"
        threshold                = 75
      }

      scale_action {
        direction = "Increase"
        type      = "ChangeCount"
        value     = "1"
        cooldown  = "PT1M"
      }
    }

    rule {
      metric_trigger {
        metric_name              = "CpuPercentage"
        metric_resource_id       = module.appserviceplan.id
        divide_by_instance_count = true
        time_grain               = "PT1M"
        statistic                = "Average"
        time_window              = "PT10M"
        time_aggregation         = "Average"
        operator                 = "LessThanOrEqual"
        threshold                = 30
      }

      scale_action {
        direction = "Decrease"
        type      = "ChangeCount"
        value     = "1"
        cooldown  = "PT5M"
      }
    }

    rule {
      metric_trigger {
        metric_name              = "MemoryPercentage"
        metric_resource_id       = module.appserviceplan.id
        divide_by_instance_count = true
        time_grain               = "PT1M"
        statistic                = "Average"
        time_window              = "PT10M"
        time_aggregation         = "Average"
        operator                 = "GreaterThanOrEqual"
        threshold                = 85
      }

      scale_action {
        direction = "Increase"
        type      = "ChangeCount"
        value     = "1"
        cooldown  = "PT1M"
      }
    }

    rule {
      metric_trigger {
        metric_name              = "MemoryPercentage"
        metric_resource_id       = module.appserviceplan.id
        divide_by_instance_count = true
        time_grain               = "PT1M"
        statistic                = "Average"
        time_window              = "PT10M"
        time_aggregation         = "Average"
        operator                 = "LessThanOrEqual"
        threshold                = 55
      }

      scale_action {
        direction = "Decrease"
        type      = "ChangeCount"
        value     = "1"
        cooldown  = "PT5M"
      }
    }
  }

  notification {
    email {
      send_to_subscription_administrator    = false
      send_to_subscription_co_administrator = false
      custom_emails                         = ["<EMAIL>"]
    }
  }
}