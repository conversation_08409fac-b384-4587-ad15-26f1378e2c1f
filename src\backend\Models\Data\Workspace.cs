﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Represents a workspace holding user documents.
    /// </summary>
    public record Workspace()
    {
        /// <summary>
        /// Identifier of the workspace.
        /// </summary>
        [JsonPropertyName("id")] 
        public string? Id { get; set; }

        /// <summary>
        /// Name of the workspace.
        /// </summary>
        [JsonPropertyName("name")] 
        public string? Name { get; set; }

        /// <summary>
        /// Description of the workspace.
        /// </summary>
        [JsonPropertyName("description")] 
        public string? Description { get; set; }

        /// <summary>
        /// Date the document expires.
        /// </summary>
        [JsonPropertyName("expires")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Expires { get; set; }

        /// <summary>
        /// Gets or sets the document info within the workspace. 
        /// </summary>
        [JsonPropertyName("documents")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public List<Document>? Documents { get; set; }
    }
}
