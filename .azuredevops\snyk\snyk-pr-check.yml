name: <PERSON><PERSON><PERSON>-Frontend-CI

variables:
  - template: .azuredevops/code-cicd/variables.yml
  - group: Snyk

trigger: none
     
stages:
    - stage: Build
      pool:
        vmImage: 'ubuntu-latest'
      jobs:
        - job: BuildJob
          displayName: 'Build Frontend'
          steps:

          - task: Npm@1
            displayName: 'npm Install'
            inputs:
              command: 'custom'
              customCommand: 'install --legacy-peer-deps'
              workingDir: '$(Build.SourcesDirectory)/src/frontend'

          - task: Npm@1
            displayName: 'npm Build'
            inputs:
              command: 'custom'
              customCommand: 'run build'
              workingDir: '$(Build.SourcesDirectory)/src/frontend'

          - task: UseDotNet@2
            displayName: 'Install .NET 8'
            inputs:
             version: 8.x
             performMultiLevelLookup: true
             includePreviewVersions: true # Required for preview versions

          - task: DotNetCoreCLI@2
            displayName: 'Build'
            inputs:
              command: 'build'
              projects: '**/Backend.csproj'
              arguments: '--configuration $(buildConfiguration)'
              
          - template: ./Snyk-CLI-Setup.yml
          - template: ./Snyk-IAC-Templates.yml 
            parameters:
              orgName: 'corp-info-serv-and-appdev-ajg-corp'
              severityThreshold: 'medium'
              runMonitor: true                                                                            
          - task: SnykSecurityScan@1
            inputs:
              serviceConnectionEndpoint: 'Snyk_Connection'
              testType: 'code'
              failOnIssues: true
            condition: always()
            displayName: SAST

          - script: |
              temp_directory=$(Agent.TempDirectory)
              if [ ! -d "$temp_directory/snyk-reports" ]; then
                  mkdir -p "$temp_directory/snyk-reports"
              else
                  echo "snyk-reports folder exist"
              fi
              json_file=$(ls "$temp_directory"/report*.json 2>/dev/null)
              html_json=$(ls "$temp_directory"/report*.html 2>/dev/null)
              echo $file
              if [ -n "$json_file" ]; then
                  mv $temp_directory/report*.json $temp_directory/snyk-reports/SAST-report.json
              else
                  echo "JSON-Report not generated"
              fi
              if [ -n "$html_json" ]; then
                  mv $temp_directory/report*.html $temp_directory/snyk-reports/SAST-report.html
              else
                  echo "HTML-Report not generated"
              fi
            displayName: 'Getting SAST Reports'
            condition: always()

          - task: SnykSecurityScan@1
            inputs:
              serviceConnectionEndpoint: 'Snyk_Connection'
              testType: 'app'
              monitorWhen: 'never'
              failOnIssues: true
              organization: 'corp-info-serv-and-appdev-ajg-corp'
              additionalArguments: '--all-projects --detection-depth=4'
            condition: always()
            displayName: SCA

          #Generating the SCA reports(html and json)
          - script: |
              temp_directory=$(Agent.TempDirectory)
              if [ ! -d "$temp_directory/snyk-reports" ]; then
                  mkdir -p "$temp_directory/snyk-reports"
              else
                  echo "snyk-reports folder exist"
              fi
              json_file=$(ls "$temp_directory"/report*.json 2>/dev/null)
              html_json=$(ls "$temp_directory"/report*.html 2>/dev/null)
              echo $file
              if [ -n "$json_file" ]; then
                  mv $temp_directory/report*.json $temp_directory/snyk-reports/SCA-report.json
              else
                  echo "JSON-Report not generated"
              fi
              if [ -n "$html_json" ]; then
                  mv $temp_directory/report*.html $temp_directory/snyk-reports/SCA-report.html
              else
                  echo "HTML-Report not generated"
              fi
            displayName: 'Getting SCA Reports'
            condition: always()
                                 

          - task: PublishPipelineArtifact@1
            #displayName: 'Publish Snyk Reports'
            inputs:
              targetPath: '$(Agent.TempDirectory)/snyk-reports'
              artifact: 'snyk-report'
            condition: always()


          
          