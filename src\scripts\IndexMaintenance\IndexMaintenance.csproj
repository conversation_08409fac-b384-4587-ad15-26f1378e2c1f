﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>IndexMaintenance</AssemblyName>
    <RootNamespace>IndexMaintenance</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
	  <Content Include="run.sh">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
    <Content Include="settings.job">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Azure.Search.Documents" Version="11.6.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.22.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\backend\appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.AU-DEV.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.AU-PROD.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.AU-TEST.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.UK-DEV.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.UK-PROD.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.UK-TEST.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.US-DEV.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.US-PROD.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\backend\appsettings.US-TEST.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj" />
  </ItemGroup>
  

</Project>
