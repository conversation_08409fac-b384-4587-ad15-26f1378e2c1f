import { Workspace, WorkspaceInput } from "../interfaces";

const apiBaseUrl = import.meta.env.VITE_API_URL; // Define a base URL for the API

// Fetch all workspaces
export const fetchWorkspaces = async (token: string): Promise<Workspace[]> => {
    const response = await fetch(`${apiBaseUrl}/workspaces`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch workspaces: ${response.statusText}`);
    }

    return response.json();
};

// Fetch a single workspace by ID
export const fetchWorkspaceById = async (id: string, token: string, signal: AbortSignal): Promise<ReadableStream<Uint8Array>> => {
    const response = await fetch(`${apiBaseUrl}/workspaces/${id}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch workspace: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    const stream = new ReadableStream({
        start(controller) {
            function push() {
                reader?.read().then(({ done, value }) => {
                    if (done) {
                        controller.close();
                        return;
                    }
                    if (signal.aborted) {
                        return;
                    }
                    controller.enqueue(value);
                    push();
                }).catch(err => {
                    controller.error(err);
                });
            }
            push();
        },
        cancel() {
            reader?.cancel();
        }
    });

    return stream;
};

// Create a new workspace
export const createWorkspace = async (workspace: WorkspaceInput, token: string): Promise<Workspace> => {
    const response = await fetch(`${apiBaseUrl}/workspaces`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify(workspace),
    });

    if (!response.ok) {
        throw new Error(`Failed to create workspace: ${response.statusText}`);
    }

    return response.json();
};

// Edit an existing workspace
export const editWorkspace = async (id: string, workspace: WorkspaceInput, token: string): Promise<Workspace> => {
    const response = await fetch(`${apiBaseUrl}/workspaces/${id}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify(workspace),
    });

    if (!response.ok) {
        throw new Error(`Failed to edit workspace: ${response.statusText}`);
    }

    return response.json();
};

// Delete a workspace
export const deleteWorkspace = async (id: string, token: string): Promise<void> => {
    const response = await fetch(`${apiBaseUrl}/workspaces/${id}`, {
        method: "DELETE",
        headers: {
            "Authorization": `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to delete workspace: ${response.statusText}`);
    }
};
