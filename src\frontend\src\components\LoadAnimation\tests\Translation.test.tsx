import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useAuth } from '../../../hooks/useAuth';
import { fetchDocuments, deleteDocument } from '../../../services/translationService';
import TranslatedDocumentsList from '../../DocsContainer/TranslatedDocumentsList';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import translationFilesUploadFlagSlice from '../../../features/translationFilesUploadFlagSlice';

// Mock the dependencies with correct paths
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: vi.fn(), // Changed to vi.fn()
}));

vi.mock('../../../services/translationService', () => ({
  fetchDocuments: vi.fn(),
  downloadDocument: vi.fn(),
  deleteDocument: vi.fn(),
}));

vi.mock('../../utils/fileIcons', () => ({
  default: {
    pdf: 'pdf-icon-url',
    other: 'other-icon-url',
  },
}));

// Create a mock store
const createMockStore = () =>
  configureStore({
    reducer: {
      translationFilesUploadFlag: translationFilesUploadFlagSlice,
    },
  });

describe('TranslatedDocumentsList', () => {
  const mockAuthResult = { accessToken:process.env.REACT_APP_MOCK_ACCESS_TOKEN };
  let mockStore: ReturnType<typeof createMockStore>;

  beforeEach(() => {
    mockStore = createMockStore();
    vi.clearAllMocks();
    // Mock useAuth implementation using Vitest syntax
    (useAuth as ReturnType<typeof vi.fn>).mockImplementation(() => ({
      getAuthResult: vi.fn().mockResolvedValue(mockAuthResult),
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders loading spinner when isLoading is true', () => {
    render(
      <Provider store={mockStore}>
        <TranslatedDocumentsList isLoading={true} newDocument={false} />
      </Provider>
    );
    
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('displays no documents message when list is empty and not loading', () => {
    (fetchDocuments as any).mockResolvedValue([]);
    
    render(
      <Provider store={mockStore}>
        <TranslatedDocumentsList isLoading={false} newDocument={false} />
      </Provider>
    );
    
    expect(screen.getByText('No documents translated yet.')).toBeInTheDocument();
  });

  it('renders document list when documents are available', async () => {
    const mockDocs = [
      { name: 'test.pdf', processed: 1, expires: ********** },
    ];
    (fetchDocuments as any).mockResolvedValue(mockDocs);

    render(
      <Provider store={mockStore}>
        <TranslatedDocumentsList isLoading={false} newDocument={false} />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument();
      expect(screen.getByText('Expires: **********')).toBeInTheDocument();
    });
  });


  it('handles document deletion', async () => {
    const mockDocs = [{ name: 'test.pdf', processed: 1, expires: ********** }];
    (fetchDocuments as any).mockResolvedValue(mockDocs);
    (deleteDocument as any).mockResolvedValue(undefined);

    render(
      <Provider store={mockStore}>
        <TranslatedDocumentsList isLoading={false} newDocument={false} />
      </Provider>
    );

    await waitFor(() => {
      const deleteButton = screen.getByLabelText('Delete This Document');
      fireEvent.click(deleteButton);
    });

    expect(deleteDocument).toHaveBeenCalledWith('test.pdf', 'mock-token');
    await waitFor(() => {
      expect(screen.queryByText('test.pdf')).not.toBeInTheDocument();
    });
  });



  it('handles fetch documents error', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    (fetchDocuments as any).mockRejectedValue(new Error('Fetch failed'));

    render(
      <Provider store={mockStore}>
        <TranslatedDocumentsList isLoading={false} newDocument={false} />
      </Provider>
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error loading translated documents:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });
});