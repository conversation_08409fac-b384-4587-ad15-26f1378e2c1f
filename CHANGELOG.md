## 20250529
FEATURES:
- Dynamic context window adjustments to RAG process. #467320
- Custom prompt templates added. #457190

ENHANCEMENTS:
- Deploy pipeline improvements. #477282

BUG FIXES:
- Follow up question NullReferenceException. #480914

## 20250424
FEATURES:
- Text translation feature. #463761

ENHANCEMENTS:
- Bump tailwind to v4. #453044
- Deploy pipeline enhancements. #469487

BUG FIXES:
- Citations links fix. #468342
- <PERSON><PERSON><PERSON><PERSON> missing close button fix. #469824
- "pdftrailer is not null here" fix. #472576

## 20250327
FEATURES:
- Document translation feature. #455003
- Canadian storage resources now in Canadian datacenters. #454039
- Predictive prompts feature. #455056

BUG FIXES:
- Better chat history button visibility in mobile. #463061
- Better PDF viewing in mobile.  #463060
- Bad file names could potentially crash background file processing.  #467400
- Image upload fixed.  #468201
- Tooltips on mobile fixes. #463059
- Square brackets in markdown code blocks render incorrectly. #461762
- Tables now render correctly in markdown. #467632

## 20250227
FEATURES:
- Settings feature, to allow users to adjust creativity settings. #453134

ENHANCEMENTS:
- Redesign of the navigation system for improved user experience. #448352
- Dark mode option across the entire user interface. #448352
- Bumped Gotenberg to v8.17.1 #459149
- Bumped SKU for US prod instance to P1vm3 for increase memory. #459149
- Increased App Insights retention from 90 days to 180 days in production. #459149
- Toast notifications when actions are performed on UI. #459030

BUG FIXES:
- Password protected or files with invalid processing displays incorrect message. #456609
- Certain prompts don't return a response causes an unhandled exception. #457170
- Resolved issue where deployments bypassed the deployment step in production and required approval in other environments. #454445
- Addressed styling inconsistencies in markdown responses generated by the AI. #452418
- Fixed navigation issue where clicking the Gallagher Logo incorrectly refreshed the page instead of updating the flyout. #457202

## 20250130
FEATURES:
- Documents have a set expiration (6 months). If not used within this period, the documents are deleted in the system. #417240
- Documents up to 300 pages and 40MB in size will now be allowed. #446785

ENHANCEMENTS:
- Application updates will be announced on sidebar. #440424
- Fix browser warnings #445987
- CICD pipelines improvements. #446785
- Bump React libs to latest version #450439
- User adjustable chat window #449084

BUG FIXES:
- White screen is shown when screen size is too small #428420
- Routes with bad deep links causes chat history to break. #444722
- Disclaimer box works better in mobile view. #453554

## 20241212
FEATURES:
- Conversations limited to 50 prompts and responses #438533

ENHANCEMENTS:
- Better code blocks in dark/light mode #438532
- Deep links for certain urls #436580
- Close button more prominent in workspaces #438214
- Bump gotenberg to 8.14.1 #439109
- Bump Terraform providers, CLI, modules to latest versions
- Bump backend and frontend dependencies

BUG FIXES:
- Responsible AI exceptions handling #434585
- Document upload retry logic - Stop retrying when doc intelligence process fails and alerts the user #444076

## 20241121
FEATURES:
- Adding support for Word documents (.docx and .doc) #435962
- Adding support for PowerPoint documents (.pptx and .ppt) #435962
- Per user tracking of tokens per AI model #427736

ENHANCEMENTS:
- Document page limit increased to 200 pages #435495
- US region label re-worded to avoid bad translations #437609
- Added infrastructure to support document expiration #435152

BUG FIXES:
- Self-rendering via iframe bug #434881
- Document Upload Retry Logic - Add Limit to Backend Queue #435801

## 20241031
FEATURES:
- Drag and drop files to upload. #423210
- Chat with image. #404595

ENHANCEMENTS:
- Lowering SKUs for unused resources. #428966
- Added AI Translation service for future document translation service. #432368
- Gotenberg for file conversion to PDF. #431473
- MaxTokens increased to 4096 from 800. #434199
- Bumped front-end dependencies to latest versions. Bumped back-end dependencies to latest versions.

BUG FIXES:
- Front end XSS vulnerability resolution. #433861
- Back end dependency vulnerability resolution. #433861
- Messages roles adjustments to fix UI bug. #434039
- "No routes mathched location" console error. #432375
- Copy functionality not working. #432093
- Background queue stops processing when json hits read write contention. #432758

## 20241017
FEATURES:
- Wire up App Insights for front-end native to ReactJS. #428498

ENHANCEMENTS:
- Update Disclaimer #428499
- Dependecy updates, including to Semantic Kernel. #430064
- Chat history what was at one point tied to a work space, but workspace no longer exists. #428911

BUG FIXES:
- If page text exceeds 8196 tokens, file gets stuck in processing queue. #428845
- Citation link fails if token is expired. #423031

## 20241004
- Initial launch of the v2 of GallagherAI.