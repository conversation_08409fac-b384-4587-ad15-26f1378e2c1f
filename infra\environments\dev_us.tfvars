environment                 = "dev"
region                      = "us"
azurerm_resource_group_name = "corp-gallagherai-us-main-rg"

rbac_devadmin = ["d5e1daf2-a8bd-41db-931e-083ff2c9f977",     #u-ADO_AJG-CORP_GallagherGPT_Admins
  "9802beeb-ab97-4c8c-9085-956947408c87",                    #u-ADO_AJG-CORP_GallagherGPT_Contributors  
"eaf0c0be-7bec-4616-a4c1-f9ec069d15a1"]                      #<PERSON><PERSON>'s AVD's service principal (ameazus1ss-74e0)
rbac_contributors = ["ab69d54d-0ae3-40c5-9b6d-8d65da9a8d59"] #corp-openai-dev-gallaghergpt-cicd (Az Devops CICD Service Principal)

additional_storage_regions = ["Canada Central"]

appserviceplan_settings = {
  sku_name               = "P1v3"
  os_type                = "Linux"
  zone_balancing_enabled = false
}

webapp_settings = {
  application_insights_retention_in_days = 30
}

search_settings = {
  sku_name      = "standard"
  semantic_sku  = "standard"
  replica_count = 1
}

docintelligence_settings = {
  sku_name                   = "S0"
  kind                       = "FormRecognizer"
  dynamic_throttling_enabled = false
}

# Networking
nw_vnet_name               = "corp-dev-pvnet"
nw_rg_name                 = "network-rg"
nw_subnet_privatelink_name = "pe2"              # *************/26 (inbound)
nw_subnet_webapp_name      = "vnet-integration" # ************/28 (outbound)