## Metadata

variable "tenant_id" {
  description = "Tenant ID for the resources"
  type        = string
}

variable "subscription_id" {
  description = "Subscription ID for the resources"
  type        = string
}

variable "region_instance" {
  description = "Region for the resources. Eg. us, uk, au, etc."
  type        = string
}

variable "division_short" {
  description = "Short code for the division"
  type        = string
}

variable "environment" {
  description = "Environment for the resources"
  type        = string
}

variable "tags" {
  description = "Tags to be applied to the resources"
  type        = map(string)
}

variable "business_unit" {
  description = "Business Unit for the resources"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "azurerm_resource_group_name" {
  description = "Name of the Azure Resource Group"
  type        = string
}

variable "azurerm_resource_group_id" {
  description = "Id of the Azure Resource Group"
  type        = string
}

variable "azurerm_resource_group_location" {
  description = "Location of the Azure Resource Group"
  type        = string
}

variable "azurerm_log_analytics_workspace_id" {
  description = "ID of the Log Analytics Workspace"
  type        = string
}

## Networking

variable "azurerm_private_endpoint_subnet_id" {
  description = "ID of the subnet for the private endpoint"
  type        = string
}

variable "azurerm_delegated_subnet_id" {
  description = "ID of the subnet for the virtual network"
  type        = string
}

variable "outbound_dns_server" {
  type        = string
  description = "Primary outbound DNS server"
  default     = null
}

variable "outbound_alt_dns_server" {
  type        = string
  description = "Secondary outbound DNS server"
  default     = null
}

variable "search_settings" {
  type = object({
    sku_name      = string,
    semantic_sku  = string,
    replica_count = number
  })
  description = "Settings for Azure Search"
}

variable "docintelligence_settings" {
  type = object({
    sku_name                   = string,
    kind                       = string,
    dynamic_throttling_enabled = bool
  })
  description = "Settings for Azure Document Intelligence"
}

variable "doctranslate_settings" {
  type = object({
    sku_name = string,
    kind     = string
  })
  description = "Settings for Azure AI Translation"
  default = {
    sku_name = "S1"
    kind     = "TextTranslation"
  }
}

#variable "websearch_settings" {
#  type = object({
#    sku_name = string,
#    kind     = string
#  })
#  description = "Settings for Azure Web Search"
#  default = {
#    sku_name = "S1"
#    kind     = "Bing.Search.v7"
#  }
#}

variable "appserviceplan_settings" {
  type = object({
    sku_name               = string,
    os_type                = string
    zone_balancing_enabled = bool
  })
  description = "Settings for App Service Plan"
}

variable "webapp_settings" {
  type = object({
    application_insights_retention_in_days = number
  })
  description = "Settings for web apps"
}

variable "additional_storage_regions" {
  description = "Additional regions for data to be stored"
  type        = list(string)
}

variable "storageaccount_settings" {
  type = object({
    account_tier     = string,
    account_kind     = string
    replication_type = string
  })
  description = "Settings for Storage Account"
}

variable "search_dns_zone_id" {
  type        = string
  description = "ID of the private DNS zone"
}

variable "rbac_devadmin" {
  description = "List of devadmin object ids"
  type        = list(string)
}

variable "rbac_contributors" {
  description = "List of contributor object ids"
  type        = list(string)
}

variable "blob_dns_zone_id" {
  description = "ID of the private DNS zone for blob storage"
  type        = string
}

variable "static_app_dns_zone_id" {
  description = "ID of the private DNS zone for the static app"
  type        = string
}

variable "current_principal_id" {
  description = "The princpal_id of the current user (Terraform principal)"
  type        = string
}

variable "gotenberg_image_name" {
  description = "Name of the Gotenberg image"
  type        = string
}