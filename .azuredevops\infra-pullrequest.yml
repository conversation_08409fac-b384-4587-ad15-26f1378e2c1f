trigger:
 branches:
   exclude:
   - '*'

resources:
  repositories:
    - repository: iac-pipeline
      type: git
      name: GTS-InfrastructureAsCode-CenterOfExcellence\Pipeline-GenericInfrastructureDeployment
      ref: refs/heads/release
      endpoint: IaC-CoE-v2-Connection

variables:
- group: IaC-CenterOfExcellence-Integration-Variables-v2
- template: infra-variables.yml
- name: INFRACOST_LOG_LEVEL
  value: debug

extends:
  template: pullrequest-validation.yml@iac-pipeline
  parameters:
    repositoryName: GallagherAI
    terraformVersion: v1.11.1
    version: v3
    environmentsToBuild:
    - dev_us
    - dev_uk
    - dev_au
    - test_us
    - test_uk
    - test_au
    - prod_us
    - prod_uk
    - prod_au
    terraformSecretMapper:
      TF_TOKEN_app_terraform_io: $(READ_TFE_TOKEN)
