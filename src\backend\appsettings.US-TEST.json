{
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=9903d8b7-05a0-4de4-ba24-e1334f41e22f;IngestionEndpoint=https://eastus2-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus2.livediagnostics.monitor.azure.com/;ApplicationId=b0420076-27c5-4845-920c-8d74b005bdf4"
  },
  "AzureOpenAI": {
    "Endpoint": "https://aiutility-apim-test.ajgco.com"
  },
  "AzureStorage": {
    "Endpoints": {
      "Default": "https://corpdlstesti0js2v.blob.core.windows.net/",
      "CAN": "https://corpdlstestjivok9.blob.core.windows.net/"
    },
    "ExpirationPolicy": 30 //Days
  },
  "AzureDocIntel": {
    "Endpoint": "https://gallagherai-doc-int-fon2.cognitiveservices.azure.com/"
  },
  "AzureSearch": {
    "Endpoints": {
      "Default": "https://corp-gallagheraius-srch-test-m2jre.search.windows.net",
      "CAN": "https://corp-gallagheraius-srch-test-9fheb.search.windows.net"
    }
  },
  "AzureKeyVault": {
    "Endpoint": "https://corp-kv-test-yu5npk.vault.azure.net/"
  },
  "Conversion": {
    "Endpoint": "https://corp-aigo-app-ytqj-test.azurewebsites.net/forms/libreoffice/convert"
  },
  "Translation": {
    "Endpoint": "https://gallagherai-doc-translate-r6qa.cognitiveservices.azure.com/"
  }
}