interface Settings {
  top: number,
  semantic_ranker: boolean;
  retrieval_mode: string;
  suggest_followup_questions: boolean;
  workspace_id?: string;
  prompt_template?: string,
}

const chatSettings: Settings = {
  top: import.meta.env.VITE_TOP || 3,
  semantic_ranker: import.meta.env.VITE_SEMANTIC_RANKER === "true",
  retrieval_mode: import.meta.env.VITE_RETRIEVAL_MODE || "Text",
  suggest_followup_questions:
    import.meta.env.VITE_SUGGEST_FOLLOWUP_QUESTIONS === "true", 
};

export default chatSettings;
