﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for OpenAI Service
    /// </summary>
    public interface IOpenAIService
    {
        /// <summary>
        /// Prompts the AI and processes the response
        /// </summary>
        IAsyncEnumerable<Content> PromptAI(Chat request, CancellationToken ct, bool outputCitations = false);

        /// <summary>
        /// Prompt AI about documentation in the request workspace
        /// </summary>
        IAsyncEnumerable<Content> PromptAIAboutDocumentation(Chat request, CancellationToken ct);
    }
}
