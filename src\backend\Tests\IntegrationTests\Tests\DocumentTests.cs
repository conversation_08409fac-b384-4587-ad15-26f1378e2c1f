﻿//using Backend.Models;
//using Backend.Models.Context;
//using Xunit;

//namespace BackendIntegrationTests
//{
//    [CollectionDefinition("Document Tests")]
//    public class DocumentTestsCollection : ICollectionFixture<BackendIntegrationTests> { }

//    [Collection("Document Tests")]
//    public class DocumentsControllerTests
//    {
//        private readonly BackendIntegrationTests _fixture;
//        private Workspace _workspace1;

//        public DocumentsControllerTests(BackendIntegrationTests fixture)
//        {
//            _fixture = fixture;

//            // Preliminary data
//            _workspace1 = new Workspace 
//            { 
//                Name = "workspace1", 
//                Description = "description1", 
//                Expires = DateTime.UtcNow.AddDays(30).ToString("MM/dd/yyyy") 
//            };

//            CurrentContext.User = new User
//            {
//                OID = "userTest",
//                DisplayName = "userTest",
//                Images = false,
//                ChatCount = 0,
//                PDL = "NAM"
//            };

//            // Add data to the in-memory storage service
//            _workspace1 = _fixture.StorageService.CreateWorkspaceAsync("userTest", _workspace1).Result;
//        }

//        [Fact]
//        public async Task GET_Documents_ReturnsOkResult()
//        {
//            // Act
//            var response = await _fixture.Client.GetAsync($"/workspaces/{_workspace1.Id}/documents");

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();
//            Assert.Contains("documents", responseString);
//        }

//        [Fact]
//        public async Task DOWNLOAD_Document_ReturnsOkResult()
//        {
            
//        }

//        [Fact]
//        public async Task UPLOAD_Documents_ReturnsOkResult()
//        {
//            //Arrange
//            var file = "test.pdf";
//            using var fileStream = new FileStream(file, FileMode.Open, FileAccess.Read);
//            using var content = new StreamContent(fileStream);
//            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/pdf");
//            var formData = new MultipartFormDataContent();
//            formData.Add(content, "document", Path.GetFileName(file));

//            // Act
//            var response = await _fixture.Client.PostAsync($"/workspaces/{_workspace1.Id}/documents", formData);

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();
//            Assert.Contains("documents", responseString);

//        }

//        [Fact]
//        public async Task DELETE_Document_ReturnsOkResult()
//        {
//            // Act
//            var response = await _fixture.Client.DeleteAsync($"/workspaces/{_workspace1.Id}/documents/Test.pdf");

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var workspaces = await _fixture.StorageService
//                .GetWorkspaces("userTest", CancellationToken.None);
//            Assert.DoesNotContain(workspaces, w => w.Id == _workspace1.Id);
//        }

//        [Fact]
//        public async Task TRANSLATE_Documents_ReturnsOkResult()
//        {
            
//        }
//    }
//}
