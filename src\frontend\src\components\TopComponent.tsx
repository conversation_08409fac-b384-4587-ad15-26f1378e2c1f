import React from "react";
import { Outlet } from "react-router";

import { MsalAuthenticationTemplate } from "@azure/msal-react";
import { InteractionType } from "@azure/msal-browser";
import Header from "./Header/Header";

const TopComponent: React.FC = () => (
  <MsalAuthenticationTemplate
    interactionType={InteractionType.Redirect}
  >
    <div className="flex h-screen w-full flex-col">
      <Header />
      <div className="flex-1 pt-[56px]">
        <Outlet />
      </div>
    </div>
  </MsalAuthenticationTemplate>
  
);

export default TopComponent;
