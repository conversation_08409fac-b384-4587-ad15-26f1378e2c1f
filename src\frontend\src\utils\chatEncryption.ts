import { TransformedMessageProps } from "./types";

const apiBaseUrl = import.meta.env.VITE_API_URL;

export const chatEncryption = async (chatMessages: TransformedMessageProps[], email: string, token: string) => {
    const temp = JSON.stringify(chatMessages);
    try {
        const response = await fetch(`${apiBaseUrl}/encryption/encrypt`, {
            method: 'POST',
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + token,
            },
            body: JSON.stringify({ data: temp, email: email })
        });

        const text = await response.text();
        // console.log('Success:', text);
        return text;
    } catch (error) {
        console.error('Error:', error);
    }
};
