import { useEffect } from "react";
import { createBrowserHistory } from "history";
import { ApplicationInsights } from '@microsoft/applicationinsights-web';
import { ReactPlugin } from '@microsoft/applicationinsights-react-js';
import { RootState } from "../store";
import { useAppSelector } from "../app/hooks";

const connectionString = import.meta.env.VITE_MSAL_CONNECTION_STRING;
export const reactPlugin = new ReactPlugin();
const browserHistory = createBrowserHistory();

const appInsights = new ApplicationInsights({
    config: {
        connectionString: connectionString,
        extensions: [reactPlugin],
        extensionConfig: {
            [reactPlugin.identifier]: { history: browserHistory }
        }
    }
});

appInsights.loadAppInsights();

const useAppInsights = () => {
    // Use typed selector hook for better type safety
    const email = useAppSelector((state: RootState) => state.email.email);

    useEffect(() => {
        if (email) {
            appInsights.setAuthenticatedUserContext(email);
        }
        appInsights.trackPageView();

        return () => {
            appInsights.clearAuthenticatedUserContext();
        };
    }, [email]);
};

export default useAppInsights;
export { appInsights };