import { Workspace, DocumentRecord } from '../interfaces';

// API Error Types
export interface ApiError {
  status: string;
  data: {
    message: string;
    errors?: Record<string, string[]>;
  };
}

// Chat API Types
export interface ChatRequest {
  workspace_id: string | null;
  settings: object;
  messages: {
    role: string;
    content: {
      type: string;
      value: string | object;
    }[];
  }[];
}

export interface ChatResponse {
  role: string;
  content: {
    type: string;
    value: string | object;
  }[];
}

// Workspace API Types
export interface WorkspaceResponse extends Workspace {}

export interface WorkspaceListResponse extends Array<Workspace> {}

// Document API Types
export interface DocumentResponse {
  name: string;
  processed: 0 | 1 | 2;
  expires?: string;
}

export interface DocumentListResponse extends Array<DocumentRecord> {}

// Translation API Types
export interface TextTranslationRequest {
  text: string;
  language: string;
}

export interface TextTranslationResponse {
  translatedText: string;
}

export interface DocumentTranslationRequest {
  document: File;
  language: string;
}

export interface TranslatedDocumentResponse {
  id: string;
  name: string;
  language: string;
  originalName: string;
  translatedDate: string;
}

export interface TranslatedDocumentListResponse extends Array<TranslatedDocumentResponse> {}
