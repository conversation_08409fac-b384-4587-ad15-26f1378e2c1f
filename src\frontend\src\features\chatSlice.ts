import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatState, ChatMessageProps } from '../utils/types';

const initialState: ChatState = {
  messages: []
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<ChatMessageProps>) => {
      state.messages.push(action.payload);
    },
    updateLastMessage: (state, action: PayloadAction<{ user: string; index: number; text: string }>) => {
      const { user, index, text } = action.payload;
      if (state.messages[index] && state.messages[index].user === user) {
        state.messages[index].text += text;
      }
    },
    clearMessages: (state) => {
      state.messages = [];
    } 
  }
});

export const { addMessage, updateLastMessage, clearMessages } = chatSlice.actions;
export default chatSlice.reducer;
