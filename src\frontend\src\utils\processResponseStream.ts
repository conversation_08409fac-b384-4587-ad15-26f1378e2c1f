const processBuffer = async (buffer: string, onData: (data: any) => void) => {
    let start = 0;
    let openBrackets = 0;
    let closeBrackets = 0;
  
    for (let i = 0; i < buffer.length; i++) {
      if (buffer[i] === '{') openBrackets++;
      if (buffer[i] === '}') closeBrackets++;
  
      if (openBrackets > 0 && openBrackets === closeBrackets) {
        let part = buffer.slice(start, i + 1);
        start = i + 1;
  
        if (part.startsWith('[') || part.startsWith(',')) {
          part = part.slice(1);
        }
  
        if (part.trim() === ']') {
          continue;
        }
  
        try {
          const data = JSON.parse(part);
          onData(data);
        } catch (error) {
          console.error("Error parsing JSON:", error);
        }
      }
    }
  
    return buffer.slice(start);
  };
  
  export const processResponseStream = async (stream: ReadableStream<Uint8Array>, onData: (data: any) => void) => {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
  
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
  
      buffer += decoder.decode(value, { stream: true });
      buffer = await processBuffer(buffer, onData);
    }
  };