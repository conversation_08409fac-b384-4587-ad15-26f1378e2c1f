import { deleteData, getAllData } from '../db/chatDB';
import { Stores } from '../constants/dbConstants';
const apiBaseUrl = import.meta.env.VITE_API_URL;

export const chatDecryption = async (hash: string, email: string, token: string) => {
    try {
        const response = await fetch(`${apiBaseUrl}/encryption/decrypt`, {
            method: 'POST',
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + token,
            },
            body: JSON.stringify({ data: hash, email: email })
        });

        const text = await response.text();
        return JSON.parse(text);
    } catch (error) {
        //console.error('Error:', error);

        // Decryption failed, delete the row from IndexedDB
        try {
            const allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);
            const rowToDelete = allDbData.find(item => item.hash === hash);
            if (rowToDelete) {
                await deleteData(Stores.Users, rowToDelete.id);
                //console.log(`Deleted row with id: ${rowToDelete.id}`);
            }
        } catch (dbError) {
            //console.error('Error during deletion from IndexedDB:', dbError);
        }
        return null; // Return null if decryption fails
    }
};
