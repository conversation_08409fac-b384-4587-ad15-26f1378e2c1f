import React, { useState } from "react";
import TextareaAutosize from "react-textarea-autosize";
import { makeAlphanumeric } from "../../../utils/encryptionHandler";
import { saveCustomTemplate } from "../../../db/settingDB";
import { useNavigate } from "react-router";
import { CheckmarkCircle24Filled } from "@fluentui/react-icons";
import useToast from "../../../hooks/useToast";
import Toast from "../../Modal/ToastModal/Toast";

const AddCustomTemplate: React.FC = () => {
    const navigate = useNavigate();
    const [inputValue, setInputValue] = useState("");
    const [textAreaValue, setTextAreaValue] = useState("");
    
  const { toasts, triggerToast, closeToast } = useToast();
    const handleSave = async () => {
        const newObj = {
        id: makeAlphanumeric(Date.now().toString()),
        label: inputValue,
        description: textAreaValue,
        template: "custom",
        default: false,
        };
        await saveCustomTemplate(newObj);
        triggerToast({
            text: "A new assistant instructions template was added",
            icon: <CheckmarkCircle24Filled />,
            duration: 1,
            position: "bottom-center",
        });
        setInputValue("");
        setTextAreaValue("");
        navigate("/settings");
    };
    return (
      <div className="relative flex flex-col p-8 items-center h-full w-full bg-white dark:bg-zinc-800">
        <div className="flex flex-col h-full p-4 mb-6 space-y-4 overflow-y-auto w-full max-w-(--breakpoint-lg) md:w-1/2 xl:w-1/3 pb-[56px]">
          <h2 className="text-xl font-semibold text-left dark:text-white">
            Add Assistant Instructions Setting
          </h2>
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            className="w-full mt-2 p-2 border-2 rounded dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300"
            placeholder="Enter the template name"
            maxLength={256}
          />
          <TextareaAutosize
            minRows={5}
            maxLength={3000}
            maxRows={10}
            className={`w-full h-24 mt-2 mb-1 p-2 border-2 rounded dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300`}
            aria-label="Enter the template description"
            placeholder="Enter the template description"
            value={textAreaValue}
            onChange={(e) => setTextAreaValue(e.target.value)}
          />
          <div
            className={`text-sm text-right ${
              textAreaValue.length > 2950
                ? "text-red-500"
                : "text-gray-500 dark:text-gray-400"
            }`}
          >
            {textAreaValue.length} / 3000 characters
          </div>
          <div className="flex space-x-4 w-full">
            <button
              onClick={() => navigate("/settings")}
              className="w-1/2 cursor-pointer px-4 py-2 border-2 rounded-sm dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 hover:text-white dark:hover:text-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300"
            >
              Back
            </button>
            <button
              onClick={handleSave}
              className={`w-1/2 px-4 py-2 border-2 rounded-sm dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 hover:text-white dark:hover:text-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 ${
                !inputValue ? "opacity-70 cursor-not-allowed" : "cursor-pointer"
              }`}
              disabled={!inputValue}
              aria-label="Save custom template"
            >
              Save
            </button>
          </div>
        </div>
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            id={toast.id}
            position={toast.position}
            text={toast.text}
            icon={toast.icon}
            duration={toast.duration}
            onClose={() => closeToast(toast.id)}
            bgColor={toast.bgColor}
          />
        ))}
      </div>
    );
};

export default AddCustomTemplate;


