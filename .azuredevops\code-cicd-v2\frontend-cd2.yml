name: Frontend-CD-v2

trigger: none

pool:
  vmImage: 'ubuntu-latest'

variables:
  - template: variables.yml
  - name: SelectedBranch
    value: $(Build.SourceBranch)

parameters:
  - name: environment
    displayName: 'Environment to deploy'
    type: string
    default: 'DEV'
    values:
      - DEV
      - TEST
      - PROD

  - name: regions
    displayName: 'Regions to deploy'
    type: object
    default:
      - US
      - UK
      - AU

stages:
  - stage: Build
    jobs:
      - job: BuildAndPublishJob
        displayName: 'Build and Publish Frontend'
        steps:
          - script: |
              corepack enable
              corepack prepare pnpm@9.12.1 --activate
            displayName: 'Install pnpm'

          - script: |
              cd $(Build.SourcesDirectory)/src/frontend
              pnpm install --no-frozen-lockfile
            displayName: 'Install Node Dependencies with pnpm'

          - script: |
              cd $(Build.SourcesDirectory)/src/frontend
              pnpm run build
            displayName: 'Build Frontend with pnpm'

          - task: ArchiveFiles@2
            displayName: 'Archive dist folder'
            inputs:
              rootFolderOrFile: '$(Build.SourcesDirectory)/src/frontend/dist'
              includeRootFolder: false
              archiveFile: '$(Build.ArtifactStagingDirectory)/frontend.zip'
              replaceExistingArchive: false

          - template: /.azuredevops/snyk/snyk-sast-sca-ubuntu.yml

          - task: PublishBuildArtifacts@1
            displayName: 'Publish artifacts'
            inputs:
              pathtoPublish: '$(Build.ArtifactStagingDirectory)/frontend.zip'
              artifactName: 'frontend'

  - stage: Deploy
    dependsOn: Build
    variables:
      environmentVar: ${{ parameters.environment }}
    jobs:
      # Approval job for PROD
      - job: Approval
        displayName: 'Deployment Approval'
        condition: eq(variables['environmentVar'], 'PROD')
        pool: server
        steps:
          - task: ManualValidation@0
            inputs:
              notifyUsers: ''
              instructions: 'Review the deployment and approve or deny.'

      # Deployment job for all regions (dependsOn Approval for PROD only)
      - ${{ each region in parameters.regions }}:
          - ${{ if eq(parameters.environment, 'PROD') }}:
              - template: frontend-deploy-job.yml
                parameters:
                  environment: ${{ format('{0}_{1}', region, parameters.environment) }}
                  api_url: ${{ variables[format('{0}_{1}_API_URL', region, parameters.environment)] }}
                  scopes: ${{ variables[format('{0}_{1}_Scopes', region, parameters.environment)] }}
                  client_id: ${{ variables[format('{0}_{1}_Client_ID', region, parameters.environment)] }}
                  authority: ${{ variables[format('{0}_{1}_Authority', region, parameters.environment)] }}
                  region: ${{ variables[format('{0}_{1}_Region', region, parameters.environment)] }}
                  connectionString: ${{ variables[format('{0}_{1}_ConnectionString', region, parameters.environment)] }}
                  service_connection: ${{ variables[format('{0}_{1}_ServiceConnection', region, parameters.environment)] }}
                  deploy_agent_pool: ${{ variables[format('{0}_{1}_DeployAgentPool', region, parameters.environment)] }}
                  app_name: ${{ variables[format('{0}_{1}_feAppName', region, parameters.environment)] }}
                  dependsOn: Approval

          - ${{ if ne(parameters.environment, 'PROD') }}:
              - template: frontend-deploy-job.yml
                parameters:
                  environment: ${{ format('{0}_{1}', region, parameters.environment) }}
                  api_url: ${{ variables[format('{0}_{1}_API_URL', region, parameters.environment)] }}
                  scopes: ${{ variables[format('{0}_{1}_Scopes', region, parameters.environment)] }}
                  client_id: ${{ variables[format('{0}_{1}_Client_ID', region, parameters.environment)] }}
                  authority: ${{ variables[format('{0}_{1}_Authority', region, parameters.environment)] }}
                  region: ${{ variables[format('{0}_{1}_Region', region, parameters.environment)] }}
                  connectionString: ${{ variables[format('{0}_{1}_ConnectionString', region, parameters.environment)] }}
                  service_connection: ${{ variables[format('{0}_{1}_ServiceConnection', region, parameters.environment)] }}
                  deploy_agent_pool: ${{ variables[format('{0}_{1}_DeployAgentPool', region, parameters.environment)] }}
                  app_name: ${{ variables[format('{0}_{1}_feAppName', region, parameters.environment)] }}
