################################################################
# This is only for static strings that are not sensitive
# Need to add the variable map to the terraformSecretMapper
# parameter in the template call if a sensitive value is passed
################################################################
variables:
  # You can inject pipeline wide variables here

  # You can also inject environment specific variables
  # The reserved variable name is: AJG_IAC_{environment name}
  # Examples:
  # AJG_IAC_dev_Target: Somevalue
  # AJG_IAC_test_Target: Sometestvalue

  # If you are using pretty terraform plans, you need to set the AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_{environment} variable in order to inject the proper environment into the plan
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_dev_us: IaC-Azure-GallagherAI-dev_us-v2
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_test_us: IaC-Azure-GallagherAI-test_us-v2
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_prod_us: IaC-Azure-GallagherAI-prod_us-v2  
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_dev_uk: IaC-Azure-GallagherAI-dev_uk-v2
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_test_uk: IaC-Azure-GallagherAI-test_uk-v2
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_prod_uk: IaC-Azure-GallagherAI-prod_uk-v2  
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_dev_au: IaC-Azure-GallagherAI-dev_au-v2
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_test_au: IaC-Azure-GallagherAI-test_au-v2
  AJG_IAC_TERRAFORMPLAN_SERVICE_CONNECTION_prod_au: IaC-Azure-GallagherAI-prod_au-v2

  ## All injected service connections must have a naming convention
  ## AJG_IaC_{name of connection}_{environment}
  ## Examples
  ## AJG_IaC_AzureConnection_dev
  ## AJG_IaC_Aws_dev
  ## This is only required if you are not using "pretty" or "simple" terraform plans
  ## Examples
  ## AJG_IaC_AzureConnection_dev: IaC-Default-GallagherAI-dev
  ## AJG_IaC_AzureConnection_test: IaC-Default-GallagherAI-test
  ## AJG_IaC_AzureConnection_prod: IaC-Default-GallagherAI-prod

  AJG_POOL_NAME_dev_us: IaC-AzurePool-CORPUSDEV-v2
  AJG_POOL_NAME_test_us: IaC-AzurePool-CORPUSTEST-v2
  AJG_POOL_NAME_prod_us: IaC-AzurePool-CORPUSPROD-v2
  AJG_POOL_NAME_dev_uk: IaC-AzurePool-CORPUKDEV-v2
  AJG_POOL_NAME_test_uk: IaC-AzurePool-CORPUKTEST-v2
  AJG_POOL_NAME_prod_uk: IaC-AzurePool-CORPUKPROD-v2
  AJG_POOL_NAME_dev_au: IaC-AzurePool-CORPAUDEV-v2
  AJG_POOL_NAME_test_au: IaC-AzurePool-CORPAUTEST-v2
  AJG_POOL_NAME_prod_au: IaC-AzurePool-CORPAUPROD-v2
