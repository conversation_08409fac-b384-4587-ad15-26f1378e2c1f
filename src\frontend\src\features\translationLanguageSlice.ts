import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TranslationLanguageState } from "../interfaces";


const initialState: TranslationLanguageState={
    selectedLanguage:"",
}

const translationLanguageSelection = createSlice({
  name: "translationLanguage",
  initialState,
  reducers: {
    setSelectedLanguage: (state, action: PayloadAction<string>) => {
      state.selectedLanguage = action.payload;
    },
  },
});

export const { setSelectedLanguage } = translationLanguageSelection.actions;

export default translationLanguageSelection.reducer;
