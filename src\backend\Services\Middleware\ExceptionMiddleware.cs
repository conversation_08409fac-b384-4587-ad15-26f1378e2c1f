﻿using Backend.Extensions;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace Backend.Services
{
    public class ExceptionMiddleware : ControllerBase
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ExceptionMiddleware> _logger;

        private static readonly Dictionary<Type, int> ExceptionStatusCodeMap = new()
        {
            { typeof(BadHttpRequestException), StatusCodes.Status400BadRequest },
            { typeof(UnauthorizedAccessException), StatusCodes.Status401Unauthorized },
            { typeof(OperationCanceledException), StatusCodes.Status408RequestTimeout },
            { typeof(TaskCanceledException), StatusCodes.Status408RequestTimeout }
            // Add other exception types and their corresponding status codes here
        };

        public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        /// <summary>
        /// Invokes the exception handling middleware
        /// </summary>
        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                if (!context.Response.HasStarted)
                {
                    if (ExceptionStatusCodeMap.TryGetValue(ex.GetType(), out int statusCode))
                    {
                        context.Response.StatusCode = statusCode;
                    }
                    else if (ex is Azure.RequestFailedException requestFailedException)
                    {
                        context.Response.StatusCode = requestFailedException.Status;
                    }
                    else
                    {
                        context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                    }
                }

                string message = ex.Message;
                if (ex is PdfSharp.Pdf.IO.PdfReaderException)
                {
                    message = $"'{StringExtensions.Sanitize(context.Request.Form.Files[0].FileName)}' is invalid. {message}";
                } else if (ex.Source != null && ex.Source.Contains("PdfSharp")) {
                    message = $"'{StringExtensions.Sanitize(context.Request.Form.Files[0].FileName)}' is invalid.";
                    if (context.Request.Form.Files[0].FileName.EndsWith("pdf")) {
                        message = message + " It may help to open it in Adobe Reader, save it as a new PDF, and then trying again.";
                    }
                }
                else if (ex.InnerException != null && ex.InnerException is Microsoft.SemanticKernel.HttpOperationException httpOperationException)
                {
                    string responseContent = httpOperationException.ResponseContent!;
                    if (responseContent.Contains("ResponsibleAIPolicyViolation"))
                    {
                        var innerError = JsonNode.Parse(responseContent);
                        var categories = new List<string>();

                        if (innerError?["error"]?["innererror"]?["content_filter_result"] is JsonObject contentFilterResult)
                        {
                            foreach (var category in contentFilterResult)
                            {
                                if (category.Value?["filtered"]?.GetValue<bool>() == true)
                                {
                                    categories.Add(category.Key);
                                }
                            }
                        }

                        message = $"Your prompt has been flagged as a Responsible AI Policy Violation in the category of: " +
                                  $"{string.Join(", ", categories)}. Please modify your prompt and retry.";
                    }
                }

                var errorDetails = JsonSerializer.Serialize(new
                {
                    ExceptionType = ex.GetType().FullName,
                    Message = message
                });

                await context.Response.WriteAsync(errorDetails);
                await context.Response.Body.FlushAsync();

                if (ex is not TaskCanceledException and not OperationCanceledException)
                {
                    var logMessage = $@"
                        An unhandled exception occurred:
                        Exception Type: {ex.GetType().FullName}
                        Message: {ex.Message}
                        Stack Trace: {ex.StackTrace}
                        Inner Exception: {ex.InnerException?.Message}
                        Inner Exception Stack Trace: {ex.InnerException?.StackTrace}
                        User: {StringExtensions.Sanitize(context?.User?.Identity?.Name) ?? "Anonymous"}
                    ";

                    _logger.LogError(logMessage);
                }
            }
        }
    }
}
