﻿//using Backend.Models;
//using Xunit;

//namespace BackendIntegrationTests
//{
//    [CollectionDefinition("Encryption Tests")]
//    public class EncryptionTestsCollection : ICollectionFixture<BackendIntegrationTests> { }

//    [Collection("Encryption Tests")]
//    public class EncryptionControllerTests
//    {
//        private readonly BackendIntegrationTests _fixture;
//        private Workspace _workspace1;

//        public EncryptionControllerTests(BackendIntegrationTests fixture)
//        {
//            _fixture = fixture;

//            // Preliminary data
//            _workspace1 = new Workspace
//            {
//                Name = "workspace1",
//                Description = "description1",
//                Expires = DateTime.UtcNow.AddDays(30).ToString("MM/dd/yyyy")
//            };

//            // Add data to the in-memory storage service
//            _workspace1 = _fixture.StorageService.CreateWorkspaceAsync("userTest", _workspace1).Result;
//        }

//        [Fact]
//        public async Task ENCRYPT_ReturnsOkResult()
//        {
//            // Act
//            var response = await _fixture.Client.GetAsync($"/workspaces/{_workspace1.Id}/documents");

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();
//            Assert.Contains("documents", responseString);
//        }

//        [Fact]
//        public async Task DECRYPT_ReturnsOkResult()
//        {

//        }
//    }
//}
