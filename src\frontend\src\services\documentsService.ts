const apiBaseUrl = import.meta.env.VITE_API_URL;

// Upload a single file to a workspace
export const uploadDocument = async (workspaceId: string, file: File, token: string, signal: AbortSignal): Promise<ReadableStream<Uint8Array>> => {
    const formData = new FormData();
    formData.append("document", file);

    const response = await fetch(`${apiBaseUrl}/workspaces/${workspaceId}/documents`, {
        method: "POST",
        headers: {
            "Authorization": `Bearer ${token}`
        },
        body: formData,
    });

    if (!response.ok) {
        // If the response is not OK, try to extract the error message
        const errorText = await response.text();
        throw new Error(errorText);
    }

    const reader = response.body?.getReader();
    const stream = new ReadableStream({
        start(controller) {
            function push() {
                reader?.read().then(({ done, value }) => {
                    if (done) {
                        controller.close();
                        return;
                    }
                    if (signal.aborted) {
                        return;
                    }
                    controller.enqueue(value);
                    push();
                }).catch((err) => {
                    controller.error(err);
                });
            }
            push();
        },
        cancel() {
            reader?.cancel();
        },
    });

    return stream;
};

// Get the documents in a workspace
export const fetchDocuments = async (workspaceId: string, token: string, signal: AbortSignal): Promise<ReadableStream<Uint8Array>> => {
    const response = await fetch(`${apiBaseUrl}/workspaces/${workspaceId}/documents`, {
        method: "GET",
        headers: {
            "Authorization": `Bearer ${token}`,
            "Content-Type": "application/json",
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    const stream = new ReadableStream({
        start(controller) {
            function push() {
                reader?.read().then(({ done, value }) => {
                    if (done) {
                        controller.close();
                        return;
                    }
                    if (signal.aborted) {
                        return;
                    }
                    controller.enqueue(value);
                    push();
                }).catch(err => {
                    controller.error(err);
                });
            }
            push();
        },
        cancel() {
            reader?.cancel();
        }
    });

    return stream;
};

// Download a document
export const downloadDocument = async (workspaceId: string, documentId: string, token: string) => {
    const response = await fetch(`${apiBaseUrl}/workspaces/${workspaceId}/documents/${documentId}`, {
        method: "GET",
        headers: {
            "Authorization": `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
    }

    return response.blob();
};

// Delete a document
export const deleteDocument = async (workspaceId: string, documentId: string, token: string) => {
    const response = await fetch(`${apiBaseUrl}/workspaces/${workspaceId}/documents/${documentId}`, {
        method: "DELETE",
        headers: {
            "Authorization": `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to delete document: ${response.statusText}`);
    }
};
