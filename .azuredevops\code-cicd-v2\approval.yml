# approval.yml

parameters:
  - name: environment

jobs:
  - job: approval
    condition: succeeded()
    displayName: 'GallagherAI Deployment Approval for ${{ parameters.environment }}'
    pool: server
    steps:
      - task: ManualValidation@0
        inputs:
          notifyUsers: '' #u-AZURE_ AJG-CORP_OpenAIGlobalGPT_Contributors_Development
          instructions: 'Review the deployment and approve or deny.'