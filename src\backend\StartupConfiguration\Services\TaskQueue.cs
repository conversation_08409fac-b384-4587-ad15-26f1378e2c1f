﻿using Backend.Models;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureTaskQueue(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<ITaskQueue, TaskQueue>(sp =>
            {
                return new TaskQueue(config.GetValue<int>("Background:QueueSettings:Workers"),
                                     config["Background:QueueSettings:BasePath"]!);
            });
        }
    }
}
