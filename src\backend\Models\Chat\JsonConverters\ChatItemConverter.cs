﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Deserializes / serializes the value of a <see cref="ChatItem"/>.
    /// </summary>
    public class ChatItemConverter : JsonConverter<ChatItem>
    {
        public override ChatItem Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var jsonDoc = JsonDocument.ParseValue(ref reader);
            var jsonObject = jsonDoc.RootElement;

            var chatItem = new ChatItem
            {
                RoleString = jsonObject.GetProperty("role").ToString(),
                Content = new List<Content>()
            };

            foreach (var contentElement in jsonObject.GetProperty("content").EnumerateArray())
            {
                chatItem.Content.Add(JsonSerializer.Deserialize<Content>(contentElement.GetRawText(), options)!);
            }

            return chatItem;
        }

        public override void Write(Utf8JsonWriter writer, ChatItem value, JsonSerializerOptions options)
        {
            JsonSerializer.Serialize(writer, value, options);
        }
    }
}