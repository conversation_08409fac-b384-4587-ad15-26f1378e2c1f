import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ChatHistoryOpenState } from "../interfaces";

const initialState: ChatHistoryOpenState = {
  isOpen: false,
};

const chatHistoryOpenSlice = createSlice({
  name: "chatHistoryOpen",
  initialState,
  reducers: {
    setChatHistoryOpen: (state, action: PayloadAction<boolean>) => {
      state.isOpen = action.payload;
    },
  },
});

export const { setChatHistoryOpen } = chatHistoryOpenSlice.actions;

export default chatHistoryOpenSlice.reducer;