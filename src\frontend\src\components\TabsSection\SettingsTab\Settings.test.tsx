import { describe, it, beforeEach, expect, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import SettingsTab from "./SettingsTab";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import settingsReducer from "../../../features/settingsSlice";
import 'fake-indexeddb/auto';
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY, } from "../../../constants/SettingsTabConstants";
import { initUserSettingDB } from "../../../db/settingDB";

vi.mock('@microsoft/applicationinsights-web', () => {
  return {
    ApplicationInsights: vi.fn().mockImplementation(() => ({
      loadAppInsights: vi.fn(),
      trackPageView: vi.fn(),
      trackEvent: vi.fn(),
      trackException: vi.fn(),
      context: { user: {} },
    })),
  };
});

const createMockStore = () =>
  configureStore({
    reducer: {
      settings: settingsReducer,
    },
  });

describe("SettingsTab", () => {
  let mockStore: ReturnType<typeof createMockStore>;

  beforeEach(async () => {
    mockStore = createMockStore();

    indexedDB.deleteDatabase("Settings");
    await initUserSettingDB();
  });

  it("falls back to default settings when no other valid setting exists", async () => {
    render(
      <Provider store={mockStore}>
        <SettingsTab />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getAllByDisplayValue(TEMPERATURE.defaultValue.toString())).toHaveLength(1);
      expect(screen.getAllByDisplayValue(TOP_P.defaultValue.toString())).toHaveLength(1);
      expect(screen.getAllByDisplayValue(FREQUENCY_PENALTY.defaultValue.toString())).toHaveLength(2);
      expect(screen.getAllByDisplayValue(PRESENCE_PENALTY.defaultValue.toString())).toHaveLength(2);
    });
  });
});