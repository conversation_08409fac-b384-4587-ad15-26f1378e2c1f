﻿using Backend.Models;
using Backend.Services;
using Microsoft.SemanticKernel;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureOpenAIService(this IServiceCollection services)
        {
            services.AddSingleton<IOpenAIService, OpenAIService>(sp =>
            {
                return new OpenAIService(
                    sp.GetRequiredService<Kernel>(),
                    sp.GetRequiredService<ISearchService>());
            });
        }
    }
}
