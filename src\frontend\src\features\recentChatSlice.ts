import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RecentChatState } from "../interfaces";

const initialState: RecentChatState[] = [];

const recentChatSlice = createSlice({
  name: "recentChat",
  initialState,
  reducers: {
    setRecentChat: (state, action: PayloadAction<{ id: string, text: string }>) => {
      const { id, text } = action.payload;
      const cleanedText = text.split('\n<')[0];
      state.push({ id, text: cleanedText.trim() });
    },
    clearRecentChat: () => {
      return [];
    }
  },
});

export const { setRecentChat, clearRecentChat } = recentChatSlice.actions;

export default recentChatSlice.reducer;
