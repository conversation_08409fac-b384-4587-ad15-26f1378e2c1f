# State Management Guide

## Overview

This guide covers the Redux Toolkit (RTK) and RTK Query implementation used in the GallagherAI application for state management and API handling.

## Key Features

### Redux Toolkit Integration
- Simplified store configuration with Redux Toolkit
- RTK Query for automatic API state management
- Typed hooks for better TypeScript integration

### Performance Features
- **Automatic Caching**: RTK Query provides intelligent response caching
- **Background Refetching**: Automatic data synchronization
- **Optimistic Updates**: Immediate UI updates with rollback on failure
- **Reduced Re-renders**: Optimized component updates through better state management

### Type Safety
- **Typed Redux Hooks**: `useAppDispatch` and `useAppSelector` for type-safe state access
- **API Type Definitions**: Comprehensive TypeScript interfaces for all API responses
- **Error Type Safety**: Structured error handling with typed error states

### Developer Experience
- **Automatic Loading States**: Built-in loading indicators for all API calls
- **Error Handling**: Centralized error management with automatic retry logic
- **Code Organization**: Modular API services with clear separation of concerns

## Architecture Overview

### Store Configuration

The Redux store is configured with RTK Query integration:

```typescript
const store = configureStore({
  reducer: {
    [api.reducerPath]: api.reducer,
    // Application reducers
    pdfViewer: PdfViewerReducer,
    chat: chatReducer,
    currentChatId: currentChatIdReducer,
    // ... other reducers
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(api.middleware),
});
```

### API Service Architecture

The API layer is organized into modular services:

```
src/frontend/src/
├── app/
│   ├── api.ts          # Base RTK Query configuration
│   └── hooks.ts        # Typed Redux hooks
├── services/api/
│   ├── chatApi.ts      # Chat API endpoints
│   ├── documentsApi.ts # Document management
│   ├── settingsApi.ts  # Settings (IndexedDB)
│   ├── translationApi.ts # Translation services
│   └── workspacesApi.ts # Workspace management
└── types/
    └── apiTypes.ts     # API type definitions
```

#### Base API Configuration (`app/api.ts`)
- **Authentication Integration**: Automatic token injection and refresh
- **Error Handling**: 401 error interception with token refresh
- **Cache Tags**: Organized cache invalidation system
- **Base Query**: Custom fetch configuration with retry logic

### Authentication

#### `useApiWithAuth` Hook
- **Automatic Token Management**: Handles token refresh automatically
- **Redux Integration**: Updates token state in Redux store
- **Component Integration**: Easy authentication state checking

```typescript
const { isAuthenticated, refreshToken } = useApiWithAuth();
```

## Usage Examples

### Basic Query Usage

```typescript
import { useGetWorkspacesQuery } from '../services/api/workspacesApi';
import useApiWithAuth from '../hooks/useApiWithAuth';

const WorkspacesList = () => {
  const { isAuthenticated } = useApiWithAuth();
  const { data: workspaces = [], isLoading, error } = useGetWorkspacesQuery(undefined, {
    skip: !isAuthenticated
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading workspaces</div>;

  return (
    <div>
      {workspaces.map(workspace => (
        <div key={workspace.id}>{workspace.name}</div>
      ))}
    </div>
  );
};
```

### Mutation Usage

```typescript
import { useCreateWorkspaceMutation } from '../services/api/workspacesApi';

const CreateWorkspace = () => {
  const [createWorkspace, { isLoading }] = useCreateWorkspaceMutation();

  const handleCreate = async () => {
    try {
      await createWorkspace({ name: 'New Workspace' }).unwrap();
      // Cache automatically updates
    } catch (error) {
      console.error('Failed to create workspace:', error);
    }
  };

  return (
    <button onClick={handleCreate} disabled={isLoading}>
      {isLoading ? 'Creating...' : 'Create Workspace'}
    </button>
  );
};
```

### Redux State Management

```typescript
import { useAppSelector, useAppDispatch } from '../app/hooks';
import { setCurrentChatId } from '../features/currentChatIdSlice';

const ChatComponent = () => {
  const dispatch = useAppDispatch();
  const currentChatId = useAppSelector(state => state.currentChatId.value);

  const handleChatSelect = (chatId: string) => {
    dispatch(setCurrentChatId(chatId));
  };

  return (
    <div>Current Chat: {currentChatId}</div>
  );
};
```

## RTK Query Features

### Automatic Caching
```typescript
// Data is cached and automatically reused
const { data: workspaces, isLoading } = useGetWorkspacesQuery();
```

### Cache Invalidation
```typescript
// Mutations automatically invalidate related cache
deleteWorkspace: builder.mutation({
  invalidatesTags: ['Workspaces'],
});
```

### Background Refetching
```typescript
// Automatic refetch on component mount
useGetWorkspacesQuery(undefined, {
  refetchOnMountOrArgChange: true
});
```

### Optimistic Updates
```typescript
// UI updates immediately, rolls back on error
const [updateWorkspace] = useUpdateWorkspaceMutation();
```

## Authentication Flow

### Token Management
1. **Initial Authentication**: `useApiWithAuth` hook manages token acquisition
2. **Automatic Refresh**: Base query intercepts 401 errors and refreshes tokens
3. **Redux Integration**: Token state synchronized across the application
4. **Component Integration**: Easy authentication checks in components

### Error Handling
- **Centralized Error States**: RTK Query provides consistent error handling
- **Automatic Retry**: Failed requests automatically retry with refreshed tokens
- **User Feedback**: Toast notifications for error states

## Performance Features

### Intelligent Caching
- Prevents duplicate requests
- Background updates keep data fresh
- Selective refetching only when necessary

### Component Optimization
- Typed selectors prevent unnecessary re-renders
- Memoized hooks with optimized dependencies
- Granular state updates

### Bundle Optimization
- Tree shaking includes only used features
- API services can be lazy-loaded
- Compile-time type checking

## Testing Considerations

### Unit Testing
- Mock RTK Query hooks in component tests
- Test Redux state changes with RTK Query actions
- Verify cache invalidation behavior

### Integration Testing
- Test API error scenarios with token refresh
- Verify cache behavior across component interactions
- Test optimistic updates and rollback scenarios

### Performance Testing
- Measure cache hit rates
- Monitor bundle size impact
- Test with large datasets

## Best Practices

### Component Development
1. **Always use typed hooks**: `useAppDispatch` and `useAppSelector`
2. **Use authentication hook**: Include `useApiWithAuth` in components making API calls
3. **Handle loading states**: Leverage RTK Query's automatic loading states
4. **Implement error handling**: Use RTK Query's structured error responses

### API Integration
1. **Use RTK Query hooks**: Replace manual fetch calls with generated hooks
2. **Implement cache invalidation**: Use proper tags for cache management
3. **Handle optimistic updates**: Implement immediate UI feedback where appropriate
4. **Follow service patterns**: Maintain consistency across API services

### Performance Optimization
1. **Conditional queries**: Use `skip` option when data isn't needed
2. **Selective subscriptions**: Only subscribe to required state slices
3. **Implement prefetching**: Load anticipated data in advance
4. **Monitor cache usage**: Ensure efficient cache strategies

## Troubleshooting

### Common Issues

1. **Authentication Errors (401)**
   - Ensure `useApiWithAuth` is used in components making API calls
   - Check token expiration and refresh logic

2. **Cache Not Updating**
   - Verify proper tag invalidation in mutations
   - Check if cache tags are correctly defined

3. **TypeScript Errors**
   - Ensure API types match backend response structure
   - Update type definitions when backend changes

4. **Performance Issues**
   - Use `skip` option for conditional queries
   - Implement proper cache invalidation strategies
   - Consider using `selectFromResult` for data transformation

### Debug Tools

1. **Redux DevTools**: Monitor RTK Query actions and state
2. **Network Tab**: Inspect actual API calls and responses
3. **RTK Query DevTools**: Use the built-in query inspector

## Resources

- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)
- [RTK Query Documentation](https://redux-toolkit.js.org/rtk-query/overview)
- [TypeScript with RTK Query](https://redux-toolkit.js.org/rtk-query/usage/typescript)
- [Project API Documentation](../src/services/api/README.md)
