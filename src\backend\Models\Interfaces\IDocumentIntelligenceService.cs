﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Document Intelligence Service
    /// </summary>
    public interface IDocumentIntelligenceService
    {
        /// <summary>
        /// Get the text from the given document stream.
        /// </summary>
        Task<string> GetDocumentTextAsync(MemoryStream stream);

        /// <summary>
        /// Creates a list of sections from the textual content.
        /// </summary>
        IEnumerable<Section> CreateSections(string content,
                                            string oid,
                                            string workspaceId,
                                            string blob,
                                            int attempt = 0);
    }
}
