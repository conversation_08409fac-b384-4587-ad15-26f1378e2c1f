//using Microsoft.SemanticKernel;
//using Microsoft.SemanticKernel.Connectors.OpenAI;

//namespace Backend.Plugins
//{
//    public sealed class PluginFilter
//    {
//        private static string? _provider;
//        private static ILogger? _logger;
//        private static string? _collection;
//        private static int _max;

//        public PluginFilter(string provider, ILogger logger, string collection, int max)
//        {
//            _provider = provider;
//            _logger = logger;
//            _collection = collection;
//            _max = max;
//        }

//        public async Task OnFunctionInvocationAsync(FunctionInvocationContext context, Func<FunctionInvocationContext, Task> next)
//        {
//            var request = GetRequestArgument(context.Arguments);

//            // Execute plugin selection logic for "InvokePrompt" function only, as main entry point.
//            if (context.Function.Name.Contains(nameof(KernelExtensions.InvokePromptAsync)) && !string.IsNullOrWhiteSpace(request))
//            {
//                // Get imported plugins in kernel.
//                var plugins = context.Kernel.Plugins;

//                // Find best functions for original request.
//                var bestFunctions = await functionProvider.GetBestFunctionsAsync(collectionName, request, plugins, numberOfBestFunctions);

//                // If any found, update execution settings and execute the request.
//                if (bestFunctions.Count > 0)
//                {
//                    bestFunctions.ForEach(function
//                        => logger.LogInformation("Best function found: {PluginName}-{FunctionName}", function.PluginName, function.Name));

//                    var updatedExecutionSettings = GetExecutionSettings(context.Arguments, bestFunctions);

//                    if (updatedExecutionSettings is not null)
//                    {
//                        // Update execution settings.
//                        context.Arguments.ExecutionSettings = updatedExecutionSettings;

//                        // Execute the request.
//                        await next(context);

//                        return;
//                    }
//                }
//            }

//            // Otherwise, execute a request with default logic, where all plugins will be shared.
//            await next(context);
//        }

//        private static Dictionary<string, PromptExecutionSettings>? GetExecutionSettings(KernelArguments arguments, List<KernelFunction> functions)
//        {
//            var promptExecutionSettings = arguments.ExecutionSettings?[PromptExecutionSettings.DefaultServiceId];

//            if (promptExecutionSettings is not null && promptExecutionSettings is OpenAIPromptExecutionSettings openAIPromptExecutionSettings)
//            {
//                // Share only selected functions with AI.
//                openAIPromptExecutionSettings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(functions);

//                return new() { [PromptExecutionSettings.DefaultServiceId] = openAIPromptExecutionSettings };
//            }

//            return null;
//        }

//        private static string? GetRequestArgument(KernelArguments arguments)
//            => arguments.TryGetValue("Request", out var requestObj) && requestObj is string request ? request : null;
//    }
//}