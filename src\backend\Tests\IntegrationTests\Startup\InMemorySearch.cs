﻿using Azure.Search.Documents;
using Backend.Models;
using System.Collections.Concurrent;

public class InMemorySearch : ISearchService
{
    private readonly ConcurrentDictionary<string, SectionItem[]> _index = new();

    public Task<SectionItem[]> QueryDocumentsAsync(string workspace, SearchSettings settings, string? textQuery = null, float[]? vector = null, CancellationToken ct = default)
    {
        _index.TryGetValue(workspace, out var results);
        return Task.FromResult(results ?? Array.Empty<SectionItem>());
    }

    public Task AddToSearchAsync(IEnumerable<Section> sections, string PDL = "Default")
    {
        foreach (var section in sections)
        {
            _index[section.Workspace] = new[] { new SectionItem(section.SourcePage, section.Content) };
        }
        return Task.CompletedTask;
    }

    public SearchClient GetSearchClient(string val)
    {
        throw new NotImplementedException();
    }
}