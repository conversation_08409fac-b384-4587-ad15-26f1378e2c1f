import React from "react";
import { HashRouter, Routes, Route, useNavigate } from "react-router-dom";
import TopComponent from "./components/TopComponent";
import ManageWorkspaces from "./components/ManageWorkspaces/ManageWorkspaces";
import EditWorkspace from "./components/EditWorkSpace/EditWorkspace";
import Healthz from "./components/Healthz/Healthz";
import useAppInsights from "./services/appInsightService";
import ChatWithChatTab from "./components/Chat/ChatWithChatTab";
import { useAuth } from "./hooks/useAuth";
import DisclaimerTab from "./components/TabsSection/DisclaimerTab";
import HelpTab from "./components/TabsSection/HelpTab";
import UpdatesTab from "./components/TabsSection/UpdatesTab";
import SettingsTab from "./components/TabsSection/SettingsTab/SettingsTab";
import TranslationTab from "./components/TabsSection/TranslationTab";
import TextTranslationTab from "./components/TabsSection/TextTranslationTab";
import AddCustomTemplate from "./components/TabsSection/SettingsTab/AddCustomTemplate";
import DeleteCustomTemplate from "./components/TabsSection/SettingsTab/DeleteCustomTemplate";

const ProcessRedirect: React.FC = () => {
  const navigate = useNavigate();
  const { activeAccount } = useAuth();

  React.useEffect(() => {
    /** If user is logged-in and enters a random path it will redirect to "/" if not, it allows redirection to MSAL */ 
    if (activeAccount) {
      navigate("/");
    }
  }, [navigate]);

  return <TopComponent />;
};

const AppRouter: React.FC = () => {
  useAppInsights();

  return (
    <HashRouter>
      <Routes>
        <Route path="/" element={<TopComponent />}>
          <Route index element={<ChatWithChatTab />} />
          <Route path="manage-workspaces" element={<ManageWorkspaces />} />
          <Route path="edit-workspace" element={<EditWorkspace />} />
          <Route path="disclaimer" element={<DisclaimerTab />} />
          <Route path="help" element={<HelpTab/> }/>
          <Route path="updates" element={<UpdatesTab/> }/>
          <Route path="settings" element={<SettingsTab />} />
          <Route path="doc-translation" element={<TranslationTab />} />
          <Route path="text-translation" element={<TextTranslationTab />} />
          <Route path="add-assistant-instructions-template" element={<AddCustomTemplate />} />
          <Route path="delete-assistant-instructions-template" element={<DeleteCustomTemplate />} />
        </Route>
        <Route path="/healthz" element={<Healthz />} />
        <Route path="*" element={<ProcessRedirect />} />
      </Routes>
    </HashRouter>
  );
};

export default AppRouter;
