﻿using Backend.Models.Context;

namespace Backend.Models
{
    /// <summary>
    /// Task object to be executed by the background processing.
    /// </summary>
    public class TaskItem
    {
        /// <summary>
        /// Unique name of the task.
        /// </summary>
        public string Task { get; set; }

        /// <summary>
        /// The type of task to be executed.
        /// </summary>
        public string TaskType { get; set; }

        /// <summary>
        /// The user that created the task.
        /// </summary
        public User User { get; set; }
        /// <summary>
        /// Used to indicate what the original file type was.
        /// </summary>
        public string Original { get; set; }
        
        /// <summary>
        /// Path to the file used to store this task.
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// Number of times the task has been retried.
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Constructor for TaskItem.
        /// </summary>
        /// <param name="task"></param>
        /// <param name="taskType"></param>
        /// <param name="path"></param>
        public TaskItem(string task,
                        string taskType,
                        User user,
                        string original = ".pdf",
                        string path = "",
                        int retryCount = 0)
        {
            Task = task;
            TaskType = taskType;
            Original = original;
            User = user;
            Path = path;
            RetryCount = retryCount;
        }

        /// <summary>
        /// Clones the TaskItem object.
        /// </summary>
        /// <returns></returns>
        public TaskItem Clone()
        {
            return new TaskItem(this.Task,
                                this.TaskType,
                                this.User,
                                this.Original,
                                this.Path,
                                this.RetryCount);
        }
    }
}
