module "cognitive-services" {
  source  = "app.terraform.io/ajg/cognitive-services/azurerm"
  version = "2.3.0"

  # Resource Metadata
  location            = var.azurerm_resource_group_location
  resource_group_name = var.azurerm_resource_group_name
  environment         = var.environment
  division_code       = var.division_short
  business_unit       = var.business_unit
  purpose             = "Document Intelligence"
  purpose_abbrevation = "doc${lower(var.region_instance)}"
  tags                = merge(var.tags, { "environment" = var.environment })

  # Custom Inputs
  kind                       = var.docintelligence_settings.kind
  sku_name                   = var.docintelligence_settings.sku_name
  dynamic_throttling_enabled = var.docintelligence_settings.dynamic_throttling_enabled

  # Networking
  azurerm_private_endpoint_subnet_id         = var.azurerm_private_endpoint_subnet_id
  custom_subdomain_name                      = "gallagherai-doc-int"
  custom_subdomain_name_append_unique_string = true
}