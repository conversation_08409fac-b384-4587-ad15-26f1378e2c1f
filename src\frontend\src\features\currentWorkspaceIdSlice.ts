import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CurrentWorkspaceId } from "../interfaces";

const initialState: CurrentWorkspaceId = {
  id: "",
};

const currentWorkspaceIdSlice = createSlice({
  name: "workspace",
  initialState,
  reducers: {
    setCurrentWorkspaceId: (state, action: PayloadAction<string>) => {
      state.id = action.payload;
    },
  },
});

export const { setCurrentWorkspaceId } = currentWorkspaceIdSlice.actions;

export default currentWorkspaceIdSlice.reducer;
