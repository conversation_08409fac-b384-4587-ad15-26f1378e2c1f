import React, { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { DocumentArrowUp20Filled, Info24Filled, DismissCircle24Filled, Document20Regular, TextT20Regular } from "@fluentui/react-icons";
import { useAuth } from "../../hooks/useAuth";
import { fetchLanguages, fetchDocuments, uploadDocuments } from "../../services/translationService";
import { FILE_TYPE_ACCEPTANCE, FILE_MIME_TYPES } from "../../constants/constants";
import TranslatedDocumentsList from "../../components/DocsContainer/TranslatedDocumentsList";
import Toast from "../Modal/ToastModal/Toast";
import useToast from "../../hooks/useToast";
import { setTranslationFilesUpload, setNewDocument } from "../../features/translationFilesUploadFlagSlice";
import { setSelectedLanguage } from "../../features/translationLanguageSlice";
import { RootState } from "../../store";
import TextTranslationTab from "./TextTranslationTab";
import { useNavigate } from "react-router";
import { initLanguageSettingDB, getLanguageSetting, saveLanguageSetting } from "../../db/languageDB";
import { LANGUAGE_SETTINGS_ID } from "../../constants/dbConstants";
import { useAppDispatch, useAppSelector } from "../../app/hooks";


const TranslationTab: React.FC = () => {
    const { acquireToken } = useAuth();
    const [, setIsWrongFileUploaded] = useState(false);
    const [, setIsDocumentAdded] = useState(false);
    const [uploadedDocuments, setUploadedDocuments] = useState<File[]>([]);
    const [languages, setLanguages] = useState<{ code: string, name: string }[]>([]);
    const [activeTab, setActiveTab] = useState<"document" | "text">("document");
    // Use typed selector hook for better type safety
    const isLoading = useAppSelector((state: RootState) => state.translationFilesUpload.status);
    // Use typed selector hook for better type safety
    const newDocument = useAppSelector((state: RootState) => state.translationFilesUpload.newDocument);
    // Use typed selector hook for better type safety
    const selectedLanguage = useAppSelector((state: RootState) => state.translationLanguageSelection.selectedLanguage);
    // Use typed dispatch hook for better type safety
    const dispatch = useAppDispatch();
    const navigate = useNavigate();

    const { toasts, triggerToast, closeToast } = useToast();

    const formattedFileTypes = FILE_TYPE_ACCEPTANCE.map((type) => type.replace(/\./g, "").toUpperCase()).join(", ");

    useEffect(() => {
        const loadLanguages = async () => {
            const getToken = await acquireToken();
            if (!getToken?.accessToken) {
                console.error("Token acquisition failed.");
                return;
            }

            const availableLanguages = await fetchLanguages(getToken.accessToken);
            setLanguages(availableLanguages);
        };

        loadLanguages();
    }, [acquireToken]);

    useEffect(() => {
        const loadLanguageFromDb = async () => {
            const initialized = await initLanguageSettingDB();
            if (!initialized) return;

            const getToken = await acquireToken();
            if (!getToken?.accessToken) {
                console.error("Token acquisition failed.");
                return;
            }

            const availableLanguages = await fetchLanguages(getToken.accessToken);
            setLanguages(availableLanguages);

            const stored = await getLanguageSetting(LANGUAGE_SETTINGS_ID);

            if (stored?.selectedLanguage && availableLanguages.some((lang) => lang.code === stored.selectedLanguage)
            ) {
                dispatch(setSelectedLanguage(stored.selectedLanguage));
            } else {
                dispatch(setSelectedLanguage(""));
            }
        };

        loadLanguageFromDb();
    }, [dispatch, acquireToken]);

    const onDrop = useCallback(
        async (acceptedFiles: File[]) => {
            if (!selectedLanguage) {
                triggerToast({
                    text: "Please select the language you want your document translated to before uploading.",
                    icon: <DismissCircle24Filled />,
                    duration: 7,
                    position: "top-center",
                    bgColor: "bg-red-500",
                });
                return;
            }

            const getToken = await acquireToken();
            if (!getToken?.accessToken) {
                console.error("Token acquisition failed.");
                return;
            }

            const translatedDocs: { name: string }[] = await fetchDocuments(getToken.accessToken);

            const filteredFiles = acceptedFiles.filter((file) => {
                const fileExtension = file.name.split(".").pop()?.toLowerCase();
                return FILE_TYPE_ACCEPTANCE.includes(`.${fileExtension}`);
            });

            if (filteredFiles.length === 0) {
                setIsWrongFileUploaded(true);
                triggerToast({
                    text: `Current file types supported: ${formattedFileTypes}.`,
                    icon: <DismissCircle24Filled />,
                    duration: 7,
                    position: "top-center",
                    bgColor: "bg-red-500",
                });
                return;
            }

            try {
                dispatch(setTranslationFilesUpload(true)); // Show spinner

                // Filter out files that already exist with the same name and selected language
                const newFiles = filteredFiles.filter((file) => {
                    const fileNameWithoutExt = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
                    const duplicateExists = translatedDocs.some((doc) =>
                        doc.name.startsWith(fileNameWithoutExt) && doc.name.endsWith(`_${selectedLanguage}${file.name.slice(fileNameWithoutExt.length)}`)
                    );
                    return !duplicateExists;
                });

                if (newFiles.length === 0) {
                    alert(`This file (${filteredFiles[0].name}) has already been translated in the selected language.`);
                    dispatch(setTranslationFilesUpload(false)); // Hide spinner
                    return;
                }

                triggerToast({
                    text: "Newly updated documents may take a minute or two before they become available.",
                    icon: <Info24Filled />,
                    duration: 7,
                    position: "top-center",
                });

                setIsDocumentAdded(true);
                setIsWrongFileUploaded(false);
                setUploadedDocuments((prev) => [...prev, ...newFiles]);

                await uploadDocuments(selectedLanguage, newFiles, getToken?.accessToken);

                dispatch(setNewDocument(true)); // Trigger document list refresh

            } catch (error) {
                let errorMessage = "Failed to upload documents.";

                if (error instanceof Error) {
                    try {
                        const errorJson = JSON.parse(error.message);
                        errorMessage = errorJson.Message;
                    } catch (e) {
                        console.error("Error uploading document", e);
                    }
                }
                setUploadedDocuments((prev) => prev.filter((doc) => !acceptedFiles.some((file) => file.name === doc.name)));

                alert(errorMessage);
            } finally {
                dispatch(setTranslationFilesUpload(false)); // Hide spinner
            }
        },
        [selectedLanguage, acquireToken, uploadedDocuments, dispatch]
    );

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: FILE_MIME_TYPES,
    });

    return (
        <div className="relative flex flex-col p-8 items-center h-full w-full bg-white dark:bg-zinc-800">
            <div className="w-full max-w-4xl text-gallagher-blue-600 dark:text-gallagher-blue-200 flex items">
                <Document20Regular className="mr-1" />
                <button className={`tab ${activeTab === "document" ? "underline cursor-pointer" : "cursor-pointer"}`}
                    onClick={() => {
                        setActiveTab("document")
                        navigate("/doc-translation");
                    }}>
                    Document Translation
                </button>
                <span className="hidden lg:block text-zinc-300 dark:text-zinc-500 px-2">|</span>
                <TextT20Regular className="mr-1" />
                <button className={`tab ${activeTab === "text" ? "underline cursor-pointer" : "cursor-pointer"}`}
                    onClick={() => {
                        setActiveTab("text")
                        navigate("/text-translation");
                    }}>
                    Text Translation
                </button>
            </div>

            {activeTab === "text" ? (
                <TextTranslationTab />
            ) : (
                <>
                    <h1 className="text-2xl p-4 font-bold dark:text-gray-300">
                        Document Translation</h1>
                    <div className="w-full max-w-4xl">
                        <div className="flex justify-between w-full my-5">
                            <label className="text dark:text-gray-300">
                                Translate a document to a different language based on your selection. A <a href="https://ajg0.sharepoint.com/:b:/r/teams/GO-ai_at_Gallagher/Shared%20Documents/Gallagher%20AI%20Document%20Translation%20Quick%20Start%20Guide_en.pdf?csf=1&web=1&e=ueDzxP" target="_blank" className="text-gallagher-dark-300 dark:text-sky-400 underline underline-offset-6 decoration-gallagher-dark-300"> Quick Start guide </a>
                                is available should you require assistance.<br/>
                                <b>Please note</b>: the font in the translated document may not always match that of the source material.
                            </label>
                        </div>
                        <div className="mb-4 w-full max-w-xs dark:text-black ">
                            <select
                                id="language"
                                value={selectedLanguage}
                                onChange={(e) => {
                                    const lang = e.target.value;
                                    dispatch(setSelectedLanguage(lang));
                                    saveLanguageSetting({ id: LANGUAGE_SETTINGS_ID, selectedLanguage: lang });
                                }}
                                className="cursor-pointer w-full p-2 border-2 rounded-sm dark:text-gray-300 dark:bg-zinc-800 dark:border-zinc-600`"
                            ><option value="" disabled>Select Language</option>

                                {languages.map((lang) => (
                                    <option key={lang.code} value={lang.code}>
                                        {lang.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                    <div className="w-full max-w-4xl">
                        <div
                            {...getRootProps()}
                            className={`w-full h-10 flex items-center justify-center border-2 border-dashed p-10 rounded cursor-pointer ${isDragActive
                                ? "bg-gray-300 text-white"
                                : "bg-white hover:bg-gallagher-dark-300 hover:text-white"
                                } hover:shadow-md hover:border-white dark:text-gray-300 dark:hover:bg-gallagher-dark-300 dark:bg-zinc-800 dark:border-zinc-600`}
                        >
                            <input {...getInputProps()} />
                            <DocumentArrowUp20Filled
                                className={`mr-2 ${isDragActive ? "text-white" : "text-gray-600"}`}
                            />
                            <p className={isDragActive ? "text-white" : ""}>
                                {isDragActive ? ("Drop the documents here...") : (<> Drag and drop files or click. <br /> Supported file types: <strong> pdf, docx, pptx.</strong></>)}
                            </p>
                        </div>
                    </div>
                    <div className="w-full max-w-4xl">
                        <TranslatedDocumentsList isLoading={isLoading} newDocument={newDocument} />
                    </div>
                    {toasts.map((toast) => (
                        <Toast
                            key={toast.id}
                            id={toast.id}
                            position={toast.position}
                            text={toast.text}
                            icon={toast.icon}
                            duration={toast.duration}
                            onClose={() => closeToast(toast.id)}
                            bgColor={toast.bgColor}
                        />
                    ))}
                </>
            )}
        </div>
    );
};

export default TranslationTab;