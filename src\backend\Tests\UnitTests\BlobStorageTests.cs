﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Backend.Models;
using Backend.Services;
using Backend.Models.Context;
using Moq;
using System.Text;

namespace BackendUnitTests
{
    public class BlobStorageTests
    {
        private readonly Mock<BlobClient> _mockBlobClient;
        private readonly Dictionary<string, Mock<BlobContainerClient>> _mockContainerClients;
        private readonly Mock<ITaskQueue> _mockTaskQueue;
        private readonly BlobStorageService _service;

        private readonly MemoryStream _stream = new MemoryStream(Encoding.UTF8.GetBytes("FileContent"));
        private BlobItem[] _blobs;

        public BlobStorageTests()
        {
            _blobs =
            [
                BlobsModelFactory.BlobItem("oid/workspace1", metadata: new Dictionary<string, string>
                {
                    { "id", "1" },
                    { "name", "Workspace1" },
                    { "description", "Description1" },
                    { "hdi_isfolder", "true" }
                }),
                BlobsModelFactory.BlobItem("oid/workspace2", metadata: new Dictionary<string, string>
                {
                    { "id", "2" },
                    { "name", "Workspace2" },
                    { "description", "Description2" },
                    { "hdi_isfolder", "true" }
                }),
                BlobsModelFactory.BlobItem("oid/workspace1/documentA.pdf", metadata: new Dictionary<string, string> { }),
                BlobsModelFactory.BlobItem("oid/workspace2/documentB.pdf", metadata: new Dictionary<string, string> { })
            ];

            CurrentContext.User = new User
            {
                OID = "oid",
                DisplayName = "user",
                Images = false,
                ChatCount = 0,
                PDL = "NAM"
            };

            _mockTaskQueue = createMockTaskQueue();
            _mockBlobClient = createMockBlobClient();
            _mockContainerClients = createMockContainerClients(_mockBlobClient, _blobs);
            _service = createBlobStorageService(
                _mockContainerClients.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Object),
                _mockTaskQueue.Object);
        }

        /******************************************
        *                Tests
        ******************************************/

        [Fact]
        public async Task GetWorkspaces_ReturnsListOfWorkspaces()
        {
            // Arrange
            var prefix = "oid";

            // Act
            var result = await _service.GetWorkspaces(prefix, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("1", result[0].Id);
            Assert.Equal("Workspace1", result[0].Name);
            Assert.Equal("Description1", result[0].Description);
            Assert.Equal("2", result[1].Id);
            Assert.Equal("Workspace2", result[1].Name);
            Assert.Equal("Description2", result[1].Description);
        }

        [Fact]
        public async Task GetWorkspace_ReturnsWorkspace()
        {
            // Arrange
            var workspaceId = "oid/workspace1";

            // Act
            var result = await _service.GetWorkspace(workspaceId, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("1", result.Id);
            Assert.Equal("Workspace1", result.Name);
            Assert.Equal("Description1", result.Description);
            Assert.Single(result.Documents!);
            Assert.Equal("documentA.pdf", result.Documents![0].Name);
        }

        [Fact]
        public async Task CreateWorkspaceAsync_CreatesWorkspaceAndReturnsIt()
        {
            // Arrange
            var oid = "oid";
            var workspace = new Workspace
            {
                Name = "NewWorkspace",
                Description = "NewDescription"
            };

            // Act
            var result = await _service.CreateWorkspaceAsync(oid, workspace);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Id);
            Assert.Equal(workspace.Name, result.Name);
            Assert.Equal(workspace.Description, result.Description);
        }

        [Fact]
        public async Task UpdateWorkspaceAsync_UpdatesWorkspaceAndReturnsIt()
        {
            // Arrange
            var oid = "oid";
            var workspace = new Workspace
            {
                Id = "2",
                Name = "2",
                Description = "2"
            };

            // Act
            var result = await _service.UpdateWorkspaceAsync(oid, workspace);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(workspace.Name, result.Name);
            Assert.Equal(workspace.Description, result.Description);
        }

        [Fact]
        public async Task DeleteBlobAsync_DeletesBlob()
        {
            // Arrange
            var prefix = "oid/workspace2/documentB.pdf";

            // Act
            await _service.DeleteBlobAsync(prefix);

            // Assert
            _mockBlobClient.Verify(client => client.DeleteAsync(It.IsAny<DeleteSnapshotsOption>(), It.IsAny<BlobRequestConditions>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockTaskQueue.Verify(queue => queue.QueueItemAsync(It.IsAny<TaskItem>(), null), Times.Once);
        }

        [Fact]
        public async Task GetDocuments_ReturnsListOfDocuments()
        {
            // Arrange
            var prefix = "oid/workspace1";

            // Act
            var result = await _service.GetDocuments(prefix, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("documentA.pdf", result[0].Name);
        }

        [Fact]
        public async Task GetDocumentStreamAsync_ReturnsDocumentStream()
        {
            // Arrange
            var document = "oid/workspace1/documentA.pdf";

            // Act
            var result = await _service.GetDocumentStreamAsync(document, CancellationToken.None);

            // Assert
            Assert.Equal(_stream, result);

            _mockContainerClients["Default"].Verify(client => client.GetBlobClient(document), Times.Once);
            _mockBlobClient.Verify(client => client.OpenReadAsync(0, null, It.IsAny<BlobRequestConditions>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task UploadDocumentsAsync_UploadsDocumentsAndReturnsResult()
        {
            // Arrange
            var oid = "oid";
            var workspaceId = "workspace1";
            var fileName = "document1.pdf";
            var documentStream = new MemoryStream(Encoding.UTF8.GetBytes("FileContent1"));

            // Act
            var result = await _service.UploadDocumentsAsync(oid, workspaceId, fileName, documentStream, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(fileName, result.Name);
            Assert.False(result.Processed == Processed.True);

            _mockBlobClient.Verify(b => b.UploadAsync(It.IsAny<Stream>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockBlobClient.Verify(b => b.SetMetadataAsync(It.IsAny<IDictionary<string, string>>(), It.IsAny<BlobRequestConditions>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockTaskQueue.Verify(t => t.QueueItemAsync(It.IsAny<TaskItem>(), null), Times.Once);
        }

        /******************************************
         * Helper functions to create mock objects
         ******************************************/

        private Mock<ITaskQueue> createMockTaskQueue()
        {
            var mockTaskQueue = new Mock<ITaskQueue>();
            mockTaskQueue
                .Setup(queue => queue.QueueItemAsync(It.IsAny<TaskItem>(), It.IsAny<DateTime>()))
                .Returns(ValueTask.CompletedTask);
            return mockTaskQueue;
        }

        private Mock<BlobClient> createMockBlobClient()
        {
            var mockBlobClient = new Mock<BlobClient>();
            mockBlobClient
                .Setup(client => client.UploadAsync(It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(Response.FromValue(Mock.Of<BlobContentInfo>(), Mock.Of<Response>())));

            mockBlobClient
                .Setup(client => client.DeleteAsync(It.IsAny<DeleteSnapshotsOption>(), It.IsAny<BlobRequestConditions>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Response.FromValue(Mock.Of<Response>(), Mock.Of<Response>()));

            mockBlobClient
                .Setup(client => client.DeleteIfExistsAsync(It.IsAny<DeleteSnapshotsOption>(),
                                                            It.IsAny<BlobRequestConditions>(),
                                                            It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(Response.FromValue(true, Mock.Of<Response>())));

            mockBlobClient
                .Setup(client => client.SetMetadataAsync(It.IsAny<IDictionary<string, string>>(),
                                                         It.IsAny<BlobRequestConditions>(),
                                                         It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(Response.FromValue(Mock.Of<BlobInfo>(), Mock.Of<Response>())));

            var mockBlobProperties = BlobsModelFactory.BlobProperties(
                lastModified: DateTimeOffset.UtcNow
            );

            mockBlobClient.Setup(x => x.GetPropertiesAsync(It.IsAny<BlobRequestConditions>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(Response.FromValue(mockBlobProperties, new Mock<Response>().Object));

            return mockBlobClient;
        }

        private Dictionary<string, Mock<BlobContainerClient>> createMockContainerClients(Mock<BlobClient> mockBlobClient, BlobItem[] blobs)
        {
            var mockContainerClients = new Dictionary<string, Mock<BlobContainerClient>>
            {
                { "Default", new Mock<BlobContainerClient>() }
            };

            mockContainerClients["Default"]
                .Setup(client => client.GetBlobClient(It.IsAny<string>()))
            .Returns<string>(blobName =>
            {
                if (blobs.FirstOrDefault(blob => blob.Name == blobName) != null)
                {
                    mockBlobClient
                        .Setup(m => m.GetPropertiesAsync(null, CancellationToken.None))
                        .ReturnsAsync(Response.FromValue(
                BlobsModelFactory.BlobProperties(
                                metadata: blobs.FirstOrDefault(blob => blob.Name == blobName)!.Metadata), Mock.Of<Response>()));
                }

                mockBlobClient
                    .Setup(client => client.OpenReadAsync(0, null, It.IsAny<BlobRequestConditions>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(_stream);

                return mockBlobClient.Object;
            });

            mockContainerClients["Default"]
                .Setup(m => m.GetBlobsAsync(
                    It.IsAny<BlobTraits>(),
                    It.IsAny<BlobStates>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Returns<BlobTraits, BlobStates, string, CancellationToken>((traits, states, prefix, token) =>
                {
                    var filteredBlobs = blobs.Where(blob => blob.Name.StartsWith(prefix)).ToArray();
                    Page<BlobItem> page = Page<BlobItem>.FromValues(filteredBlobs, null, Mock.Of<Response>());
                    return AsyncPageable<BlobItem>.FromPages(new[] { page });
                });

            return mockContainerClients;
        }

        private BlobStorageService createBlobStorageService(Dictionary<string, BlobContainerClient> mockContainerClients,
                                                       ITaskQueue mockTaskQueue,
                                                       int days = 30)
        {
            return new BlobStorageService(
               mockContainerClients,
               days,
               mockTaskQueue
           );
        }
    }
}