import { api } from '../../app/api';
import { Workspace, WorkspaceInput } from '../../interfaces';
import { WorkspaceResponse, WorkspaceListResponse } from '../../types/apiTypes';

export const workspacesApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all workspaces
    getWorkspaces: builder.query<WorkspaceListResponse, void>({
      query: () => '/workspaces',
      providesTags: ['Workspaces' as const],
      transformResponse: (response: Workspace[]): WorkspaceListResponse => {
        return response;
      },
    }),

    // Get a single workspace by ID
    getWorkspace: builder.query<WorkspaceResponse, string>({
      query: (id) => `/workspaces/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Workspaces' as const, id }],
      transformResponse: (response: Workspace): WorkspaceResponse => {
        return response;
      },
    }),

    // Create a new workspace
    createWorkspace: builder.mutation<WorkspaceResponse, WorkspaceInput>({
      query: (workspace) => ({
        url: '/workspaces',
        method: 'POST',
        body: workspace,
      }),
      invalidatesTags: ['Workspaces' as const],
      transformResponse: (response: Workspace): WorkspaceResponse => {
        return response;
      },
    }),

    // Update an existing workspace
    updateWorkspace: builder.mutation<WorkspaceResponse, { id: string; workspace: WorkspaceInput }>({
      query: ({ id, workspace }) => ({
        url: `/workspaces/${id}`,
        method: 'PUT',
        body: workspace,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'Workspaces' as const, id },
        'Workspaces' as const,
      ],
      transformResponse: (response: Workspace): WorkspaceResponse => {
        return response;
      },
    }),

    // Delete a workspace
    deleteWorkspace: builder.mutation<void, string>({
      query: (id) => ({
        url: `/workspaces/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Workspaces' as const],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetWorkspacesQuery,
  useGetWorkspaceQuery,
  useCreateWorkspaceMutation,
  useUpdateWorkspaceMutation,
  useDeleteWorkspaceMutation,
} = workspacesApi;
