import { useRef } from "react";
import {
  ArrowDownload20Regular,
  Dismiss20Regular,
  Checkmark20Regular,
  Warning20Regular,
} from "@fluentui/react-icons";
import Spinner from "../LoadAnimation/DocumentSpinner";
import Tooltip from "../Tooltip/Tooltip";
import {
  deleteDocument,
  downloadDocument,
} from "../../services/documentsService";
import { useAuth } from "../../hooks/useAuth";
import { RootState } from "../../store";
import { setWorkspaceProperty } from "../../features/workspaceSlice";
import fileIcons from "../../utils/fileIcons";
import { FILE_NOT_PROCESSED_ERROR } from "../../constants/constants";
import { useAppSelector, useAppDispatch } from "../../app/hooks";

const DocumentsList: React.FC = () => {
  const { getAuthResult } = useAuth();
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  // Use typed selector hook for better type safety
  const { id, documents = [] } = useAppSelector(
    (state: RootState) => state.workspace
  );

  const linkRef = useRef<HTMLAnchorElement | null>(null);

  const handleDownload = async (documentName: string) => {
    try {
      const authResult = await getAuthResult();
      if (!authResult) return;

      const blob = await downloadDocument(
        id,
        documentName,
        authResult.accessToken
      );
      const url = window.URL.createObjectURL(blob);
      const link = linkRef.current;
      if (link) {
        link.href = url;
        link.setAttribute("download", documentName);
        link.click();
        window.URL.revokeObjectURL(url); // Clean up the URL object
      }
    } catch (error) {
      console.error("Error downloading document:", error);
    }
  };

  const handleRemove = async (documentName: string) => {
    try {
      if (!getAuthResult) return;

      const authResult = await getAuthResult();

      if (!authResult?.accessToken) {
        throw new Error("Token acquisition failed.");
      }

      await deleteDocument(id, documentName, authResult.accessToken);

      dispatch(
        setWorkspaceProperty({
          property: "documents",
          value: documents.filter((doc) => doc.name !== documentName),
        })
      );
    } catch (error) {
      console.error("Error deleting document:", error);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop() || "other"; // Assigns the value 'other' if no extension is found
    return fileIcons[extension] || fileIcons["other"]; // Default to otherIcon if no extension is found
  };

  return (
    <div className="w-full">
      {documents !== null && documents.length > 0 ? (
        <div className="grid md:grid-cols-2 grid-cols-1 gap-4 mb-6 mt-6">
          {documents.map((document, index) => (
            <div
              key={index}
              onClick={() => handleDownload(document.name)}
              aria-label="Download This Document"
              title="Download This Document"
              className={`flex justify-between items-start p-4 bg-wild-sand-50 dark:bg-zinc-800 hover:bg-wild-sand-100 dark:hover:bg-zinc-700 rounded-sm hover:shadow-md cursor-pointer border dark:border-zinc-600`}
            >
              <img
                src={getFileIcon(document.name)}
                className="min-w-12 w-12 mr-2.5"
                aria-label="File Type Icon"
                title="File Type Icon"
              />
              <div className="flex flex-col space-y-2 flex-1">
                <span className="text-gray-800 mr-1 dark:text-gray-300 break-all">{document.name}</span>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Expires: {document.expires}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                {document.processed === 1 ? (
                  <Checkmark20Regular style={{ color: "green" }} />
                ) : document.processed === 2 ? (
                  <Tooltip message={FILE_NOT_PROCESSED_ERROR}>
                    <Warning20Regular style={{ color: "orange" }} />
                  </Tooltip>
                ) : (
                  <Spinner />
                )}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownload(document.name);
                  }} className="cursor-pointer text-silver-chalice-700 hover:text-blue-700"
                  aria-label="Download This Document"
                  title="Download This Document"
                >
                  <ArrowDownload20Regular />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(document.name);
                  }}
                  className="cursor-pointer text-silver-chalice-700 hover:text-red-700"
                  aria-label="Delete This Document"
                  title="Delete This Document"
                >
                  <Dismiss20Regular />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500 m-6">No documents uploaded yet.</p>
      )}
      <a ref={linkRef} style={{ display: "none" }}>
        Hidden Download Link
      </a>
    </div>
  );
};

export default DocumentsList;
