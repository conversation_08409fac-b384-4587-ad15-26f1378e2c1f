﻿using Backend.Models;
using HtmlAgilityPack;
using System.Text;

namespace Backend.Services
{
    public class ConversionService : IConversionService
    {
        private static readonly HttpClient _client = new();
        private static string? _endpoint;
        private static string? _key;

        public ConversionService(string endpoint, string key)
        {
            _endpoint = endpoint;
            _key = key;
        }

        public async Task<Stream> ConvertToPDF(string fileName, Stream stream)
        {
            stream.Seek(0, SeekOrigin.Begin);

            using (var content = new MultipartFormDataContent())
            {
                var contentStream = new MemoryStream();
                await stream.CopyToAsync(contentStream);
                contentStream.Seek(0, SeekOrigin.Begin);

                content.Add(new StreamContent(contentStream), _key!, fileName);

                var response = await _client.PostAsync(_endpoint, content);
                response.EnsureSuccessStatusCode();

                var responseStream = await response.Content.ReadAsStreamAsync();
                return responseStream;
            }
        }

        public static string ConvertHtmlToMarkdown(HtmlNode node)
        {
            StringBuilder markdown = new StringBuilder();
            bool reachedExternalLinks = false;

            foreach (var child in node.ChildNodes)
            {
                if (child.Name == "style" || child.Name == "link")
                {
                    // Skip <style> and <link> tags entirely, including their content
                    continue;
                }

                if (child.Name == "h2" && child.InnerText.Trim().Equals("External links", StringComparison.OrdinalIgnoreCase))
                {
                    // Stop processing further content after "External links"
                    reachedExternalLinks = true;
                    break;
                }

                switch (child.Name)
                {
                    case "h1":
                        markdown.AppendLine($"# {HtmlEntity.DeEntitize(child.InnerText.Trim())}");
                        break;
                    case "h2":
                        markdown.AppendLine($"## {HtmlEntity.DeEntitize(child.InnerText.Trim())}");
                        break;
                    case "h3":
                        markdown.AppendLine($"### {HtmlEntity.DeEntitize(child.InnerText.Trim())}");
                        break;
                    case "p":
                    case "dd": // Handle <dd> similarly to <p>
                        markdown.AppendLine($"{ProcessInlineElements(child, reachedExternalLinks)}\n");
                        break;
                    case "ul":
                        var ulNodes = child.SelectNodes("li");
                        if (ulNodes != null)
                        {
                            foreach (var li in ulNodes)
                            {
                                markdown.AppendLine($"- {HtmlEntity.DeEntitize(li.InnerText.Trim())}");
                            }
                        }
                        break;
                    case "ol":
                        var olNodes = child.SelectNodes("li");
                        if (olNodes != null)
                        {
                            int index = 1;
                            foreach (var li in olNodes)
                            {
                                markdown.AppendLine($"{index}. {HtmlEntity.DeEntitize(li.InnerText.Trim())}");
                                index++;
                            }
                        }
                        break;

                    case "table":
                        ConvertTableToMarkdown(child, markdown);
                        break;
                    default:
                        markdown.Append(ConvertHtmlToMarkdown(child));
                        break;
                }
            }

            return markdown.ToString();
        }

        private static string ProcessInlineElements(HtmlNode node, bool includeLinks)
        {
            StringBuilder inlineMarkdown = new StringBuilder();
            bool lastWasText = false;

            foreach (var child in node.ChildNodes)
            {
                if (child.Name == "a")
                {
                    string text = HtmlEntity.DeEntitize(child.InnerText.Trim());
                    string href = child.GetAttributeValue("href", string.Empty);

                    if (includeLinks && !string.IsNullOrEmpty(href))
                    {
                        // Include hyperlinks in the reference section
                        if (lastWasText) inlineMarkdown.Append(" ");
                        inlineMarkdown.Append($"[{text}]({href})");
                    }
                    else
                    {
                        // Exclude hyperlinks, only append the text content
                        if (lastWasText) inlineMarkdown.Append(" ");
                        inlineMarkdown.Append(text);
                    }
                    lastWasText = true;
                }
                else if (child.NodeType == HtmlNodeType.Text)
                {
                    string text = HtmlEntity.DeEntitize(child.InnerText.Trim());
                    if (!string.IsNullOrEmpty(text))
                    {
                        if (lastWasText) inlineMarkdown.Append(" ");
                        inlineMarkdown.Append(text);
                        lastWasText = true;
                    }
                }
                else
                {
                    string content = ProcessInlineElements(child, includeLinks);
                    if (!string.IsNullOrEmpty(content))
                    {
                        if (lastWasText) inlineMarkdown.Append(" ");
                        inlineMarkdown.Append(content);
                        lastWasText = true;
                    }
                }
            }

            return inlineMarkdown.ToString();
        }

        private static void ConvertTableToMarkdown(HtmlNode tableNode, StringBuilder markdown)
        {
            var rows = tableNode.SelectNodes(".//tr");
            if (rows == null) return;

            bool headerProcessed = false;

            foreach (var row in rows)
            {
                var headers = row.SelectNodes("th");
                var cells = row.SelectNodes("td");

                if (headers != null && headers.Count > 0)
                {
                    // Process header row
                    foreach (var header in headers)
                    {
                        markdown.Append($"| {HtmlEntity.DeEntitize(header.InnerText.Trim())} ");
                    }
                    markdown.AppendLine("|");

                    // Add Markdown table header separator
                    foreach (var header in headers)
                    {
                        markdown.Append("| --- ");
                    }
                    markdown.AppendLine("|");

                    headerProcessed = true;
                }
                else if (cells != null && cells.Count > 0)
                {
                    // Process data row
                    foreach (var cell in cells)
                    {
                        markdown.Append($"| {HtmlEntity.DeEntitize(cell.InnerText.Trim())} ");
                    }
                    markdown.AppendLine("|");
                }
            }
            markdown.AppendLine();
        }
    }
}
