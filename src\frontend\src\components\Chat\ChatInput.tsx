import { forwardRef, useImperativeHandle } from "react";
import { Send24Filled, CaretDown20Filled, ImageAdd24Regular, Dismiss16Filled, SpinnerIos20Regular } from "@fluentui/react-icons";
import TextareaAutosize from 'react-textarea-autosize';
import useChatInput from "../../hooks/useChatInput";
import Styles from "../TabsSection/ChatTab/ChatTab.module.css";
import { ChatInputProps } from "../../interfaces";
import { IMAGE_ERROR_MESSAGE, IMAGE_INPUT_ACCEPTANCE, MESSAGES_LIMIT_ALERT } from "../../constants/constants";
import DOMPurify from 'dompurify';

export interface ChatInputRef {
  setInputValue: (value: string) => void;
}

const ChatInput = forwardRef<ChatInputRef, ChatInputProps>(({ onMessageComplete }, ref) => {
  const {
    input,
    isFocused,
    selectedOption,
    workspaces,
    fileInputRef,
    notImageUpload,
    imageUploading,
    imageList,
    imageAttachmentDisable,
    messageLimitReached,
    handleImageChange,
    handleRemoveImg,
    handleOptionChange,
    handleInputChange,
    handleKeyDown,
    openFileDialog,
    handleFocus,
    handleBlur,
    sendChatMessage,
    setInputValue,
  } = useChatInput({ onMessageComplete });

  useImperativeHandle(ref, () => ({
    setInputValue: (value: string) => {
      setInputValue(value);
      // Force text area update
      const event = new Event('input', { bubbles: true });
      const textarea = document.querySelector('textarea[aria-label="Chat input"]');
      textarea?.dispatchEvent(event);
    }
  }), [setInputValue]);

  return (
    <div className="flex flex-col px-4 pb-2 mt-0 bg-wild-sand-50 dark:bg-zinc-800">
      {messageLimitReached && (
        <div className="flex items-center justify-center text-center mb-2 italic py-4 bg-yellow-100 dark:bg-yellow-800 dark:text-white border-yellow-700 rounded-2xl drop-shadow-md">
          {MESSAGES_LIMIT_ALERT}
        </div>
      )}
      <div className={`flex items-center p-2 border-2 bg-white dark:bg-zinc-600 rounded-2xl justify-between ${isFocused ? "border-gallagher-blue-400" : "border-zinc-200 dark:border-zinc-400"} ${messageLimitReached ? "hidden" : ""}`}>
        <div className="flex flex-col w-full relative">
          <TextareaAutosize
            className={`p-2 dark:bg-zinc-600 dark:text-white rounded-l-2xl focus:outline-hidden resize-none`}
            placeholder="Ask me anything"
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            onBlur={handleBlur}
            minRows={1}
            maxRows={10}
          />
          {(imageList.length > 0 || imageUploading) && (
            <div className={`flex bottom-0 left-0 w-full bg-white dark:bg-zinc-600 p-2 relative `}>
              {imageList && imageList.length > 0 && imageList.map((val: string, ind: number) => (
                <div key={ind} className="relative">
                  <img src={DOMPurify.sanitize(val)} alt="Image Preview" className="max-w-10 max-h-[120px] mr-2" />
                  <button
                    className="group absolute border-2 rounded-full flex items-center justify-center p-2 top-1 right-4 bg-white dark:bg-gray-400"
                    id={val}
                    onClick={() => handleRemoveImg(val, ind)}
                    aria-label="Remove Image"
                    title="Remove Image"
                  >
                    <Dismiss16Filled className="text-gallagher-dark-300 group-hover:text-gallagher-blue-400" />
                  </button>
                </div>
              ))}
              {imageUploading && (
                <div className="ml-8">
                  <SpinnerIos20Regular className={`${Styles.spinner} h-8 w-8 text-gallagher-blue-400`} />
                </div>
              )}
            </div>
          )}
          {notImageUpload && (
            <p className="text-red-500 ml-2">{IMAGE_ERROR_MESSAGE}</p>
          )}
        </div>
        <div className="flex">
          <div className={`p-2 h-11 bg-white dark:bg-zinc-600`}>
            <button className={`cursor-pointer text-curious-blue-500 dark:text-curious-blue-400 ${imageAttachmentDisable ? "pointer-events-none text-gray-500" : ""}`} onClick={openFileDialog} aria-label="Upload an Image" title="Upload an Image">
              <ImageAdd24Regular />
            </button>
            <input
              type="file"
              ref={fileInputRef}
              accept={IMAGE_INPUT_ACCEPTANCE}
              onChange={handleImageChange}
              className="hidden"
              id="imageUpload"
              disabled={imageAttachmentDisable}
              aria-label="Image Upload Input"
              title="Image Upload Input"
            />
          </div>
          <button
            className={`p-2 h-11 rounded-r-2xl bg-white dark:bg-zinc-600`}
            onClick={sendChatMessage}
            aria-label="Send Message"
            title="Send Message"
          >
            <Send24Filled className={`${input.trim() ? "cursor-pointer text-curious-blue-500 dark:text-curious-blue-400" : "text-gray-500 cursor-not-allowed"}`} />
          </button>
        </div>
      </div>
      <div className="relative mb-2 h-11 xl:w-1/2 lg:w-full md:w-full sm:w-full">
        <select
          className="cursor-pointer p-2 mt-2 dark:bg-zinc-600 dark:text-gray-400 border rounded-xl shadow-xs focus:outline-hidden w-full appearance-none truncate"
          value={selectedOption}
          onChange={handleOptionChange}
          aria-label="Select Workspace"
          title="Select Workspace"
        >
          <option value="">-- No Workspace --</option>
          {workspaces.map((workspace) => (
            <option key={workspace.id} value={workspace.id}>
              {workspace.name}
            </option>
          ))}
        </select>
        <CaretDown20Filled className="absolute top-5 right-1 pointer-events-none text-silver-chalice-700" />
      </div>
    </div>
  );
});

export default ChatInput;