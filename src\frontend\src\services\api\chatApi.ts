import { api } from '../../app/api';

// Define the chat request type
export interface ChatRequest {
  workspace_id: string | null;
  settings: object;
  messages: {
    role: string;
    content: {
      type: string;
      value: string | object;
    }[];
  }[];
}

export const chatApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Send a chat message and get a streaming response
    // Note: RTK Query doesn't natively support streaming responses,
    // so we'll need to handle this differently
    sendChatMessage: builder.mutation<string, ChatRequest>({
      queryFn: async (chatRequest, { getState }) => {
        try {
          // Get the base URL and token from the state
          const state = getState() as any;
          const token = state.token.token;
          const baseUrl = import.meta.env.VITE_API_URL;

          // Make the fetch request manually to handle streaming
          const response = await fetch(`${baseUrl}/Chat`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(chatRequest),
          });

          if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
          }

          // Return a success response
          // The actual streaming will be handled by the component
          return { data: 'Streaming response initiated' };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Unknown error',
            },
          };
        }
      },
      invalidatesTags: ['Chat' as const],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useSendChatMessageMutation,
} = chatApi;

// Export a utility function to handle streaming chat responses
export const streamChatResponse = async (
  chatRequest: ChatRequest,
  token: string,
  onDataChunk: (chunk: string) => void
): Promise<void> => {
  try {
    const baseUrl = import.meta.env.VITE_API_URL;
    const response = await fetch(`${baseUrl}/Chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(chatRequest),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is null');
    }

    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      onDataChunk(chunk);
    }
  } catch (error) {
    console.error('Error streaming chat response:', error);
    throw error;
  }
};
