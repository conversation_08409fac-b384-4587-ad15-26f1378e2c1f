# Example Usage: .\CreateSearchIndex.ps1 -ApiKey "kUMkUm42..........h9QhcAzSeDPpYU5" -IndexDefinitionJsonPath .\gptkbindex.json -SearchServiceName "corp-gallagheraius-srch-dev-ljluq"

param (
    [Parameter(Mandatory=$true)]
    [String]$ApiKey,

    [Parameter(Mandatory=$true)]
    [String]$IndexDefinitionJsonPath,

    [Parameter(Mandatory=$true)]
    [String]$SearchServiceName
)

$IndexDetails = Get-Content -Raw -Path $IndexDefinitionJsonPath | ConvertFrom-Json
$IndexName = $IndexDetails.name

$Uri = "https://$SearchServiceName.search.windows.net/indexes/$($IndexName)?api-version=2024-07-01"

$headers = @{
    "Content-Type" = "application/json"
    "api-key" = $ApiKey
}

Write-Host "Creating index $IndexName in search service $Uri..."


try {
    Invoke-RestMethod -Uri $Uri -Method Put -Headers $headers -Body (Get-Content -Raw -Path $IndexDefinitionJsonPath)

    Write-Host "Index Create / Update Success"
} catch {
    Write-Host "StatusCode:" $_.Exception.Response.StatusCode.value__ 
    Write-Host "StatusDescription:" $_.Exception.Response.StatusDescription

    $s = $_.Exception.Response.GetResponseStream()
    $s.Position = 0;
    $sr = New-Object System.IO.StreamReader($s)
    $err = $sr.ReadToEnd()
    $sr.Close()
    $s.Close()
    $ErrorDetails = $err | ConvertFrom-Json

    Write-Host "Error Code:" $ErrorDetails.error.code
    Write-Host "Error Message:" $ErrorDetails.error.message

}