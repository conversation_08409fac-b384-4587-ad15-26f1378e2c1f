import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import Cookies from "js-cookie";

export interface VersionState {
    isUpdateAvailable: boolean;
    currentVersion: string | null;
  }

const initialState: VersionState = {
  isUpdateAvailable: false,
  currentVersion: localStorage.getItem("version") || null,
};

// Thunk para verificar la versión actual
export const checkVersionChange = createAsyncThunk(
  "version/checkVersionChange",
  async () => {
    try {
      const response = await fetch("/version.json");
      const newVersion = await response.json();
      const savedVersion = localStorage.getItem("version");
      const cookie = Cookies.get("update");

      if (newVersion.version !== savedVersion) {
        localStorage.setItem("version", newVersion.version);
        Cookies.set("update", "false");
        return { isUpdateAvailable: true, currentVersion: newVersion.version };
      } else {
        return { isUpdateAvailable: cookie === "false", currentVersion: savedVersion };
      }
    } catch (error) {
      console.error("Error fetching version.json", error);
      return { isUpdateAvailable: false, currentVersion: null };
    }
  }
);

const versionSlice = createSlice({
  name: "version",
  initialState,
  reducers: {
    markUpdatesAsViewed: (state) => {
      Cookies.set("update", "true", { expires: 36500 });
      state.isUpdateAvailable = false;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(checkVersionChange.fulfilled, (state, action) => {
      state.isUpdateAvailable = action.payload.isUpdateAvailable;
      state.currentVersion = action.payload.currentVersion;
    });
  },
});

export const { markUpdatesAsViewed } = versionSlice.actions;
export default versionSlice.reducer;
