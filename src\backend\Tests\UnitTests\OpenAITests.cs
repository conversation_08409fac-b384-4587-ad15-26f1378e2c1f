﻿#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0001

using Moq;
using Backend.Models;
using Backend.Services;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace BackendUnitTests
{
    public class OpenAITests
    {
        private readonly OpenAISettings _openAISettings;
        private readonly List<ChatItem> _messages;
        private readonly List<ChatMessageContent> _sampleResponse;
        private readonly float[] _sampleEmbeddings = { 0.1f, 0.2f, 0.3f };

        /******************************************
         *       Initialize default objects
         ******************************************/

        public OpenAITests()
        {
            _openAISettings = new OpenAISettings
            {
                MaxTokens = 100,
                Temperature = 0.01f,
                TopP = 1.0f,
                FrequencyPenalty = 0.0f,
                PresencePenalty = 0.0f
            };

            _messages = new List<ChatItem>
            {
                new ChatItem
                {
                    Role = ChatRole.User,
                    Content = new List<Content>
                    {
                        new Content { Type = cType.Text, Value = "Hi there!" }
                    }
                }
            };

            _sampleResponse = new List<ChatMessageContent>
            {
                new ChatMessageContent { Content = "Sample response!" }
            };

        }

        /******************************************
        *                Tests
        ******************************************/

        [Fact]
        public async Task PromptAI_ReturnsText()
        {
            // Arrange
            var request = new Chat
            {
                Messages = _messages,
                Settings = new Settings()
                {
                    PromptTemplate = "template",
                    OpenAI = _openAISettings
                }
            };

            var openAIService = createOpenAIService(
                createMockSearchService(null),
                createMockChatService(_sampleResponse),
                createMockEmbeddingService(_sampleEmbeddings));

            // Act
            var result = openAIService.PromptAI(request, CancellationToken.None);

            // Assert
            var messages = await result.ToListAsync();
            Assert.Single(messages);
            Assert.Equal(cType.Text, messages[0].Type);
            Assert.Equal(_sampleResponse[0].Content, string.Concat(messages[0].Value));
        }

        [Fact]
        public async Task PromptAI_ReturnsFollowUpQuestions()
        {
            // Arrange
            var request = new Chat
            {
                Messages = _messages,
                Settings = new Settings()
                {
                    PromptTemplate = "template",
                    OpenAI = _openAISettings,
                    SuggestFollowupQuestions = true
                }
            };

            var openAIService = createOpenAIService(
                createMockSearchService(null),
                createMockChatService(_sampleResponse),
                createMockEmbeddingService(_sampleEmbeddings));

            // Act
            var result = openAIService.PromptAI(request, CancellationToken.None);

            // Assert
            var messages = await result.ToListAsync();
            Assert.Equal(2, messages.Count);
            Assert.Equal(cType.Text, messages[0].Type);
            Assert.Equal(cType.Follow_Up_Questions, messages[1].Type);
            Assert.Equal(_sampleResponse[0].Content, string.Concat(messages[0].Value));
            Assert.IsAssignableFrom<IEnumerable<string>>(messages[1].Value);
        }

        [Fact]
        public async Task PromptAIAboutDocumentation_ReturnsContent()
        {

            // Arrange
            var request = new Chat
            {
                Messages = _messages,
                Settings = new Settings
                {
                    PromptTemplate = "template",
                    Search = new SearchSettings
                    {
                        RetrievalMode = RetrievalMode.Text
                    },
                    OpenAI = _openAISettings,
                    SuggestFollowupQuestions = false
                }
            };

            var openAIService = createOpenAIService(
                createMockSearchService(
                    [new SectionItem("file.pdf#page=1", "Paris is the capital of France.")]),
                createMockChatService(
                    new List<ChatMessageContent> { 
                        new ChatMessageContent { 
                            Content = "The capital of France is Paris. <!-- Page=file.pdf#page=1 -->" } }),

                createMockEmbeddingService(_sampleEmbeddings));

            // Act
            var result = openAIService.PromptAIAboutDocumentation(request, CancellationToken.None);
            var messages = await result.ToListAsync();

            // Assert
            Assert.Equal(2, messages.Count);
            Assert.Equal(cType.Text, messages[0].Type);
            Assert.Equal(cType.Citations, messages[1].Type);
            Assert.Equal("The capital of France is Paris. [1]", messages[0].Value);
            Assert.Equal(new List<string> { "1. file.pdf#page=1" }, messages[1].Value);
        }

        [Fact]
        public async Task PromptAIAboutDocumentation_HandlesLargeContext()
        {
            // Arrange
            var request = new Chat
            {
                Messages = _messages,
                Settings = new Settings
                {
                    PromptTemplate = "template",
                    Search = new SearchSettings
                    {
                        RetrievalMode = RetrievalMode.Text
                    },
                    OpenAI = _openAISettings,
                    SuggestFollowupQuestions = false
                }
            };

            var largeContext = new List<SectionItem>();
            string longContent = new string('A', 3000);

            for (int i = 1; i <= 100; i++)
            {
                largeContext.Add(new SectionItem($"file1.pdf#page={i}", $"Content from file 1, page {i}. {longContent}"));
                largeContext.Add(new SectionItem($"file2.pdf#page={i}", $"Content from file 2, page {i}. {longContent}"));
            }

            var openAIService = createOpenAIService(
                createMockSearchService(largeContext.ToArray()),
                createMockChatService(new List<ChatMessageContent> { new ChatMessageContent { Content = "Aggregated response from multiple files. <!-- Page=file1.pdf#page=3 -->" } }),
                createMockEmbeddingService(_sampleEmbeddings));

            // Act
            var result = openAIService.PromptAIAboutDocumentation(request, CancellationToken.None);
            var messages = await result.ToListAsync();

            // Assert
            Assert.True(messages.Count > 1); // Ensure multiple messages are returned
            Assert.Contains(messages, m => m.Type == cType.Text);
            Assert.Contains(messages, m => m.Type == cType.Citations);
        }

        [Fact]
        public void GetTextWithCitations_ShouldReturnCorrectContentAndCitations()
        {
            // Arrange
            var content = @"Here are the details of the quote:

            1. **Addressed to**: Mr. XXX, Gallagher Cyber Practice, ARTHUR J GALLAGHER RISK MNGT SERV, 300 S RIVERSIDE PLZ STE 1500, CHICAGO, IL 60606-6637. <!-- Page=quote_v2.pdf#page=1 -->
            2. **Applicant**: National Council of YMCAs of the USA, 101 N. Wacker Drive, Suite 1600, Chicago, IL 60606. <!-- Page=quote_v2.pdf#page=2 -->
            3. **Producer Information**: ARTHUR J GALLAGHER RISK MNGT SERV, 300 S RIVERSIDE PLZ STE 1500, CHICAGO, IL 60606-6637. <!-- Page=quote_v2.pdf#page=2 -->
            4. **Policy Amount**: Aggregate Limit of Liability for all coverages is $5,000,000. <!-- Page=quote_v2.pdf#page=3 -->
            5. **Policy Premium**: $64,000. <!-- Page=quote_v2.pdf#page=3 -->
            6. **Policy Number**: 123123123. <!-- Page=quote_v2.pdf#page=5 -->
            7. **Effective Date**: 03/31/2024. <!-- Page=quote_v2.pdf#page=2 -->
            8. **Expiration Date**: 03/31/2025. <!-- Page=quote_v2.pdf#page=2 -->

            These details are extracted from the provided document pages.";

            var expectedText = @"Here are the details of the quote:

            1. **Addressed to**: Mr. XXX, Gallagher Cyber Practice, ARTHUR J GALLAGHER RISK MNGT SERV, 300 S RIVERSIDE PLZ STE 1500, CHICAGO, IL 60606-6637. [1]
            2. **Applicant**: National Council of YMCAs of the USA, 101 N. Wacker Drive, Suite 1600, Chicago, IL 60606. [2]
            3. **Producer Information**: ARTHUR J GALLAGHER RISK MNGT SERV, 300 S RIVERSIDE PLZ STE 1500, CHICAGO, IL 60606-6637. [2]
            4. **Policy Amount**: Aggregate Limit of Liability for all coverages is $5,000,000. [3]
            5. **Policy Premium**: $64,000. [3]
            6. **Policy Number**: 123123123. [4]
            7. **Effective Date**: 03/31/2024. [2]
            8. **Expiration Date**: 03/31/2025. [2]

            These details are extracted from the provided document pages.";

            var expectedCitations = new List<string>
            {
                "1. quote_v2.pdf#page=1",
                "2. quote_v2.pdf#page=2",
                "3. quote_v2.pdf#page=3",
                "4. quote_v2.pdf#page=5"
            };

            var openAIService = createOpenAIService(
                createMockSearchService(null),
                createMockChatService(_sampleResponse),
                createMockEmbeddingService(_sampleEmbeddings));

            // Use reflection to access the private method
            var methodInfo = typeof(OpenAIService).GetMethod("GetTextWithCitations", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.NotNull(methodInfo);

            // Act
            var result = (IEnumerable<Content>)methodInfo.Invoke(openAIService, new object[] { content })!;
            var resultList = result.ToList();

            // Assert
            Assert.Equal(2, resultList.Count);
            Assert.Equal(cType.Text, resultList[0].Type);
            Assert.Equal(expectedText, resultList[0].Value);
            Assert.Equal(cType.Citations, resultList[1].Type);
            Assert.Equal(expectedCitations, resultList[1].Value);
        }

        /******************************************
         * Helper functions to create mock objects
         ******************************************/

        private Mock<IChatCompletionService> createMockChatService(List<ChatMessageContent> result)
        {
            var mockChatService = new Mock<IChatCompletionService>();
            mockChatService
                .Setup(service => service.GetChatMessageContentsAsync(
                    It.IsAny<ChatHistory>(),
                    It.IsAny<OpenAIPromptExecutionSettings>(),
                    It.IsAny<Kernel>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult<IReadOnlyList<ChatMessageContent>>(result));
            return mockChatService;
        }

        private Mock<ISearchService> createMockSearchService(SectionItem[]? result)
        {
            var mockSearchService = new Mock<ISearchService>();
            if (result == null) { return mockSearchService; }
            mockSearchService
                .Setup(s => s.QueryDocumentsAsync(
                    It.IsAny<string>(),
                    It.IsAny<SearchSettings>(),
                    It.IsAny<string>(),
                    It.IsAny<float[]>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);
            return mockSearchService;
        }

        private Mock<ITextEmbeddingGenerationService> createMockEmbeddingService(float[] result)
        {
            var mockEmbeddingService = new Mock<ITextEmbeddingGenerationService>();
            mockEmbeddingService
                .Setup(s => s.GenerateEmbeddingsAsync(
                    It.IsAny<IList<string>>(),
                    It.IsAny<Kernel>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ReadOnlyMemory<float>> { result });
            return mockEmbeddingService;
        }

        private OpenAIService createOpenAIService(Mock<ISearchService> searchService,
                                                  Mock<IChatCompletionService> chatService,
                                                  Mock<ITextEmbeddingGenerationService> embeddingService)
        {
            var kernelBuilder = Kernel.CreateBuilder();

            kernelBuilder.Services.AddSingleton(chatService.Object);
            kernelBuilder.Services.AddSingleton(embeddingService.Object);

            var kernel = kernelBuilder.Build();

            return new OpenAIService(
                kernel,
                searchService.Object);
        }


        [Fact]
        public async Task PromptAI_ReturnsNoFollowupQuestions_WhenResponseContentIsNull()
        {
            // Arrange
            var request = new Chat
            {
                Messages = _messages,
                Settings = new Settings
                {
                    PromptTemplate = "template",
                    OpenAI = _openAISettings,
                    SuggestFollowupQuestions = true
                }
            };

            var nullContentResponse = new List<ChatMessageContent>
    {
        new ChatMessageContent { Content = null }
    };

            var openAIService = createOpenAIService(
                createMockSearchService(null),
                createMockChatService(nullContentResponse),
                createMockEmbeddingService(_sampleEmbeddings)
            );

            // Act
            var result = openAIService.PromptAI(request, CancellationToken.None);
            var messages = await result.ToListAsync();

            // Assert
            Assert.Single(messages);
            Assert.Equal(cType.Text, messages[0].Type);
            Assert.Equal("I do not know.", string.Concat(messages[0].Value));
        }

    }
}