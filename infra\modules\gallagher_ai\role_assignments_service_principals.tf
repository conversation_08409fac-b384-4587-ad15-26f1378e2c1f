# *******************************************************************
# CICD Service Principal access to resources (for deployment of code)
# *******************************************************************

resource "azurerm_role_assignment" "cicd_contributors" {
  count                = length(var.rbac_contributors)
  scope                = var.azurerm_resource_group_id
  role_definition_name = "AJG Contributor"
  principal_id         = var.rbac_contributors[count.index]
  description          = "Managed by Terraform - Allows the contributor group to perform all actions on the resource group"
}

# *******************************************************************
#				Tf account for Terraform related tasks
# *******************************************************************

resource "azurerm_role_assignment" "storage_account_contributor" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Account Contributor"
  principal_id         = var.current_principal_id # the princpal_id of the current user
  description          = "Managed by Terraform - Permits management of storage accounts."
}

resource "azurerm_role_assignment" "storage_blob_data_owner" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Owner"
  principal_id         = var.current_principal_id # the princpal_id of the current user
  description          = "Managed by Terraform - Provides full access to Azure Storage blob containers and data."
}

resource "azurerm_role_assignment" "contributor_to_storage" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = var.current_principal_id # the princpal_id of the current user
  description          = "Managed by Terraform - Read, write, and delete Azure Storage containers and blobs."
}

# *****************************************
#	 Additional Regional Storage Access
# *****************************************

resource "azurerm_role_assignment" "add_storage_account_contributor" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Account Contributor"
  principal_id         = var.current_principal_id # the princpal_id of the current user
  description          = "Managed by Terraform - Permits management of storage accounts."
}

resource "azurerm_role_assignment" "add_storage_blob_data_owner" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Owner"
  principal_id         = var.current_principal_id # the princpal_id of the current user
  description          = "Managed by Terraform - Provides full access to Azure Storage blob containers and data."
}

resource "azurerm_role_assignment" "add_contributor_to_storage" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = var.current_principal_id # the princpal_id of the current user
  description          = "Managed by Terraform - Read, write, and delete Azure Storage containers and blobs."
}

# *****************************************************************************

# *****************************************
#			Primary Storage Access
# *****************************************

resource "azurerm_role_assignment" "backend_storage_account_contributor" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Account Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Permits management of storage accounts."
}

resource "azurerm_role_assignment" "backend_storage_blob_data_owner" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Owner"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Provides full access to Azure Storage blob containers and data."
}

resource "azurerm_role_assignment" "backend_storage_blob_data_reader" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Read and list Azure Storage containers and blobs."
}

resource "azurerm_role_assignment" "backend_storage_blob_data_contributor" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Read, write, and delete Azure Storage containers and blobs."
}

resource "azurerm_role_assignment" "translation_to_storage_reader" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.cognitive-doc-translation.cognitive_account.identity[0].principal_id
  description          = "Managed by Terraform - Translation Service access to storage account where user files exist."
}

resource "azurerm_role_assignment" "translation_to_storage_contributor" {
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.cognitive-doc-translation.cognitive_account.identity[0].principal_id
  description          = "Managed by Terraform - Translation Service access to storage account where user files exist."
}


# *****************************************
#		   Doc Intelligence Access
# *****************************************

resource "azurerm_role_assignment" "backend_cog_svcs_contributor" {
  scope                = module.cognitive-services.cognitive_account.id
  role_definition_name = "Cognitive Services Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Contributor to Document Intelligence resource."
}

resource "azurerm_role_assignment" "backend_cog_svcs_user" {
  scope                = module.cognitive-services.cognitive_account.id
  role_definition_name = "Cognitive Services User"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Lets you read and list keys of Cognitive Services."
}

# *****************************************
#			Search Service Access
# *****************************************

resource "azurerm_role_assignment" "backend_search_service_data_contributor" {
  scope                = azurerm_search_service.search.id
  role_definition_name = "Search Index Data Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Grants full access to Index data."
}

resource "azurerm_role_assignment" "backend_search_service_data_reader" {
  scope                = azurerm_search_service.search.id
  role_definition_name = "Search Index Data Reader"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Grants read access to Azure Cognitive Search index data."
}

# *****************************************
#		  Translator Service Access
# *****************************************

resource "azurerm_role_assignment" "backend_translation_contributor" {
  scope                = module.cognitive-doc-translation.cognitive_account.id
  role_definition_name = "Cognitive Services Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Contributor to Document Intelligence resource."
}

resource "azurerm_role_assignment" "backend_translation_user" {
  scope                = module.cognitive-doc-translation.cognitive_account.id
  role_definition_name = "Cognitive Services User"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Lets you read and list keys of Cognitive Services."
}

#resource "azurerm_role_assignment" "backend_web_search_reader" {
#  count                = length(var.rbac_devadmin)
#  scope                = module.cognitive-bing-search.cognitive_account.id
#  role_definition_name = "Search Index Data Reader"
#  principal_id         = var.rbac_devadmin[count.index]
#  description          = "Managed by Terraform - Read-only access for querying search indexes."
#}

#resource "azurerm_role_assignment" "backend_web_search_contributor" {
#  count                = length(var.rbac_devadmin)
#  scope                = module.cognitive-bing-search.cognitive_account.id
#  role_definition_name = "Search Index Data Contributor"
#  principal_id         = var.rbac_devadmin[count.index]
#  description          = "Managed by Terraform - Read-write access to content in indexes."
#}

# *****************************************
#	 Additional Regional Storage Access
# *****************************************

resource "azurerm_role_assignment" "add_backend_storage_account_contributor" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Account Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Permits management of storage accounts."
}

resource "azurerm_role_assignment" "add_backend_storage_blob_data_owner" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Owner"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Provides full access to Azure Storage blob containers and data."
}

resource "azurerm_role_assignment" "add_backend_storage_blob_data_reader" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Read and list Azure Storage containers and blobs."
}

resource "azurerm_role_assignment" "add_backend_storage_blob_data_contributor" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Read, write, and delete Azure Storage containers and blobs."
}

resource "azurerm_role_assignment" "add_backend_search_service_data_contributor" {
  for_each             = azurerm_search_service.add_search
  scope                = each.value.id
  role_definition_name = "Search Index Data Contributor"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Grants full access to Index data."
}

resource "azurerm_role_assignment" "add_backend_search_service_data_reader" {
  for_each             = azurerm_search_service.add_search
  scope                = each.value.id
  role_definition_name = "Search Index Data Reader"
  principal_id         = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id
  description          = "Managed by Terraform - Grants read access to Azure Cognitive Search index data."
}

resource "azurerm_role_assignment" "add_translation_to_storage_reader" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.cognitive-doc-translation.cognitive_account.identity[0].principal_id
  description          = "Managed by Terraform - Translation Service access to storage account where user files exist."
}

resource "azurerm_role_assignment" "add_translation_to_storage_contributor" {
  for_each             = azurerm_storage_account.asr
  scope                = each.value.id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.cognitive-doc-translation.cognitive_account.identity[0].principal_id
  description          = "Managed by Terraform - Translation Service access to storage account where user files exist."
}