import React from "react";
import { DISCLAIMER_DATA, DISCLAIMER_HEADING } from "../../constants/constants";

const DisclaimerTab: React.FC = React.memo(() => {
  return (
    <div className="h-full flex justify-center bg-white dark:bg-zinc-800">
      <div className="flex flex-col h-full p-4 space-y-4 overflow-y-auto w-full max-w-3xl mx-auto">
        <h2 className="text-lg font-semibold text-left dark:text-white">{DISCLAIMER_HEADING}</h2>
        {DISCLAIMER_DATA.map((data: string, ind: number) => (
          <p className="dark:text-white" key={ind}>
            {data}
          </p>
        ))}
      </div>
    </div>
  );
});

export default DisclaimerTab;
