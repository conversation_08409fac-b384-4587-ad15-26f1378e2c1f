﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Contains the content of a chat item.
    /// </summary>
    [JsonConverter(typeof(ContentConverter))]
    public class Content
    {
        /// <summary>
        /// The type of chat item content (Text, Image, Follow_Up_Questions)
        /// </summary>
        [JsonPropertyName("type")]
        [JsonPropertyOrder(0)]
        public cType? Type { get; set; }

        /// <summary>
        /// The value of the chat item content.
        /// </summary>
        [JsonPropertyName("value")]
        [JsonPropertyOrder(1)]
        public object? Value { get; set; }
    }
}