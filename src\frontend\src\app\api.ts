import { createApi, fetchBaseQuery, FetchBaseQueryError } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store';
import { PublicClientApplication } from '@azure/msal-browser';
import { msalConfig, loginRequest } from '../authConfig';

// Create a custom fetch base query with retry logic for auth errors
const customFetchBaseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_API_URL,
  prepareHeaders: (headers, { getState }) => {
    // Get the token from the Redux store
    const token = (getState() as RootState).token.token;

    // If we have a token, add it to the headers
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  },
});

// Base API configuration
export const api = createApi({
  reducerPath: 'api',
  baseQuery: async (args, api, extraOptions) => {
    // First attempt with current token
    let result = await customFetchBaseQuery(args, api, extraOptions);

    // If we get a 401 Unauthorized error, try to refresh the token
    if (result.error && (result.error as FetchBaseQueryError).status === 401) {
      try {
        // Initialize MSAL
        const pca = new PublicClientApplication(msalConfig);
        await pca.initialize();

        // Get the active account
        const accounts = pca.getAllAccounts();
        if (accounts.length > 0) {
          // Try to silently acquire a new token
          const tokenResponse = await pca.acquireTokenSilent({
            ...loginRequest,
            account: accounts[0],
          });

          // If we got a new token, update the Redux store
          if (tokenResponse) {
            // Dispatch an action to update the token in the Redux store
            // This will be picked up by the next API call
            api.dispatch({
              type: 'token/setToken',
              payload: tokenResponse.accessToken
            });
          }
        }
      } catch (error) {
        console.error('Error refreshing token in RTK Query:', error);
      }
    }

    return result;
  },
  // Define tag types for cache invalidation
  tagTypes: ['Workspaces', 'Documents', 'Chat', 'Settings', 'Translation'],
  endpoints: () => ({}),
});

// Export hooks for usage in components
export const enhancedApi = api;
