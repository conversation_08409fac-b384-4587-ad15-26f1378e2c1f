import { useState } from "react";
import { ToastProps } from "../interfaces";

const useToast = () => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const triggerToast = (config: Omit<ToastProps, 'id' | 'onClose'>) => {
    const id = Date.now();
    const onClose = () => closeToast(id);
    setToasts((prevToasts) => [...prevToasts, { ...config, id, onClose }]);
    setTimeout(() => closeToast(id), (config.duration || 3) * 1000);
  };

  const closeToast = (id: number) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  };

  return { toasts, triggerToast, closeToast };
};

export default useToast;