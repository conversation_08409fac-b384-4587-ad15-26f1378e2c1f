﻿using Microsoft.SemanticKernel.ChatCompletion;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Represents a chat item (e.g. a user's question or a response from the LLM).
    /// </summary>
    [JsonConverter(typeof(ChatItemConverter))]
    public class ChatItem
    {
        /// <summary>
        /// Describes who made chat item. (e.g. User, System, Assistant, Tool)
        /// </summary>
        [JsonIgnore]
        public ChatRole Role { get; set; }

        /// <summary>
        /// String representation of the Role property.
        /// </summary>
        [JsonPropertyName("role")]
        public string RoleString
        {
            get => Role.ToString();
            set
            {
                if (Enum.TryParse<ChatRole>(value, true, out var parsedRole))
                {
                    Role = parsedRole;
                }
                else if (int.TryParse(value, out var intRole) && Enum.IsDefined(typeof(ChatRole), intRole))
                {
                    Role = (ChatRole)intRole;
                }
                else
                {
                    throw new ArgumentException("Invalid value for Role");
                }
            }
        }

        /// <summary>
        /// Gets or sets the content of the chat item.
        /// </summary>
        [JsonPropertyName("content")] 
        public List<Content>? Content { get; set; }

        /// <summary>
        /// Gets the AuthorRole of the chat item.
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        public AuthorRole GetRole()
        {
            return Role switch
            {
                ChatRole.System => AuthorRole.System,
                ChatRole.Assistant => AuthorRole.Assistant,
                ChatRole.User => AuthorRole.User,
                ChatRole.Tool => AuthorRole.Tool,
                _ => throw new ArgumentOutOfRangeException()
            };
        }
    }
}
