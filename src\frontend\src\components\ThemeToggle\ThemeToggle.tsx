import React, { useEffect, useState } from "react";
import {
  WeatherSunny24Regular,
  WeatherMoon24Regular,
} from "@fluentui/react-icons";

const ThemeToggle: React.FC = () => {
  const [isDarkMode, setIsDarkMode] = useState(
    document.documentElement.classList.contains("dark")
  );

  const toggleTheme = () => {
    const newIsDarkMode = !isDarkMode;
    setIsDarkMode(newIsDarkMode);

    // Apply the global theme
    if (newIsDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Saves user's preference in localStorage
    localStorage.setItem("theme", newIsDarkMode ? "dark" : "light");

    // Triggers themechange event
    window.dispatchEvent(new Event("themechange"));
  };

  // Effect to load the user's preference when the app starts
  useEffect(() => {
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme === "dark") {
      document.documentElement.classList.add("dark");
      setIsDarkMode(true);
    } else if (savedTheme === "light") {
      document.documentElement.classList.remove("dark");
      setIsDarkMode(false);
    }
  }, []);

  return (
    <button
      onClick={toggleTheme}
      className="flex items-center justify-center w-8 h-10 rounded-full focus:outline-hidden cursor-pointer"
      title="Switch Theme"
    >
      {isDarkMode ? (
        <WeatherSunny24Regular className="text-gallagher-blue-300 hover:text-gallagher-blue-200" />
      ) : (
        <WeatherMoon24Regular className="text-gallagher-blue-500 hover:text-gallagher-blue-400" />
      )}
    </button>
  );
};

export default ThemeToggle;
