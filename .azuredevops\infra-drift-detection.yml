trigger:
 branches:
   exclude:
   - '*'

schedules:
  - cron: '0 8 * * 0'  # Runs at 01:20 every day
    displayName: Weekly Drift Detection check at 8 am every Sunday
    branches:
      include:
        - main
    always: true

resources:
  repositories:
    - repository: iac-pipeline
      type: git
      name: GTS-InfrastructureAsCode-CenterOfExcellence\Pipeline-GenericInfrastructureDeployment
      ref: refs/heads/release
      endpoint: IaC-CoE-v2-Connection

variables:
- group: IaC-CenterOfExcellence-Integration-Variables-v2
- template: infra-variables.yml

extends:
  template: drift-detection.yml@iac-pipeline
  parameters:
    environmentsToTarget:
    - prod_us
    repositoryName: GallagherAI
    version: v3
    terraformVersion: v1.11.1
    poolName: IaC-AzurePool-CORPUSPROD-v2
    terraformSecretMapper:
      TF_TOKEN_app_terraform_io: $(READ_TFE_TOKEN)
