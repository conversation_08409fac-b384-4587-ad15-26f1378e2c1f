﻿using Azure;
using Backend.Models;
using System.Text.RegularExpressions;
using Azure.AI.DocumentIntelligence;

namespace Backend.Services
{
    public sealed partial class DocumentIntelligenceService : IDocumentIntelligenceService
    {
        [GeneratedRegex("[^0-9a-zA-Z_-]")]
        private static partial Regex MatchInSetRegex();
        private static readonly char[] _sentenceEndings = new[] { '.', '!', '?' };
        private static readonly char[] _wordBreaks = new[] { ' ', ',', ';', ':', ')', ']', '}', '>', '\t', '\n' };

        private readonly DocumentIntelligenceClient _docClient;
        private readonly ILogger<DocumentIntelligenceService> _logger;
        private readonly int _sectionOverlap;

        public DocumentIntelligenceService(DocumentIntelligenceClient docClient,
                                           ILogger<DocumentIntelligenceService> logger,
                                           int sectionOverlap)
        {
            _docClient = docClient;
            _logger = logger;
            _sectionOverlap = sectionOverlap;
        }

        /// <summary>
        /// Get the text from the given document stream.
        /// </summary>
        public async Task<string> GetDocumentTextAsync(MemoryStream stream)
        {
            List<PageDetail> pageMap = [];
            stream.Seek(0, SeekOrigin.Begin);

            // Create a CancellationTokenSource with a 60-second timeout
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(60));
            
            try
            {
                //Analyze the Document
                Operation<AnalyzeResult> operation;
                var options = new AnalyzeDocumentOptions("prebuilt-layout", BinaryData.FromStream(stream));
                options.OutputContentFormat = DocumentContentFormat.Markdown;

                operation = await _docClient.AnalyzeDocumentAsync(
                    WaitUntil.Completed,
                    options,
                    cancellationToken: cts.Token);

                string content = operation.Value.Content;

                // Split the content into pages
                var pages = Regex.Split(content, @"(?<=<!-- PageBreak -->)");

                // Process the pages
                FormatPages(pages);
                content = string.Join("\n", pages);
                content = content.Replace("\n\n\n", "\n\n");
                content = content.Replace("\n\n\n", "\n\n");
                content = content.Replace("\n\n\n", "\n\n");

                return content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Formats headers, footers on each page.
        /// </summary>
        private static string[] FormatPages(string[] pages)
        {
            for (int i = 0; i < pages.Length; i++)
            {
                string[] lines = pages[i].Split('\n').ToArray();
                lines = CollapseHeaders(lines);
                lines = CollapseFooters(lines);

                var headerLines = lines.Where(
                    line => Regex.IsMatch(line, "<!-- PageHeader")).ToArray();

                var footerLines = lines.Where(
                    line => Regex.IsMatch(line, "<!-- PageFooter") ||
                    Regex.IsMatch(line, "<!-- PageBreak -->")).ToArray();

                var content = lines.Where(
                              line => !Regex.IsMatch(line, "<!-- PageHeader") && 
                              !Regex.IsMatch(line, "<!-- PageFooter") && 
                              !Regex.IsMatch(line, "<!-- PageBreak -->") && 
                              !Regex.IsMatch(line, "<!-- PageNumber=")).ToArray();

                pages[i] = string.Join("\n", headerLines) + "\n" + 
                           string.Join("\n", content) + "\n" + 
                           string.Join("\n", footerLines);
            }

            return pages;
        }

        /// <summary>
        /// Collapses/Groups headers together. Also removes pageNumbers.
        /// </summary>
        private static string[] CollapseHeaders(string[] lines)
        {
            int header = Array.FindIndex(lines, line => Regex.IsMatch(line, @"<!-- PageHeader="));
            for (int j = header; j >= 0; j--)
            {
                if (j == 0 && (lines[j] == "" || 
                    lines[j] == "<figure>" || 
                    lines[j] == "</figure>" || 
                    Regex.IsMatch(lines[j], @"<!-- PageNumber=")))
                {
                    lines = lines[1..];
                }
                else if (lines[j] == "" || 
                         lines[j] == "<figure>" || 
                         lines[j] == "</figure>" || 
                         Regex.IsMatch(lines[j], @"<!-- PageNumber="))
                {
                    lines = lines[..j].Concat(lines[(j + 1)..]).ToArray();
                }
                else if (j != header && !Regex.IsMatch(lines[j], @"^<!--.*-->$"))
                {
                    lines[j] = $"<!-- PageHeader=\"{lines[j]}\" -->";
                }
            }
            return lines;
        }

        /// <summary>
        /// Collapses/Groups footers together. Also removes pageNumbers.
        /// </summary>
        private static string[] CollapseFooters(string[] lines)
        {
            int footer = Array.FindIndex(lines, line => Regex.IsMatch(line, @"<!-- PageFooter="));
            int midpoint = lines.Length / 2;

            if (footer != 0 && ((lines.Length - footer) < Math.Abs(footer - midpoint)))
            {
                for (int j = lines.Length - 1; j >= footer; j--)
                {
                    if (j == lines.Length - 1 && 
                        (lines[j] == "" || 
                         lines[j] == "<figure>" || 
                         lines[j] == "</figure>" || 
                         Regex.IsMatch(lines[j], @"<!-- PageNumber=")))
                    {
                        lines = lines[..^1];
                    }
                    else if (lines[j] == "" || 
                             lines[j] == "<figure>" || 
                             lines[j] == "</figure>" || 
                             Regex.IsMatch(lines[j], @"<!-- PageNumber="))
                    {
                        lines = lines[..j].Concat(lines[(j + 1)..]).ToArray();
                    }
                    else if (!Regex.IsMatch(lines[j], @"^<!--.*-->$"))
                    {
                        lines[j] = $"<!-- PageFooter=\"{lines[j]}\" -->";
                    }
                }
            }
            return lines;
        }

        /// <summary>
        /// Creates a list of sections from textual content.
        /// </summary>
        public IEnumerable<Section> CreateSections(string content,
                                                   string oid,
                                                   string workspaceId,
                                                   string blob,
                                                   int attempt = 0)
        {
            if (_logger.IsEnabled(LogLevel.Information))
            {
                _logger.LogInformation("Splitting '{Blob}' into sections by page", blob);
            }

            var pages = Regex.Split(content, @"(?<=<!-- PageBreak -->)");
            var max = Math.Max(10_000, 20_000 - (2_500 * attempt));
            Sequence sequence = new Sequence { i = 1 };

            for (int i = 0; i < pages.Length; i++)
            {            
                foreach (var section in SplitIntoSections(pages[i], blob, oid, workspaceId, sequence, (i + 1), max))
                {
                    yield return section;
                }
            }
        }

        /// <summary>
        /// Splits the given text into sections.
        /// </summary>
        private IEnumerable<Section> SplitIntoSections(string pageText,
                                                       string blob,
                                                       string oid,
                                                       string workspaceId,
                                                       Sequence sequence,
                                                       int page,
                                                       int max = 20_000)
        {
            int i = 0;
            int cLength;
            int splitPoint;
            string currentSectionText;

            while (i < pageText.Length)
            {
                if (pageText.Length - i > max)
                {
                    cLength = Math.Min(max, pageText.Length - i);
                    splitPoint = FindSplitPoint(pageText[i..], cLength / 2, max);
                } 
                else
                {
                    splitPoint = pageText.Length - i;
                }

                currentSectionText = pageText.Substring(i, splitPoint);

                yield return new Section(
                    Id: MatchInSetRegex().Replace($"{blob}-{page}", "_").TrimStart('_'),
                    Content: currentSectionText,
                    Workspace: workspaceId,
                    SourcePage: blob.Replace(oid + "/" + workspaceId, "").Replace("/", "") + $"#page={page}",
                    SourceFile: blob,
                    Sequence: sequence.i);
                    
                i += splitPoint;
                sequence.i++;
            }
        }

        /// <summary>
        /// Returns a suitable split point in the provided text 
        /// that can be used to split the text into 2 sections.
        /// </summary>
        private int FindSplitPoint(string text, int mid, int max = 20_000)
        {
            int split = -1;

            split = text.IndexOf('\n', mid);
            if (split != -1 && split <= max-1)
            {
                return split + 1;
            }

            split = text.IndexOfAny(_sentenceEndings, mid);
            if (split != -1 && split <= max-1)
            {
                return split + 1; 
            }

            split = text.IndexOfAny(_wordBreaks, mid);
            if (split != -1 && split <= max-1)
            {
                return split + 1;
            }

            return mid;
        }
    }
}