﻿using Microsoft.AspNetCore.StaticFiles;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Backend.Models
{
    /// <summary>
    /// Represents a chat thread.
    /// </summary>
    public class Chat
    {
        /// <summary>
        /// MimeType provider.
        /// </summary>
        [JsonIgnore]
        private static FileExtensionContentTypeProvider _provider = new();

        /// <summary>
        /// Defines the workspace for the chat.  
        /// </summary>
        [JsonPropertyName("workspace_id")]
        public string? Workspace { get; set; }

        /// <summary>
        /// Gets or sets the settings for the chat.
        /// </summary>
        [JsonPropertyName("settings")]
        public Settings? Settings { get; set; }

        /// <summary>
        /// Gets or sets the message(s) to be sent to the AI.
        /// </summary>
        [JsonPropertyName("messages")]
        public IList<ChatItem> Messages { get; set; } = new List<ChatItem>();

        /// <summary>
        /// Gets the messages or list of chat items as a ChatHistory object.
        /// </summary>s
        /// <returns></returns>
        public ChatHistory GetChatHistory()
        {
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(Settings!.PromptTemplate!);

            foreach (var message in Messages)
            {
                var contentItems = new ChatMessageContentItemCollection();
                foreach (var content in message.Content!)
                {
                    switch (content.Type)
                    {
                        case cType.Text:
                            contentItems.Add(new TextContent((string)content.Value!));
                            break;
                        case cType.Image:
                            if (content.Value is IFormFile formFile)
                            {
                                using (var memoryStream = new MemoryStream())
                                {
                                    formFile.CopyTo(memoryStream);
                                    var imageBytes = memoryStream.ToArray();
                                    contentItems.Add(new ImageContent
                                    {
                                        Data = imageBytes,
                                        MimeType = _provider.TryGetContentType(formFile.FileName, out var mimeType) 
                                            ? mimeType 
                                            : throw new InvalidOperationException("Mime type not found.")
                                    });
                                }
                            }
                            else if (content.Value is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.String)
                            {
                                var value = jsonElement.GetString();
                                var match = Regex.Match(value!, @"^data:(?<type>.+?);base64,(?<data>.+)$");
                                contentItems.Add(new ImageContent
                                {
                                    Data = Convert.FromBase64String(match.Groups["data"].Value),
                                    MimeType = match.Groups["type"].Value
                                });
                            }
                            break;
                    }
                }
                chatHistory.AddMessage(message.GetRole(), contentItems);
            }
            return chatHistory;
        }

        /// <summary>
        /// Gets the user's last or most recent question/message.
        /// </summary>
        /// <returns></returns>
        public string GetUsersLastMessage()
        {
            var userMessage = Messages.LastOrDefault(m => m.Role == ChatRole.User) ??
                throw new InvalidOperationException("User question is null");

            var textItems = userMessage.Content?.Where(c => c.Type == cType.Text).Select(c => (string)c.Value!);
            if (textItems == null) { return string.Empty; }

            return string.Join("", textItems);
        }

        /// <summary>
        /// Returns the OpenAI settings.
        /// </summary>
        /// <returns></returns>
        public OpenAIPromptExecutionSettings GetOpenAISettings()
        {
            return new OpenAIPromptExecutionSettings
            {
                Temperature = Settings!.OpenAI!.Temperature ?? 0.01,
                MaxTokens = Settings.OpenAI.MaxTokens ?? 800,
                TopP = Settings.OpenAI.TopP ?? 0.95,
                FrequencyPenalty = Settings.OpenAI.FrequencyPenalty ?? 0,
                PresencePenalty = Settings.OpenAI.PresencePenalty ?? 0,
                FunctionChoiceBehavior = Settings.OpenAI.FunctionChoiceBehavior
            };
        }
    }
}
