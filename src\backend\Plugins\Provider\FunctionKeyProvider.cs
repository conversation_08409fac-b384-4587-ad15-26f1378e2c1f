﻿using Backend.Models;
using Microsoft.SemanticKernel;

namespace Backend.Plugins
{
    public class FunctionKeyProvider : IFunctionKeyProvider
    {
        public string GetFunctionKey(KernelFunction kernelFunction)
        {
            return !string.IsNullOrWhiteSpace(kernelFunction.PluginName) ?
                $"{kernelFunction.PluginName}-{kernelFunction.Name}" :
                kernelFunction.Name;
        }
    }
}
