using Backend.Startup;
using Backend.Services;
using Microsoft.AspNetCore.Authorization;

namespace Backend
{
    /// <summary>
    /// Main entry point for the application.
    /// </summary>
    internal class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Host + Logging Configuration
            builder.ConfigureKestrel();
            builder.ConfigureEnvironment();
            builder.ConfigureLogging();
            builder.ValidateConfiguration();

            // Service Configuration
            builder.Services.ConfigureHostOptions();
            builder.Services.ConfigureCors();
            builder.Services.ConfigureAuthentication(builder.Configuration);
            builder.Services.ConfigureControllers();
            builder.Services.ConfigureSwagger(builder.Configuration);
            builder.Services.ConfigureKernel(builder.Configuration);
            builder.Services.ConfigureBlobServices(builder.Configuration);
            builder.Services.ConfigureConversionService(builder.Configuration);
            builder.Services.ConfigureEncryptionService();
            builder.Services.ConfigureSearchService(builder.Configuration);
            builder.Services.ConfigureDocumentIntelligenceService(builder.Configuration);
            builder.Services.ConfigureTranslationService(builder.Configuration);            
            builder.Services.ConfigureTaskQueue(builder.Configuration);
            builder.Services.ConfigureTaskProcessingService(builder.Configuration);
            builder.Services.ConfigureOpenAIService();

            var app = builder.Build();

            // Add swagger if in development or local environment
            if (app.Environment.IsDevelopment() ||
                app.Environment.IsEnvironment("US-DEV") ||
                app.Environment.IsEnvironment("UK-DEV") ||
                app.Environment.IsEnvironment("AU-DEV") ||
                app.Environment.IsEnvironment("Local"))
            {
                app.UseSwagger();
                app.UseSwaggerUI();

                app.Use(async (context, next) =>
                {
                    if (context.Request.Path == "/")
                    {
                        context.Response.Redirect("/swagger");
                        return;
                    }
                    await next();
                });
            }

            //app.UseHsts();
            //app.UseHttpsRedirection();
            app.UseRouting();
            app.UseCors();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseMiddleware<ExceptionMiddleware>();

#pragma warning disable ASP0014
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers().RequireAuthorization();
                endpoints.MapHealthChecks("/healthz").WithMetadata(new AllowAnonymousAttribute());
                endpoints.MapGet("/", context =>
                {
                    context.Response.Redirect("/swagger");
                    return Task.CompletedTask;
                }).WithMetadata(new AllowAnonymousAttribute());
            });
#pragma warning restore ASP0014

            app.Run();
        }
    }
}
