import { Configuration } from "@azure/msal-browser";

export const msalConfig: Configuration = {
  auth: {
    clientId: import.meta.env.VITE_MSAL_CLIENT_ID, // Move to a config file
    authority: import.meta.env.VITE_MSAL_AUTHORITY, // Move to a config file
    redirectUri: window.location.origin, // Ensure this matches what is configured in Azure AD
  },
  cache: {
    cacheLocation: "localStorage", // This configures where the cache will be stored
    storeAuthStateInCookie: true, // Set this to "true" if you are having issues on IE11 or Edge
  },
};

export const loginRequest = {
  scopes: [import.meta.env.VITE_MSAL_SCOPES],
};