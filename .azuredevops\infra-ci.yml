trigger:
 branches:
   include:
   - '*'

resources:
  repositories:
    - repository: iac-pipeline
      type: git
      name: GTS-InfrastructureAsCode-CenterOfExcellence\Pipeline-GenericInfrastructureDeployment
      ref: refs/heads/release
      endpoint: IaC-CoE-v2-Connection

variables:
- group: IaC-CenterOfExcellence-Integration-Variables-v2
- template: infra-variables.yml

extends:
  template: continuous-integration.yml@iac-pipeline
  parameters:
    repositoryName: GallagherAI
    terraformVersion: v1.11.1
    version: v3
    poolName: IaC-AzurePool-CORPUSDEV-v2
    terraformSecretMapper:
      TF_TOKEN_app_terraform_io: $(READ_TFE_TOKEN)
