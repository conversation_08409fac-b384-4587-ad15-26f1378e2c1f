﻿using Azure.Storage.Blobs;
using Backend.Models;
using Backend.Models.Context;

namespace Backend.Services
{
    public class BlobStorageService : StorageService
    {
        private readonly ITaskQueue _taskQueue;

        public BlobStorageService(Dictionary<string, BlobContainerClient> containerClients, int days, ITaskQueue taskQueue)
            : base(containerClients, days)
        {
            _taskQueue = taskQueue;
        }

        /// <summary>
        /// Deletes an object from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <returns></returns>
        public override async Task DeleteBlobAsync(string prefix, bool isFile = true)
        {
            BlobClient blobClient = GetContainerClient().GetBlobClient(prefix);
            await blobClient.DeleteAsync();
            if (isFile)
            {
                var taskItem = new TaskItem(blobClient.Name, "RemoveFromIndex", CurrentContext.User);
                await _taskQueue.QueueItemAsync(taskItem);
            }
        }

        /// <summary>
        /// Uploads a document to Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the document</param>
        /// <param name="workspaceId">The workspace the document falls under</param>
        /// <param name="fileName">The name of the document</param>
        /// <param name="documentStream">Stream object holding document content</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        public override async Task<Document> UploadDocumentsAsync(string oid,
            string workspaceId,
            string fileName,
            Stream documentStream,
            CancellationToken ct,
            string original = ".pdf")
        {
            var result = new Document();
            var blobClient = GetContainerClient().GetBlobClient(oid + "/" + workspaceId + "/" + fileName);
            if (ct.IsCancellationRequested) { return result; }

            try
            {
                documentStream.Seek(0, SeekOrigin.Begin);
                await blobClient.UploadAsync(documentStream, cancellationToken: ct);
            }
            catch (Azure.RequestFailedException)
            {
                await DeleteBlobAsync(oid + "/" + workspaceId + "/" + fileName);
                documentStream.Seek(0, SeekOrigin.Begin);
                await blobClient.UploadAsync(documentStream, cancellationToken: ct);
            }

            await blobClient.SetMetadataAsync(new Dictionary<string, string>
            {
                { "processed", Processed.False.ToString().ToLower() },
                { "original", $"{(original == ".pdf").ToString().ToLower()}" }
            });

            if (ct.IsCancellationRequested) { return result; }
            if (Path.GetExtension(fileName).ToLower() == ".pdf")
            {
                var taskItem = new TaskItem(
                    oid + "/" + workspaceId + "/" + fileName,
                    "ComputeEmbeddings",
                    CurrentContext.User,
                    original);

                await _taskQueue.QueueItemAsync(taskItem);
            }

            result = new Document
            {
                Name = fileName,
                Processed = Processed.False,
                Expires = DateTime.UtcNow.AddDays(_days).ToString("MM/dd/yyyy")!
            };
            _ = Refresh(oid + "/" + workspaceId);
            return result;
        }
    }
}