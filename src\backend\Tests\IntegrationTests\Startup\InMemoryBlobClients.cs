﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using Azure.Core;
using System.Diagnostics.CodeAnalysis;

namespace BackendIntegrationTests
{
    public class InMemoryBlobContainerClient : BlobContainerClient
    {
        private readonly ConcurrentDictionary<string, InMemoryBlobClient> _blobs = new();

        public override BlobClient GetBlobClient(string blobName)
        {
            return _blobs.GetOrAdd(blobName, name => new InMemoryBlobClient(name));
        }

        public override AsyncPageable<BlobItem> GetBlobsAsync(
            BlobTraits traits = BlobTraits.None,
            BlobStates states = BlobStates.None,
            string prefix = null!,
            CancellationToken cancellationToken = default)
        {
            async IAsyncEnumerable<BlobItem> GetBlobsAsyncEnumerable([EnumeratorCancellation] CancellationToken ct)
            {
                foreach (var blob in _blobs.Values)
                {
                    yield return BlobsModelFactory.BlobItem(
                        blob.Name,
                        properties: BlobsModelFactory.BlobItemProperties(false),
                        metadata: new Dictionary<string, string>());
                }
            }
            return null!;
        }
    }

    public class InMemoryBlobClient : BlobClient
    {
        private readonly MemoryStream _stream = new();
        private readonly Dictionary<string, string> _metadata = new();

        public InMemoryBlobClient(string name)
        {
            Name = name;
        }

        public override string Name { get; }

        public override async Task<Response<BlobContentInfo>> UploadAsync(
            Stream content,
            CancellationToken cancellationToken = default)
        {
            await content.CopyToAsync(_stream, cancellationToken);
            _stream.Position = 0;
            var blobContentInfo = BlobsModelFactory.BlobContentInfo(
                eTag: new ETag("etag"),
                lastModified: DateTimeOffset.UtcNow,
                contentHash: new byte[0],
                blobSequenceNumber: 0,
                encryptionKeySha256: null,
                encryptionScope: null,
                versionId: null
            );
            return Response.FromValue(blobContentInfo, new MockResponse());
        }

        public override Response<bool> DeleteIfExists(
            DeleteSnapshotsOption snapshotsOption = DeleteSnapshotsOption.None,
            BlobRequestConditions conditions = null!,
            CancellationToken cancellationToken = default)
        {
            _stream.SetLength(0);
            return Response.FromValue(true, new MockResponse());
        }

        public async Task<Response> DeleteAsync(CancellationToken cancellationToken = default)
        {
            _stream.SetLength(0);
            return await Task.FromResult(Response.FromValue(new MockResponse(), new MockResponse()));
        }

        public async Task<Response<BlobProperties>> GetPropertiesAsync()
        {
            var properties = BlobsModelFactory.BlobProperties(
                lastModified: DateTimeOffset.UtcNow, // Set the LastModified property
                metadata: _metadata
            );
            return await Task.FromResult(Response.FromValue(properties, new MockResponse()));
        }

        public override async Task<Response<BlobInfo>> SetMetadataAsync(
            IDictionary<string, string> metadata,
            BlobRequestConditions conditions = null!,
            CancellationToken cancellationToken = default)
        {
            foreach (var kvp in metadata)
            {
                _metadata[kvp.Key] = kvp.Value;
            }
            var blobInfo = BlobsModelFactory.BlobInfo(
                eTag: new ETag("etag"),
                lastModified: DateTimeOffset.UtcNow
            );
            return await Task.FromResult(Response.FromValue(blobInfo, new MockResponse()));
        }
    }

    public class MockResponse : Response
    {
        public override int Status => 200;
        public override string ReasonPhrase => "OK";
        public override Stream? ContentStream { get; set; }
        public override string ClientRequestId { get; set; } = string.Empty;
        public override void Dispose() { }
        protected override bool TryGetHeader(
            string name, 
            [NotNullWhen(true)] out string? value) { value = null; return false; }
        protected override bool TryGetHeaderValues(
            string name, 
            [NotNullWhen(true)] out IEnumerable<string>? values) { values = null; return false; }
        protected override bool ContainsHeader(string name) => false;
        protected override IEnumerable<HttpHeader> EnumerateHeaders() => Enumerable.Empty<HttpHeader>();
    }
}