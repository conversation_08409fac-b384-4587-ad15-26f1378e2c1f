# Redux and RTK Query Implementation

This directory contains the core Redux configuration and RTK Query implementation for the application.

## Overview

The application uses Redux Toolkit for state management and RTK Query for API calls. This approach provides several benefits:

- **Centralized State Management**: All application state is managed in a single store
- **Automatic Caching**: RTK Query automatically caches API responses
- **Automatic Loading and Error States**: RTK Query provides loading and error states for API calls
- **Automatic Refetching**: RTK Query can automatically refetch data when needed
- **TypeScript Integration**: Full TypeScript support for type safety

## Directory Structure

- `app/api.ts`: Base RTK Query API configuration
- `app/hooks.ts`: Typed hooks for using Redux
- `services/api/`: API service implementations
  - `workspacesApi.ts`: Workspaces API
  - `documentsApi.ts`: Documents API
  - `chatApi.ts`: Chat API
  - `translationApi.ts`: Translation API
  - `settingsApi.ts`: Settings API (local IndexedDB)

## Usage

### Using Redux State

To access Redux state in a component, use the `useAppSelector` hook:

```tsx
import { useAppSelector } from '../app/hooks';

const MyComponent = () => {
  const user = useAppSelector((state) => state.user);

  return <div>Hello, {user.name}</div>;
};
```

### Dispatching Actions

To dispatch actions, use the `useAppDispatch` hook:

```tsx
import { useAppDispatch } from '../app/hooks';
import { setToken } from '../features/tokenSlice';

const MyComponent = () => {
  const dispatch = useAppDispatch();

  const handleLogin = (token: string) => {
    dispatch(setToken(token));
  };

  return <button onClick={() => handleLogin('new-token')}>Login</button>;
};
```

### Using RTK Query APIs

RTK Query provides hooks for each API endpoint:

```tsx
import { useGetWorkspacesQuery, useCreateWorkspaceMutation } from '../services/api/workspacesApi';

const WorkspacesComponent = () => {
  // Query hook provides data, loading, and error states
  const { data: workspaces, isLoading, error } = useGetWorkspacesQuery();

  // Mutation hook provides trigger function and mutation states
  const [createWorkspace, { isLoading: isCreating }] = useCreateWorkspaceMutation();

  const handleCreate = async () => {
    try {
      await createWorkspace({ name: 'New Workspace' }).unwrap();
    } catch (error) {
      console.error('Failed to create workspace:', error);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading workspaces</div>;

  return (
    <div>
      {workspaces?.map(workspace => (
        <div key={workspace.id}>{workspace.name}</div>
      ))}
      <button onClick={handleCreate} disabled={isCreating}>
        Create Workspace
      </button>
    </div>
  );
};
```

### Authentication Integration

Use the `useApiWithAuth` hook to ensure API calls have valid authentication:

```tsx
import useApiWithAuth from '../hooks/useApiWithAuth';

const AuthenticatedComponent = () => {
  const { isAuthenticated, refreshToken } = useApiWithAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      refreshToken();
    }
  }, [isAuthenticated, refreshToken]);

  if (!isAuthenticated) {
    return <div>Authenticating...</div>;
  }

  return <div>Authenticated content</div>;
};
```

## Best Practices

### 1. Always Use Typed Hooks
```tsx
// ✅ Good - Type safe
import { useAppSelector, useAppDispatch } from '../app/hooks';

// ❌ Avoid - No type safety
import { useSelector, useDispatch } from 'react-redux';
```

### 2. Handle Loading and Error States
```tsx
const { data, isLoading, error } = useGetWorkspacesQuery();

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
```

### 3. Use Authentication Hook
```tsx
// Always use in components that make API calls
const { isAuthenticated, refreshToken } = useApiWithAuth();
```

### 4. Cache Invalidation
```tsx
// Mutations automatically invalidate related cache
const [deleteWorkspace] = useDeleteWorkspaceMutation();
// This will automatically refetch workspaces list
```

## Performance Tips

1. **Selective Subscriptions**: Only subscribe to the state you need
2. **Skip Queries**: Use `skip` option to conditionally run queries
3. **Polling**: Use `pollingInterval` for real-time data
4. **Prefetching**: Use `usePrefetch` for anticipated data needs

## Troubleshooting

### Common Issues

1. **401 Errors**: Ensure `useApiWithAuth` is used in components making API calls
2. **Cache Issues**: Check tag invalidation in mutations
3. **Type Errors**: Verify API types match backend responses
4. **Loading States**: Ensure proper loading state handling in UI
