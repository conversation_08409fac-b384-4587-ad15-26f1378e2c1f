﻿using Azure.Storage.Blobs;
using Backend.Models;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureTaskProcessingService(this IServiceCollection services, IConfiguration config)
        {
            services.AddHostedService<TaskProcessingService>(sp =>
            {
                return new TaskProcessingService(
                    sp.GetRequiredService<ITaskQueue>(),
                    sp.GetRequiredService<ILogger<TaskProcessingService>>(),
                    sp.GetRequiredService<Dictionary<string, BlobServiceClient>>()
                        .ToDictionary(kvp => kvp.Key,
                                      kvp => kvp.Value.GetBlobContainerClient(config["AzureStorage:Container"])),
                    sp.GetRequiredService<IDocumentIntelligenceService>(),
                    sp.GetRequiredService<ISearchService>(),
                    config.GetValue<int>("Background:QueueSettings:Workers"));
            });
        }
    }
}
