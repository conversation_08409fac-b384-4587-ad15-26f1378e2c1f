import React from "react";
import { ConfirmToastProps } from "../../../interfaces";

const positionClasses = {
    "top-left": "top-0 left-0 mt-16 ml-6",
    "top-center": "top-0 left-1/2 transform -translate-x-1/2 mt-16",
    "top-right": "top-0 right-0 mt-16 mr-6",
    "mid-left": "top-1/2 left-0 transform -translate-y-1/2 ml-6",
    "mid-center": "top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
    "mid-right": "top-1/2 right-0 transform -translate-y-1/2 mr-6",
    "bottom-left": "bottom-0 left-0 mb-16 ml-6",
    "bottom-center": "bottom-0 left-1/2 transform -translate-x-1/2 mb-16",
    "bottom-right": "bottom-0 right-0 mb-16 mr-6",
} as const;

const ConfirmToast: React.FC<ConfirmToastProps> = ({
    text,
    icon,
    position = "bottom-center",
    onConfirm,
    onCancel,
    bgColor = "bg-gallagher-blue-400",
}) => {
    return (
        <div
            className={`fixed p-4 text-white text-sm rounded-lg shadow-lg z-50 flex items-start gap-4 transition-all duration-300 ease-in-out ${bgColor} ${positionClasses[position]}`}
        >
            {icon && <div className="text-xl mt-1">{icon}</div>}
            <div className="flex flex-col space-y-2">
                <p className="text-white">{text}</p>
                <div className="flex justify-center gap-2">
                    <button
                        className="cursor-pointer px-3 py-1 border border-white rounded hover:bg-white hover:text-black transition"
                        onClick={onConfirm}
                    >
                        OK
                    </button>
                    <button
                        className="cursor-pointer px-3 py-1 border border-white rounded hover:bg-white hover:text-black transition"
                        onClick={onCancel}
                    >
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmToast;
