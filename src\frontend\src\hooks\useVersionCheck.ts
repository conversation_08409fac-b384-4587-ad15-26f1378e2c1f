import { useEffect } from "react";
import { RootState } from "../store";
import { checkVersionChange, markUpdatesAsViewed } from "../features/versionSlice";
import { useAppDispatch, useAppSelector } from "../app/hooks";

const useVersionCheck = () => {
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  // Use typed selector hook for better type safety
  const isUpdateAvailable = useAppSelector((state: RootState) => state.version.isUpdateAvailable);

  useEffect(() => {
    dispatch(checkVersionChange());
  }, [dispatch]);

  return { isUpdateAvailable, markUpdatesAsViewed: () => dispatch(markUpdatesAsViewed()) };
};

export default useVersionCheck;
