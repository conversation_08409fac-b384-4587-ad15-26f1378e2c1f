import { useNavigate } from "react-router";
import { BottomButtonsProps } from "../../interfaces";
import { useCallback } from "react";
import React from "react";

const BottomButtons = React.memo(({ workspaceIdFromUrl, activeAccount, save }: BottomButtonsProps) => {
  const navigate = useNavigate();
  const handleSave = useCallback(() => save(), [save]);

  return (
    <div className="flex items-center w-full mt-4">
      <div className="flex space-x-2">
        <button
          className="cursor-pointer bg-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:text-white hover:shadow-md border-2 hover:border-gallagher-dark-300 p-2 px-4 rounded-sm box-border dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 dark:hover:text-white"
          onClick={() => navigate("/manage-workspaces")}
          aria-label="Back to All Workspaces"
          title="Back"
        >
          Back
        </button>
        {workspaceIdFromUrl && activeAccount && (
          <button
            className="cursor-pointer bg-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:text-white hover:shadow-md border-2 hover:border-gallagher-dark-300 p-2 px-4 rounded-sm box-border dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 dark:hover:text-white"
            onClick={handleSave}
            aria-label="Save Changes"
            title="Save Changes"
          >
            Update
          </button>
        )}
        {!workspaceIdFromUrl && activeAccount && (
          <button
            className="cursor-pointer bg-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:text-white hover:shadow-md border-2 hover:border-gallagher-dark-300 p-2 px-4 rounded-sm box-border dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 dark:hover:text-white"
            onClick={handleSave}
            aria-label="Create Workspace"
            title="Create Workspace"
          >
            Create
          </button>
        )}
      </div>
      <button
        className="cursor-pointer ml-auto bg-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:text-white hover:shadow-md border-2 hover:border-gallagher-dark-300 p-2 px-4 rounded-sm box-border dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 dark:hover:text-white"
        onClick={() => navigate("/")}
        aria-label="Back to Chats"
        title="Back"
      >
        Close
      </button>
    </div>
  );
});

export default BottomButtons;