using System.Text.Json;
using System.Text;
using Backend.Extensions;

namespace BackendUnitTests
{
    public class ControllerExtensionsTests
    {
        [Fact]
        public void GetClaimValue_ReturnsExpectedClaim()
        {
            // Arrange
            var claims = new Dictionary<string, object> { { "xms_pdl", "NAM" }, { "other", 123 } };
            var json = JsonSerializer.Serialize(claims);
            var bytes = Encoding.GetEncoding("ISO-8859-1").GetBytes(json);
            var base64 = Convert.ToBase64String(bytes)
                .Replace('+', '-').Replace('/', '_').TrimEnd('=');
            var token = $"Bearer header.{base64}.signature";

            // Act
            var result = ControllerExtensions.GetClaimValue(token, "xms_pdl");

            // Assert
            Assert.Equal("NAM", result);
        }

        [Fact]
        public void GetClaimValue_ReturnsNullForMissingClaim()
        {
            // Arrange
            var claims = new Dictionary<string, object> { { "foo", "bar" } };
            var json = JsonSerializer.Serialize(claims);
            var bytes = Encoding.GetEncoding("ISO-8859-1").GetBytes(json);
            var base64 = Convert.ToBase64String(bytes)
                .Replace('+', '-').Replace('/', '_').TrimEnd('=');
            var token = $"Bearer header.{base64}.signature";

            // Act
            var result = ControllerExtensions.GetClaimValue(token, "xms_pdl");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void GetClaimValue_ValueIsEmptyString()
        {
            // Arrange
            Dictionary<string, object> claims = new Dictionary<string, object> { { "xms_pdl", "" } };
            var json = JsonSerializer.Serialize(claims);
            var bytes = Encoding.GetEncoding("ISO-8859-1").GetBytes(json);
            var base64 = Convert.ToBase64String(bytes)
                .Replace('+', '-').Replace('/', '_').TrimEnd('=');
            var token = $"Bearer header.{base64}.signature";

            // Act
            var result = ControllerExtensions.GetClaimValue(token, "xms_pdl");

            // Assert
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public void GetClaimValue_ReturnsExpectedClaim_WithAdditionalClaim()
        {

            var claims = new Dictionary<string, object> { { "xms_pdl", "NAM" }, { "additional", "VALUE" } };
            var json = JsonSerializer.Serialize(claims);
            var bytes = Encoding.GetEncoding("ISO-8859-1").GetBytes(json);
            var base64 = Convert.ToBase64String(bytes)
                .Replace('+', '-').Replace('/', '_').TrimEnd('=');
            var token = $"Bearer header.{base64}.signature";


            var result = ControllerExtensions.GetClaimValue(token, "additional");


            Assert.Equal("VALUE", result);
        }

        [Fact]
        public void GetClaimValue_ThrowsOnMalformedToken()
        {
            // Arrange
            var token = "Bearer malformed.token";

            // Act & Assert
            Assert.Throws<FormatException>(() => ControllerExtensions.GetClaimValue(token, "xms_pdl"));
        }
    }
}
