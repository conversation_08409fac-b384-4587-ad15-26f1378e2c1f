.loading-dots {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 0.6em;
}

.dot {
  width: 0.3em;
  height: 0.3em;
  margin: 0 0.1em;
  background-color: #ccc;
  border-radius: 50%;
  animation: wave 4s infinite;
}

.dot1 {
  animation-delay: 0s;
}

.dot2 {
  animation-delay: 0.5s;
}

.dot3 {
  animation-delay: 1s;
}

.dot4 {
    animation-delay: 1.5s;
  }

@keyframes wave {
  0%,
  20%,
  100% {
    transform: translateY(0);
    background-color: #ccc;
  }
  40% {
    transform: translateY(-0.2em);
    background-color: #666;
  }
  60% {
    transform: translateY(0);
    background-color: #ccc;
  }
}