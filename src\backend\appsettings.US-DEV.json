{
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=b1128e34-459b-41e3-92b2-7d9f343fb746;IngestionEndpoint=https://eastus2-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus2.livediagnostics.monitor.azure.com/;ApplicationId=545fa773-116b-49c0-80fe-17dc71cdb2c2"
  },
  "AzureOpenAI": {
    "Endpoint": "https://aiutility-apim-dev-gateway.ajgco.com"
  },
  "AzureStorage": {
    "Endpoints": {
      "Default": "https://corpdlsdevywjngc.blob.core.windows.net/",
      "CAN": "https://corpdlsdevoq6fci.blob.core.windows.net/"
    },
    "ExpirationPolicy": 30 //Days
  },
  "AzureDocIntel": {
    "Endpoint": "https://gallagherai-doc-int-2jn9.cognitiveservices.azure.com/"
  },
  "AzureSearch": {
    "Endpoints": {
      "Default": "https://corp-gallagheraius-srch-dev-sdf0v.search.windows.net",
      "CAN": "https://corp-gallagheraius-srch-dev-j9vde.search.windows.net"
    }
  },
  "AzureKeyVault": {
    "Endpoint": "https://corp-kv-dev-a2fm0e.vault.azure.net/"
  },
  "Conversion": {
    "Endpoint": "https://corp-aigo-app-ngec-dev.azurewebsites.net/forms/libreoffice/convert"
  },
  "Translation": {
    "Endpoint": "https://gallagherai-doc-translate-cnrh.cognitiveservices.azure.com/"
  }
}
