import { useEffect } from "react";

const useCodeBlocksThemeSwitcher = () => {
  useEffect(() => {
    const themeId = "code-blocks-theme";

    const getThemeHref = (isDarkMode: boolean) => {
      // Verifies if the mode is localhost or cloud based
      const isLocalEnv = import.meta.env.MODE === "localhost";

      const basePath = isLocalEnv
        ? "/node_modules/highlight.js/styles" // Localhost styles route
        : "/assets/styles"; // Cloud styles route (staticly generated by the build in pipelines)

      return isDarkMode
        ? `${basePath}/atom-one-dark.min.css`
        : `${basePath}/atom-one-light.min.css`;
    };

    const applyTheme = (isDarkMode: boolean) => {
      let themeLink = document.getElementById(themeId) as HTMLLinkElement;

      if (!themeLink) {
        themeLink = document.createElement("link");
        themeLink.id = themeId;
        themeLink.rel = "stylesheet";
        document.head.appendChild(themeLink);
      }

      themeLink.href = getThemeHref(isDarkMode);
    };

    const loadTheme = () => {
      const savedTheme = localStorage.getItem("theme");
      const isDarkMode = savedTheme === "dark";
      applyTheme(isDarkMode);
    };

    loadTheme();

    const handleThemeChange = () => {
      const newTheme = document.documentElement.classList.contains("dark") ? "dark" : "light";
      applyTheme(newTheme === "dark");
    };

    window.addEventListener("themechange", handleThemeChange);

    return () => {
      window.removeEventListener("themechange", handleThemeChange);
    };
  }, []);
};

export default useCodeBlocksThemeSwitcher;