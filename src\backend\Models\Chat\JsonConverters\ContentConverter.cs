﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Deserializes / serializes the value of a <see cref="Content"/>.
    /// </summary>
    public class ContentConverter : JsonConverter<Content>
    {
        public override Content Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var jsonElement = JsonDocument.ParseValue(ref reader).RootElement;
            var typeString = jsonElement.GetProperty("type").GetString();
            var type = Enum.Parse<cType>(typeString!, ignoreCase: true);
            object? value = type switch
            {
                cType.Text => jsonElement.GetProperty("value").GetString(),
                cType.Follow_Up_Questions => JsonSerializer.Deserialize<List<string>>(jsonElement.GetProperty("value").GetRawText(), options),
                cType.Citations => JsonSerializer.Deserialize<List<string>>(jsonElement.GetProperty("value").GetRawText(), options),
                cType.Image => JsonSerializer.Deserialize<object>(jsonElement.GetProperty("value").GetRawText(), options),
                _ => throw new NotSupportedException($"Chat item type '{type}' is not supported")
            };

            return new Content
            {
                Type = type,
                Value = value
            };
        }

        public override void Write(Utf8JsonWriter writer, Content value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            writer.WriteString("type", value.Type!.ToString()!.ToLower());
            writer.WritePropertyName("value");
            switch (value.Type)
            {
                case cType.Text:
                    writer.WriteStringValue(value.Value as string);
                    break;
                case cType.Follow_Up_Questions:
                    JsonSerializer.Serialize(writer, value.Value as List<string>, options);
                    break;
                case cType.Image:
                    JsonSerializer.Serialize(writer, value.Value, options);
                    break;
                case cType.Citations:
                    JsonSerializer.Serialize(writer, value.Value as List<string>, options);
                    break;
                default:
                    throw new NotSupportedException($"Chat item type '{value.Type}' is not supported");
            }
            writer.WriteEndObject();
        }
    }
}