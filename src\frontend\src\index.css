@import 'tailwindcss';

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@utility fade-in {
  @apply transition-opacity duration-300 ease-in opacity-100;
}

@utility fade-out {
  @apply transition-opacity duration-300 ease-out opacity-0;
}

@font-face {
    font-family: 'PTSans';
    src: url('../src/assets/fonts/PTSans-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PTSans';
    src: url('../src/assets/fonts/PTSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'PTSans';
    src: url('../src/assets/fonts/PTSans-Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
}

/* Pulse animation for updates red dot */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.9);
    opacity: 0.3;
  }
}

.animate-pulse-custom {
  animation: pulse 1.5s infinite;
}

body {
    font-family: 'PTSans', Arial, Helvetica, sans-serif;
}

/* Messages styles */
.message-line-wrap {
  white-space: pre-wrap;
}

.bot-message, .user-message {
  font-size: 0.92rem !important;
}

.markdown-body ol{
  list-style: number;
}

.markdown-body ul{
  list-style: circle;
}

pre code.hljs {
  white-space: pre-wrap !important;
}

.markdown-body table {
  width: 100% !important;
}

/* Follow-Up Questions animation styles STARTS */
.followUpEnter {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

.followUpEnterActive {
  opacity: 1;
  max-height: 300px;
  transition: opacity 300ms, max-height 300ms;
}

.followUpExit {
  opacity: 1;
  max-height: 300px;
}

.followUpExitActive {
  opacity: 0;
  max-height: 0;
  transition: opacity 300ms, max-height 300ms;
  overflow: hidden;
}

/* Fade in animation */
.fadeIn {
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
/* Follow-Up Questions animation styles end */

/* Highlight animation for citation buttons */
@keyframes highlightPulse {
  0% {
    background-color: var(--highlight-color-start);
  }
  50% {
    background-color: var(--highlight-color-mid);
  }
  100% {
    background-color: var(--highlight-color-end);
  }
}

:root {
  --highlight-color-start: #e5e7eb; /* blue-400 */
  --highlight-color-mid: #9eb5ff;   /* blue-500 */
  --highlight-color-end: #e5e7eb;   /* blue-600 */
}

.dark {
  --highlight-color-start: #374151; /* blue-800 */
  --highlight-color-mid: #0d5bd8;   /* blue-900 */
  --highlight-color-end: #374151;   /* blue-950 */
}

.citation-highlight {
  animation: highlightPulse 2s ease-in-out;
}

@media (max-width: 1024px) {
  .mobileViewPdfPanel {
    width: 100%;
    position: absolute;
    z-index: 10;
    top: 50px;
  }
  .mobileViewChat{
    opacity: 0;
  }
}