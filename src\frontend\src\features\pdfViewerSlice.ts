import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface PdfViewerState {
  isOpen: boolean;
  pdfUrl: string | null;
}

const initialState: PdfViewerState = {
  isOpen: false,
  pdfUrl: null,
};

const pdfViewerSlice = createSlice({
  name: 'analysisPanel',
  initialState,
  reducers: {
    openPanel(state, action: PayloadAction<string>) {
      state.isOpen = true;
      state.pdfUrl = action.payload;
    },
    closePanel(state) {
      state.isOpen = false;
      state.pdfUrl = null;
    },
  },
});

export const { openPanel, closePanel } = pdfViewerSlice.actions;
export default pdfViewerSlice.reducer;
