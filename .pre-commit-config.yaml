# Please see docs for installation: https://pre-commit.com/#installation
repos:
- repo: https://dev.azure.com/ajg-corp/GTS-InfrastructureAsCode-CenterOfExcellence/_git/pre-commit-terraform
  rev: v1.79.1
  hooks:
    - id: terraform_fmt
      args:
        - --args=-recursive
        - --args=-write=true
    - id: terraform_tflint
      args:
        - --args=--no-module
        - --args=--disable-rule=terraform_module_pinned_source
        - --args=--enable-rule=terraform_documented_variables
    - id: terraform_validate
      args:
        - --hook-config=--retry-once-with-cleanup=true

- repo: https://dev.azure.com/ajg-corp/GTS-InfrastructureAsCode-CenterOfExcellence/_git/pre-commit-hooks
  rev: v2.3.0
  hooks:
    - id: check-yaml
    - id: end-of-file-fixer
    - id: trailing-whitespace
