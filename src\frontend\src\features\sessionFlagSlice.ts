import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { SessionFlagState } from "../interfaces";

const initialState: SessionFlagState = {
  status: true,
};

const sessionCallFlagSlice = createSlice({
  name: "status",
  initialState,
  reducers: {
    setSessionCallFlag: (state, action: PayloadAction<boolean>) => {
      state.status = action.payload;
    },
  },
});

export const { setSessionCallFlag } = sessionCallFlagSlice.actions;

export default sessionCallFlagSlice.reducer;
