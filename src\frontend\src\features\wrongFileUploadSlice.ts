import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { WrongFileUploadState } from "../interfaces";

const initialState: WrongFileUploadState = {
  status: false,
};

const wrongFileUploadSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    SetFileUploadStatus: (state, action: PayloadAction<boolean>) => {
      state.status = action.payload;
    },
  },
});

export const { SetFileUploadStatus } = wrongFileUploadSlice.actions;

export default wrongFileUploadSlice.reducer;
