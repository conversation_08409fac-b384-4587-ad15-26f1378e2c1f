import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import BottomButtons from '../BottomButtons';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { JSX } from 'react/jsx-runtime';
import { BottomButtonsProps } from '../../../interfaces';

describe('BottomButtons', () => {
  const mockNavigate = vi.fn();
  const mockSave = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
  });

  vi.mock('react-router-dom', async () => {
    const actual = await vi.importActual('react-router-dom');
    return {
      ...actual,
      useNavigate: () => mockNavigate,
    };
  });

  const renderComponent = (props: JSX.IntrinsicAttributes & BottomButtonsProps) => {
    return render(
      <MemoryRouter>
        <BottomButtons {...props} />
      </MemoryRouter>
    );
  };

  it('should render Back button and Close button', () => {
    renderComponent({ workspaceIdFromUrl: null, activeAccount: null, save: mockSave });

    const backButton = screen.getByLabelText('Back to All Workspaces');
    expect(backButton).toBeInTheDocument();
    expect(backButton).toHaveTextContent('Back');
    expect(backButton).toBeEnabled();

    const closeButton = screen.getByLabelText('Back to Chats');
    expect(closeButton).toBeInTheDocument();
    expect(closeButton).toHaveTextContent('Close');
    expect(closeButton).toBeEnabled();
  });



  it('should render Update button when workspaceIdFromUrl and activeAccount are provided', () => {
    renderComponent({ workspaceIdFromUrl: '123', activeAccount: {
        homeAccountId: '',
        environment: '',
        tenantId: '',
        username: '',
        localAccountId: ''
    }, save: mockSave });

    const saveChangesButton = screen.getByLabelText('Save Changes');
    expect(saveChangesButton).toBeInTheDocument();
    expect(saveChangesButton).toHaveTextContent('Update');
    expect(saveChangesButton).toBeEnabled();
  });

  it('should render Create button when workspaceIdFromUrl is not provided but activeAccount is provided', () => {
    renderComponent({ workspaceIdFromUrl: null, activeAccount: {
        homeAccountId: '',
        environment: '',
        tenantId: '',
        username: '',
        localAccountId: ''
    }, save: mockSave });

    const createWorkspaceButton = screen.getByLabelText('Create Workspace');
    expect(createWorkspaceButton).toBeInTheDocument();
    expect(createWorkspaceButton).toHaveTextContent('Create');
    expect(createWorkspaceButton).toBeEnabled();
  });

  it('should call save function when Update button is clicked', () => {
    renderComponent({ workspaceIdFromUrl: '123', activeAccount: {
        homeAccountId: '',
        environment: '',
        tenantId: '',
        username: '',
        localAccountId: ''
    }, save: mockSave });

    const saveChangesButton = screen.getByLabelText('Save Changes');
    fireEvent.click(saveChangesButton);
    expect(mockSave).toHaveBeenCalled();
  });

  it('should call save function when Create button is clicked', () => {
    renderComponent({ workspaceIdFromUrl: null, activeAccount: {
        homeAccountId: '',
        environment: '',
        tenantId: '',
        username: '',
        localAccountId: ''
    }, save: mockSave });

    const createWorkspaceButton = screen.getByLabelText('Create Workspace');
    fireEvent.click(createWorkspaceButton);
    expect(mockSave).toHaveBeenCalled();
  });
});