import { MAX_FILE_SIZE, MAX_PAGES } from "../constants/constants";

export const parsePdfUrl = (pdfUrl: string) => {
  const [fileName, pageParam] = pdfUrl.split("#");
  return {
    fileName,
    pageParam,
  };
};

export const validateFiles = async (files: File[]): Promise<void> => {
  for (const file of files) {
    if (file.size > MAX_FILE_SIZE) {
      const MAX_MEGS = MAX_FILE_SIZE / 1024 / 1024;
      throw new Error(
        `The file "${file.name}" exceeds the maximum allowed size (${MAX_MEGS}MB).`
      );
    }

    const getNumPages = async (file: File) => {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const pdfText = new TextDecoder().decode(uint8Array);
      const pageRegex = /\/Type\s*\/Page\b/g;
      const matches = pdfText.match(pageRegex);
      return matches ? matches.length : 0;
    };

    const numPages = await getNumPages(file);
    if (numPages > MAX_PAGES) {
      throw new Error(
        `The document "${file.name}" exceeds the maximum allowed number of pages (~300).`
      );
    }
  }
};
