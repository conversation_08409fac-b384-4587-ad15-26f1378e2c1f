﻿
namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureControllers(this IServiceCollection services)
        {
            services.AddControllers();
            services.AddAuthorization(options => { options.FallbackPolicy = options.DefaultPolicy; });
            services.AddEndpointsApiExplorer();
            services.AddHealthChecks();
        }
    }
}
