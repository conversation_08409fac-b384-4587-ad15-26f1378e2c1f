﻿using Azure.Storage.Blobs;
using Backend.Models;
using Backend.Services;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureBlobServices(this IServiceCollection services, IConfiguration config)
        {
            Dictionary<string, BlobServiceClient> clients = new();
            foreach (var endpoint in config.GetSection("AzureStorage:Endpoints").Get<Dictionary<string, string>>()!)
            {
                clients.Add(endpoint.Key,
                    new BlobServiceClient(
                        new Uri(endpoint.Value), _azureCredential));
            }

            services.AddSingleton(clients);

            services.AddSingleton<IStorageService, BlobStorageService>(sp =>
            {
                return new BlobStorageService(
                    sp.GetRequiredService<Dictionary<string, BlobServiceClient>>()
                        .ToDictionary(kvp => kvp.Key,
                                      kvp => kvp.Value.GetBlobContainerClient(config["AzureStorage:Container"])),
                    config.GetValue<int>("AzureStorage:ExpirationPolicy"),
                    sp.GetRequiredService<ITaskQueue>());
            });
        }
    }
}
