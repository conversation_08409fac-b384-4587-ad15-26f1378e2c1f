﻿using System.Text;
using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using static Common.Common;

partial class Program
{
    private const int BatchSize = 1000;     // Maximum allowed size is 1000
    private static BlobContainerClient? _blobClient;
    private static SearchClient? _searchClient;
    private static SearchIndexClient? _indexClient;

    /// <summary>
    /// Main script
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    static async Task Main(string[] args)
    {
        Console.WriteLine("Initializing Index Cleanup...");
        Initialize();

        try
        {
            foreach (var key in _blobClients!.Keys)
            {
                _blobClient = _blobClients[key];
                _searchClient = _searchClients![key];
                _indexClient = _indexClients![key];

                // Validate the Index Fields
                Log(LogLevel.Information, $"Index Cleanup {key.ToString()}: Validating index fields...");
                await ValidateIndex("gallagherai-docs");

                // Get the names of all blobs in the container
                Log(LogLevel.Information, $"Index Cleanup {key.ToString()}: Getting Blobs...");
                var blobNames = await GetBlobNamesAsync();

                // Get the IDs of documents that need to be deleted
                Log(LogLevel.Information, $"Index Cleanup {key.ToString()}: Determining orphaned items...");
                var itemsToDelete = await GetItemsToDeleteAsync(blobNames);

                // Delete the documents in batches
                Log(LogLevel.Information, $"Index Cleanup {key.ToString()}: Deleting {itemsToDelete.Count} orphaned items...");
                await DeleteDocumentsInBatchesAsync(itemsToDelete);
            }
        }
        catch (Exception ex)
        {
            Log(LogLevel.Error, "An error occurred during index cleanup.", ex);
        }
    }

    /// <summary>
    /// Gets the names of all blobs in the container
    /// </summary>
    /// <returns></returns>
    private static async Task<HashSet<string>> GetBlobNamesAsync()
    {
        var blobItems = _blobClient!.GetBlobsAsync();
        var blobNames = new HashSet<string>();

        await foreach (var blobItem in blobItems)
        {
            blobNames.Add(blobItem.Name);
        }

        return blobNames;
    }

    /// <summary>
    /// Gets the items that need to be deleted
    /// </summary>
    /// <param name="blobNames"></param>
    /// <returns></returns>
    private static async Task<List<string>> GetItemsToDeleteAsync(HashSet<string> blobNames)
    {
        var facets = await GetDistinctWorkspacesAsync();
        var itemsToDelete = new List<string>();

        for (int i = 0; i < facets.Count / 2; i += 75)
        {
            var filter = BuildFilter(facets, i, 75);
            var searchOptions = new SearchOptions
            {
                Size = BatchSize,
                Select = { "id", "sourcefile" },
                Filter = filter
            };

            itemsToDelete.AddRange(await FetchOrphanedItemsAsync(searchOptions, blobNames));
        }

        return itemsToDelete;
    }

    /// <summary>
    /// Builds the filter for to be used in the search options
    /// </summary>
    /// <param name="facets"></param>
    /// <param name="startIndex"></param>
    /// <param name="count"></param>
    /// <returns></returns>
    private static string BuildFilter(HashSet<string> facets, int startIndex, int count)
    {
        var filterBuilder = new StringBuilder();
        var endIndex = Math.Min(startIndex + count, facets.Count);

        // facets are ordered by their count descending. We get the first and
        // last elements as a way to balance the number of search results between filters.

        for (int i = startIndex; i < endIndex; i++)
        {
            filterBuilder.Append($"workspace eq '{facets.ElementAt(i)}' or ");
            if (facets.Count > 0)
            {
                filterBuilder.Append($"workspace eq '{facets.ElementAt(facets.Count - i - 1)}' or ");
            }
        }

        if (filterBuilder.Length > 4)
        {
            filterBuilder.Length -= 4; // Remove the trailing " or "
        }

        return filterBuilder.ToString();
    }

    /// <summary>
    /// Fetches search items given search options and existing blob names
    /// </summary>
    /// <param name="searchOptions"></param>
    /// <param name="blobNames">The names of all existing blobs in the storage storage</param>
    /// <returns></returns>
    private static async Task<List<string>> FetchOrphanedItemsAsync(SearchOptions searchOptions, HashSet<string> blobNames)
    {
        var items = new List<string>();
        int skip = 0;
        bool hasMoreResults = true;

        while (hasMoreResults)
        {
            searchOptions.Skip = skip;
            var searchResults = await _searchClient!.SearchAsync<Dictionary<string, object>>("*", searchOptions);

            if (searchResults != null)
            {
                var resultsList = searchResults.Value.GetResults().ToList();
                items.AddRange(resultsList
                    .Where(result => result.Document.TryGetValue("sourcefile", out var sourcefile)
                        && !blobNames.Contains(sourcefile.ToString()!))
                    .Select(result => result.Document["id"].ToString())
                    .ToList()!);

                hasMoreResults = resultsList.Count >= searchOptions.Size;
                skip += BatchSize;
            }
            else
            {
                hasMoreResults = false;
            }
        }

        return items;
    }

    /// <summary>
    /// Gets all workspaces in the index
    /// </summary>
    /// <returns></returns>
    private static async Task<HashSet<string>> GetDistinctWorkspacesAsync()
    {
        var distinctWorkspaces = new HashSet<string>();
        var searchOptions = new SearchOptions
        {
            Size = 0,
            Facets = { "workspace, count:0" } // Count:0 - Gets all distinct workspaces
        };

        CancellationToken hasMoreResults = CancellationToken.None;

        var searchResults = await
                _searchClient!.SearchAsync<Dictionary<string, object>>("*", searchOptions);

        if (searchResults != null)
        {
            var facetResults = searchResults.Value.Facets;

            if (facetResults != null && facetResults.ContainsKey("workspace"))
            {
                foreach (var facet in facetResults["workspace"])
                {
                    distinctWorkspaces.Add(facet.Value.ToString()!);
                }
            }
        }

        return distinctWorkspaces;
    }

    /// <summary>
    /// Deletes the documents in batches
    /// </summary>
    /// <param name="documentIds"></param>
    /// <returns></returns>
    private static async Task DeleteDocumentsInBatchesAsync(List<string> documentIds)
    {
        const int batchSize = 100;

        for (int i = 0; i < documentIds.Count; i += batchSize)
        {
            var batch = documentIds.Skip(i).Take(batchSize).ToList();
            await _searchClient!.DeleteDocumentsAsync("id", batch);
            Log(LogLevel.Information, $"Index Cleanup: Deleted {batch.Count} documents from the search index.");
        }
    }

    /// <summary>
    /// Validates and adds missing fields to the specified index
    /// </summary>
    /// <param name="indexName"></param>
    /// <returns></returns>
    private static async Task ValidateIndex(string indexName)
    {
        try
        {
            var index = await _indexClient!.GetIndexAsync(indexName);

            // Fields that should be in the index
            var fields = new[]
            {
                new SimpleField("id", SearchFieldDataType.String) { IsKey = true, IsFilterable = false, IsSortable = false, IsFacetable = false },
                new SearchableField("content") { IsFilterable = false, IsSortable = false, IsFacetable = false, AnalyzerName = LexicalAnalyzerName.EnMicrosoft },
                new SearchField("embedding", SearchFieldDataType.Collection(SearchFieldDataType.Single)) { IsSearchable = true, IsFilterable = false, IsSortable = false, IsFacetable = false },
                new SimpleField("workspace", SearchFieldDataType.String) { IsFilterable = true, IsSortable = false, IsFacetable = true },
                new SimpleField("sourcepage", SearchFieldDataType.String) { IsFilterable = true, IsSortable = false, IsFacetable = true },
                new SimpleField("sourcefile", SearchFieldDataType.String) { IsFilterable = true, IsSortable = false, IsFacetable = true },
                new SimpleField("storageUrl", SearchFieldDataType.String) { IsFilterable = true, IsSortable = false, IsFacetable = false },
                new SimpleField("oids", SearchFieldDataType.Collection(SearchFieldDataType.String)) { IsFilterable = true, IsSortable = false, IsFacetable = false },
                new SimpleField("groups", SearchFieldDataType.Collection(SearchFieldDataType.String)) { IsFilterable = true, IsSortable = false, IsFacetable = false },
                new SimpleField("sequence", SearchFieldDataType.Int32) { IsFilterable = true, IsSortable = true, IsFacetable = false }
            };

            foreach (var field in fields)
            {
                if (!index.Value.Fields.Any(f => f.Name == field.Name))
                {
                    index.Value.Fields.Add(field);
                    Log(LogLevel.Information,
                        $"Field '{field.Name}' added to the index '{indexName}'.");
                }
            }

            await _indexClient!.CreateOrUpdateIndexAsync(index.Value);
            Log(LogLevel.Information, $"Index '{indexName}' validated.");
        }
        catch (RequestFailedException ex)
        {
            Log(LogLevel.Error, $"Failed to update the index '{indexName}'.", ex);
        }
    }
}
