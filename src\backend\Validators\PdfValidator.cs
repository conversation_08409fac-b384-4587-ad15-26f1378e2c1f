﻿using PdfSharp.Pdf.IO;

namespace Backend.Validators
{
    public static class PdfValidator
    {
        private const int MaxPages = 350;
        private const long MaxFileSize = 40 * 1024 * 1024; // 40MB

        public static void Validate(Stream pdfStream, long fileSize, string fileName)
        {
            if (!IsFileSizeValid(fileSize))
            {
                throw new BadHttpRequestException($"'{fileName}' size exceeds the maximum limit of {MaxFileSize / (1024 * 1024)}MB.");
            }

            if (!IsPdfPageCountValid(pdfStream))
            {
                throw new BadHttpRequestException($"'{fileName}' exceeds the maximum allowed number of pages.");
            }
        }

        private static bool IsPdfPageCountValid(Stream pdfStream)
        {
            using (var document = PdfReader.Open(pdfStream, PdfDocumentOpenMode.Import))
            {
                return document.PageCount <= MaxPages;
            }
        }

        private static bool IsFileSizeValid(long fileSize)
        {
            return fileSize <= MaxFileSize;
        }
    }
}