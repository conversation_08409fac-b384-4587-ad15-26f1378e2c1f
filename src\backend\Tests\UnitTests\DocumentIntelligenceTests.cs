﻿using Azure;
using Azure.AI.DocumentIntelligence;
using Backend.Models;
using Backend.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;
using System.Reflection;
using Newtonsoft.Json.Linq;

namespace BackendUnitTests
{
    public class DocumentIntelligenceTests
    {
        private readonly Mock<DocumentIntelligenceClient> _mockDocClient;
        private readonly Mock<ILogger<DocumentIntelligenceService>> _mockLogger;
        private readonly DocumentIntelligenceService _service;

        public DocumentIntelligenceTests()
        {
            _mockDocClient = new Mock<DocumentIntelligenceClient>();
            _mockLogger = new Mock<ILogger<DocumentIntelligenceService>>();
            _service = new DocumentIntelligenceService(_mockDocClient.Object,
                                                       _mockLogger.Object,
                                                       130);
        }

        private IEnumerable<Section> InvokeSplitIntoSections(string text,
                                                             string blob,
                                                             string oid,
                                                             string workspaceId,
                                                             object sequence,
                                                             int page,
                                                             int max = 20_000)
        {
            MethodInfo methodInfo = typeof(DocumentIntelligenceService)
                .GetMethod("SplitIntoSections", BindingFlags.NonPublic | BindingFlags.Instance)!;
            return (IEnumerable<Section>)methodInfo.Invoke(
                _service, new object[] { text, blob, oid, workspaceId, sequence, page, max })!;
        }

        /******************************************
        *                Tests
        ******************************************/

        [Fact]
        public void CreateSections_EmptyPageDetails_ReturnsEmptyList()
        {
            // Arrange
            string content = "";
            var oid = "test-oid";
            var workspaceId = "test-workspace";
            var blob = "test-blob";

            // Act
            var sections = _service.CreateSections(
                content, oid, workspaceId, blob).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.Empty(sections);
        }

        [Fact]
        public void CreateSections_RandomTests()
        {
            // Arrange
            var random = new Random();
            for (int i = 0; i < 100; i++)
            {
                string pageDetails = GenerateRandomPageDetails(random, random.Next(1, 40));
                string oid = Guid.NewGuid().ToString();
                string workspaceId = Guid.NewGuid().ToString();
                string blob = GenerateRandomText(random, 10, "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");

                // Act
                var sections = _service.CreateSections(pageDetails, oid, workspaceId, blob).ToList();

                // Assert
                Assert.NotNull(sections);
                Assert.All(sections, section =>
                {
                    Assert.StartsWith(blob, section.Id);
                    Assert.Equal(workspaceId, section.Workspace);
                    Assert.StartsWith(blob, section.SourceFile);
                });
            }
        }

        [Fact]
        public void SplitIntoSections_Max25()
        {
            // Arrange
            string text = "This is a test. This is another test. Some more text.";
            string blob = "test-blob";
            string oid = "test-oid";
            string workspaceId = "test-workspace";
            var sequence = new Sequence { i = 1 };
            int page = 1;
            int max = 25;

            // Act
            var sections = InvokeSplitIntoSections(text, blob, oid, workspaceId, sequence, page, max).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.True(sections.Count > 1);
            Assert.Equal("This is a test.", sections[0].Content);
            Assert.Equal(" This is another test.", sections[1].Content);
            Assert.Equal(" Some more text.", sections[2].Content);
        }

        [Fact]
        public void SplitIntoSections_CreatesSections()
        {
            // Arrange
            string text = "This is a test. This is another test. Some more text.";
            string blob = "test-blob";
            string oid = "test-oid";
            string workspaceId = "test-workspace";
            var sequence = new Sequence { i = 1 };
            int page = 1;
            int max = 20_000;

            // Act
            var sections = InvokeSplitIntoSections(text, blob, oid, workspaceId, sequence, page, max).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.True(sections.Count == 1);
            Assert.Contains(sections, s => s.Content.Contains("This is a test. This is another test. Some more text."));
        }

        [Fact]
        public void SplitIntoSections_WorksWithNoText()
        {
            // Arrange
            string text = "";
            string blob = "";
            string oid = "";
            string workspaceId = "";
            var sequence = new Sequence { i = 1 };
            int page = 1;
            int max = 20_000;

            // Act
            var sections = InvokeSplitIntoSections(text, blob, oid, workspaceId, sequence, page, max).ToList();

            // Assert
            Assert.NotNull(sections);
        }

        [Fact]
        public void FindSplitPoint_SplitsAtASentenceEnding()
        {
            // Arrange
            string text = "This is a test. This is another test. Some more text.";
            int mid = text.Length / 2;

            // Act
            int splitPoint = InvokeFindSplitPoint(text, mid);

            // Assert
            Assert.Equal(37, splitPoint); // Split point should be after the second sentence
        }

        [Fact]
        public void FindSplitPoint_ReturnsMidIfNoSuitableSplitPoint()
        {
            // Arrange
            string text = "Thisisaverylongwordwithoutanybreaks";
            int mid = text.Length / 2;

            // Act
            int splitPoint = InvokeFindSplitPoint(text, mid);

            // Assert
            Assert.Equal(mid, splitPoint); // Split point should be the middle of the text
        }

        [Fact]
        public void FindSplitPoint_IsMidOrDoesntExceedMax_RandomTests()
        {
            // Arrange
            var random = new Random();
            for (int i = 0; i < 100; i++)
            {
                string text = GenerateRandomText(random, 40_000);
                int mid = text.Length / 2;
                int max = random.Next(100, 40_000);

                // Act
                int splitPoint = InvokeFindSplitPoint(text, mid, max);

                // Assert
                Assert.True(splitPoint <= max || splitPoint == mid,
                    $"Split point {splitPoint} exceeds max {max} or is not {mid} for text: {text}");
            }
        }

        //[Fact]
        //public async Task GetDocumentTextAsync_ReturnsPageDetails()
        //{
        //    // Arrange
        //    var stream = new MemoryStream(Encoding.UTF8.GetBytes("Sample document content"));
        //    var analyzeResultType = typeof(AnalyzeResult);
        //    var constructor = analyzeResultType.GetConstructors(BindingFlags.NonPublic | BindingFlags.Instance)
        //        .FirstOrDefault(ctor => ctor.GetParameters().Length == 0);

        //    var mockResult = (AnalyzeResult)constructor!.Invoke(null);

        //    // Use reflection to set the fields directly
        //    analyzeResultType.GetField("<ApiVersion>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, "2023-10-01");
        //    analyzeResultType.GetField("<ModelId>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, "prebuilt-layout");

        //    var stringIndexType = analyzeResultType.Assembly.GetType("Azure.AI.DocumentIntelligence.Models.StringIndexType");
        //    var contentFormatType = analyzeResultType.Assembly.GetType("Azure.AI.DocumentIntelligence.Models.DocumentContentFormat");

        //    analyzeResultType.GetField("<StringIndexType>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, Enum.Parse(stringIndexType!, "Utf16CodeUnit"));
        //    analyzeResultType.GetField("<ContentFormat>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, Enum.Parse(contentFormatType!, "Markdown"));

        //    analyzeResultType.GetField("<Content>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, "Sample document content\n<!-- PageBreak -->\nPage 2 content");
        //    analyzeResultType.GetField("<Pages>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentPage>());
        //    analyzeResultType.GetField("<Paragraphs>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentParagraph>());
        //    analyzeResultType.GetField("<Tables>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentTable>());
        //    analyzeResultType.GetField("<Figures>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentFigure>());
        //    analyzeResultType.GetField("<Sections>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentSection>());
        //    analyzeResultType.GetField("<KeyValuePairs>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentKeyValuePair>());
        //    analyzeResultType.GetField("<Styles>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentStyle>());
        //    analyzeResultType.GetField("<Languages>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentLanguage>());
        //    analyzeResultType.GetField("<Documents>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<AnalyzedDocument>());
        //    analyzeResultType.GetField("<Warnings>k__BackingField", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new List<DocumentIntelligenceWarning>());
        //    analyzeResultType.GetField("_serializedAdditionalRawData", BindingFlags.NonPublic | BindingFlags.Instance)!.SetValue(mockResult, new Dictionary<string, BinaryData>());

        //    var mockOperation = new Mock<Operation<AnalyzeResult>>();
        //    mockOperation.Setup(op => op.Value).Returns(mockResult);

        //    _mockDocClient
        //        .Setup(client => client.AnalyzeDocumentAsync(
        //            It.IsAny<WaitUntil>(),
        //            It.IsAny<AnalyzeDocumentOptions>(),
        //            It.IsAny<CancellationToken>()))
        //        .ReturnsAsync(mockOperation.Object);

        //    // Act
        //    var result = await _service.GetDocumentTextAsync(stream);

        //    // Assert
        //    Assert.NotNull(result);
        //    Assert.Contains("Sample document content", result);
        //    Assert.Contains("Page 2 content", result);
        //}

        [Fact]
        public void CreateSections_CombinesTextAsNeeded()
        {
            // Arrange
            string pageDetails = "This is the text of page 1.\n" +
                                 "<!-- PageBreak -->\n" +
                                 "This is the text of page 2.";

            var oid = "test-oid";
            var workspaceId = "test-workspace";
            var blob = "test-blob";

            // Act
            var sections = _service.CreateSections(pageDetails, oid, workspaceId, blob).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.Equal(2, sections.Count);

            Assert.Equal("This is the text of page 1.\n<!-- PageBreak -->", sections[0].Content);
            Assert.Equal("test-workspace", sections[0].Workspace);
            Assert.Equal("test-blob#page=1", sections[0].SourcePage);
            Assert.Equal("test-blob", sections[0].SourceFile);

            Assert.Equal("\nThis is the text of page 2.", sections[1].Content);
            Assert.Equal("test-workspace", sections[1].Workspace);
            Assert.Equal("test-blob#page=2", sections[1].SourcePage);
            Assert.Equal("test-blob", sections[1].SourceFile);
        }

        [Fact]
        public void CreateSections_CreatesMultipleSections()
        {
            // Arrange
            var random = new Random();
            string GenerateRandomSentence(int wordCount)
            {
                var words = new List<string>();
                for (int i = 0; i < wordCount; i++)
                {
                    words.Add(new string((char)random.Next('a', 'z' + 1), random.Next(3, 8)));
                }
                return string.Join(" ", words) + ".";
            }

            var longTextPage1 = string.Join(" ", Enumerable.Range(0, 50).Select(_ => GenerateRandomSentence(10)));
            var longTextPage2 = string.Join(" ", Enumerable.Range(0, 60).Select(_ => GenerateRandomSentence(9)));
            var longTextPage3 = string.Join(" ", Enumerable.Range(0, 40).Select(_ => GenerateRandomSentence(8)));

            var pageDetails = longTextPage1 + 
                              "\n<!-- PageBreak -->\n" + 
                              longTextPage2 + 
                              "\n<!-- PageBreak -->\n" + 
                              longTextPage3;

            var oid = "test-oid";
            var workspaceId = "test-workspace";
            var blob = "test-blob";

            // Act
            var sections = _service.CreateSections(pageDetails, oid, workspaceId, blob).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.True(sections.Count > 1);
            Assert.All(sections, section =>
            {
                Assert.StartsWith(blob, section.Id);
                Assert.Equal(workspaceId, section.Workspace);
                Assert.StartsWith(blob, section.SourceFile);
            });
        }

        /******************************************
        * Helper functions to create mock objects *
        ******************************************/

        private int InvokeFindSplitPoint(string text, int mid, int max = 20_000)
        {
            MethodInfo methodInfo = typeof(DocumentIntelligenceService)
                .GetMethod("FindSplitPoint", BindingFlags.NonPublic | BindingFlags.Instance)!;
            return (int)methodInfo.Invoke(
                _service, new object[] { text, mid, max })!;
        }

        private string InvokeReplaceTablesWithHtml(Operation<AnalyzeResult> result,
                                                   int pageIndex,
                                                   int[] tableChars,
                                                   List<DocumentTable> tablesOnPage)
        {
            MethodInfo methodInfo = typeof(DocumentIntelligenceService)
                .GetMethod("ReplaceTablesWithHtml", BindingFlags.NonPublic | BindingFlags.Instance)!;
            return (string)methodInfo.Invoke(
                _service, new object[] { result, pageIndex, tableChars, tablesOnPage })!;
        }

        private string GenerateRandomText(Random random, int length, string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .!?")
        {
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        private string GenerateRandomPageDetails(Random random, int count)
        {
            StringBuilder pageDetails = new StringBuilder();

            for (int i = 0; i < count; i++)
            {
                string text = GenerateRandomText(random, random.Next(100, 25000));
                pageDetails.Append(text);
                pageDetails.Append("\n<!-- PageBreak -->\n");
            }
            return pageDetails.ToString();
        }
    }
}