﻿#pragma warning disable SKEXP0001

using Backend.Models;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel;

namespace Backend.Plugins
{
    public class FunctionProvider(
        ITextEmbeddingGenerationService textEmbeddingGenerationService,
        IVectorStore vectorStore,
        IFunctionKeyProvider functionKeyProvider) : IFunctionProvider
    {
        public async Task<List<KernelFunction>> GetBestFunctionsAsync(
            string collectionName,
            string request,
            KernelPluginCollection plugins,
            int numberOfBestFunctions,
            CancellationToken cancellationToken = default)
        {
            // Generate embedding for original request.
            var requestEmbedding = await textEmbeddingGenerationService.GenerateEmbeddingAsync(request, cancellationToken: cancellationToken);

            var collection = vectorStore.GetCollection<string, FunctionRecord>(collectionName);
            await collection.CreateCollectionIfNotExistsAsync(cancellationToken);

            // Find best functions to call for original request.
            var searchResults = await collection.VectorizedSearchAsync(requestEmbedding, new() { Top = numberOfBestFunctions }, cancellationToken);
            var recordKeys = (await searchResults.Results.ToListAsync(cancellationToken)).Select(l => l.Record.Id);

            return plugins
                .SelectMany(plugin => plugin)
                .Where(function => recordKeys.Contains(functionKeyProvider.GetFunctionKey(function)))
                .ToList();
        }
    }
}
