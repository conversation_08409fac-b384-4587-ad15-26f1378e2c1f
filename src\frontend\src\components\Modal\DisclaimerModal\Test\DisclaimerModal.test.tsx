import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, Mock } from 'vitest';
import DisclaimerModal from '../DisclaimerModal';
import Cookies from 'js-cookie';
import { AJG_ONE_URL } from '../../../../constants/constants';

vi.mock('js-cookie', () => {
    const actual = vi.importActual('js-cookie');
    return {
        ...actual,
        default: {
            get: vi.fn(),
            set: vi.fn(),
        },
    };
});

describe('DisclaimerModal', () => {
    it('should render the modal when cookie is not accepted', () => {
        (Cookies.get as Mock).mockReturnValue('false');
        render(<DisclaimerModal />);
        
        const acceptButton = screen.getByRole('button', { name: /Accept/i });
        const declineButton = screen.getByRole('link', { name: /Decline/i });

        expect(acceptButton).toBeInTheDocument();
        expect(declineButton).toBeInTheDocument();
    });

    it('should not render the modal when cookie is accepted', () => {
        (Cookies.get as Mock).mockReturnValue('true');
        render(<DisclaimerModal />);
        
        expect(screen.queryByRole('button', { name: /Accept/i })).not.toBeInTheDocument();
        expect(screen.queryByRole('link', { name: /Decline/i })).not.toBeInTheDocument();
    });

    it('should set cookie and hide modal on accept', () => {
        (Cookies.get as Mock).mockReturnValue('false');
        render(<DisclaimerModal />);
        
        const acceptButton = screen.getByRole('button', { name: /Accept/i });
        fireEvent.click(acceptButton);
        
        expect(Cookies.set).toHaveBeenCalledWith('disclaimerAccepted', 'true', { expires: 36500 });
        expect(screen.queryByRole('button', { name: /Accept/i })).not.toBeInTheDocument();
    });

    it('should navigate to AJG_ONE_URL on decline', () => {
        (Cookies.get as Mock).mockReturnValue('false');
        render(<DisclaimerModal />);
        
        const declineButton = screen.getByRole('link', { name: /Decline/i });
        expect(declineButton).toHaveAttribute('href', AJG_ONE_URL);
    });
});