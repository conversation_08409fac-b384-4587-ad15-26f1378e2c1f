module "gallagherai" {
  source = "./modules/gallagher_ai"

  region_instance      = var.region
  division_short       = local.division_short
  environment          = local.environment
  business_unit        = local.business_unit
  project_name         = local.project_name
  tenant_id            = data.azurerm_client_config.current.tenant_id
  subscription_id      = data.azurerm_client_config.current.subscription_id
  current_principal_id = data.azurerm_client_config.current.object_id
  tags                 = {}

  azurerm_resource_group_name        = data.azurerm_resource_group.main.name
  azurerm_resource_group_id          = data.azurerm_resource_group.main.id
  azurerm_resource_group_location    = data.azurerm_resource_group.main.location
  azurerm_log_analytics_workspace_id = azurerm_log_analytics_workspace.logs.id
  azurerm_private_endpoint_subnet_id = data.azurerm_subnet.private_link.id
  azurerm_delegated_subnet_id        = data.azurerm_subnet.private_link_webapp.id

  # App Service Plan
  appserviceplan_settings = var.appserviceplan_settings

  # Webapps
  webapp_settings         = var.webapp_settings
  outbound_dns_server     = var.outbound_dns_server
  outbound_alt_dns_server = var.outbound_alt_dns_server
  gotenberg_image_name    = local.gotenberg_image_name

  # AI Search
  search_settings    = var.search_settings
  search_dns_zone_id = var.search_dns_zone_id

  # Doc Intelligence
  docintelligence_settings = var.docintelligence_settings

  # Doc Translation
  doctranslate_settings = var.doctranslate_settings

  # Web Search
  # websearch_settings = var.websearch_settings

  # Storage regions
  additional_storage_regions = var.additional_storage_regions

  # Storage account
  storageaccount_settings = var.storageaccount_settings
  blob_dns_zone_id        = var.blob_dns_zone_id
  static_app_dns_zone_id  = var.static_app_dns_zone_id

  rbac_devadmin     = var.rbac_devadmin
  rbac_contributors = var.rbac_contributors
}