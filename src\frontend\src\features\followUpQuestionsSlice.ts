import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface FollowUpQuestionsState {
  questions: string[];
}

const initialState: FollowUpQuestionsState = {
  questions: [],
};

const followUpQuestionsSlice = createSlice({
  name: "followUpQuestions",
  initialState,
  reducers: {
    setFollowUpQuestions: (state, action: PayloadAction<string[]>) => {
      state.questions = action.payload;
    },
    clearFollowUpQuestions: (state) => {
      state.questions = [];
    },
  },
});

export const { setFollowUpQuestions, clearFollowUpQuestions } =
  followUpQuestionsSlice.actions;
export default followUpQuestionsSlice.reducer;
