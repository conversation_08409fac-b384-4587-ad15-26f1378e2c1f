module "appserviceplan" {
  source  = "app.terraform.io/ajg/appserviceplan/azurerm"
  version = "1.0.3"

  # Resource Metadata
  division            = var.division_short
  environment         = var.environment
  functionality       = "app"
  resource_group_name = var.azurerm_resource_group_name

  # Required inputs  
  os_type                = var.appserviceplan_settings.os_type
  sku_name               = var.appserviceplan_settings.sku_name
  zone_balancing_enabled = var.appserviceplan_settings.zone_balancing_enabled

  tags = merge(var.tags, { "environment" = var.environment })
}


module "gallagher-ai-frontend-linux-webapp" {
  source  = "app.terraform.io/ajg/linux-webapp/azurerm"
  version = "2.2.1"

  # Resource Metadata
  azurerm_location                       = var.azurerm_resource_group_location
  azurerm_resource_group_name            = var.azurerm_resource_group_name
  environment                            = var.environment
  division_code                          = var.division_short
  purpose                                = "WebApp Frontend"
  purpose_abbrevation                    = "aife"
  application_insights_retention_in_days = var.webapp_settings.application_insights_retention_in_days
  tags                                   = merge(var.tags, { "environment" = var.environment })

  # Required inputs
  azurerm_linux_web_app_service_plan_id       = module.appserviceplan.id
  application_insights_analytics_workspace_id = var.azurerm_log_analytics_workspace_id
  linux_web_app_sku_name                      = "P1v3" # NOTE: This is not currently used in the module, but required

  # Networking
  azurerm_private_endpoint_subnet_id = var.azurerm_private_endpoint_subnet_id
  azurerm_virtual_network_subnet_id  = var.azurerm_delegated_subnet_id

  linux_web_app_site_config = {
    application_stack = {
      node_version = "20-lts"
    }

    always_on                         = true
    ftps_state                        = "Disabled"
    http2_enabled                     = true
    websockets_enabled                = false
    health_check_path                 = "/#/healthz"
    health_check_eviction_time_in_min = 5
    minimum_tls_version               = "1.2"
    scm_minimum_tls_version           = "1.2"
    app_command_line                  = "pm2 serve /home/<USER>/wwwroot --no-daemon"
  }

  linux_web_app_app_settings = {
    ApplicationInsightsAgent_EXTENSION_VERSION = "~3",
    ASPNETCORE_ENVIRONMENT                     = "${upper(var.region_instance)}-${upper(var.environment)}"
  }
}

module "gallagher-ai-backend-linux-webapp" {
  source  = "app.terraform.io/ajg/linux-webapp/azurerm"
  version = "2.2.1"

  # Resource Metadata
  azurerm_location                       = var.azurerm_resource_group_location
  azurerm_resource_group_name            = var.azurerm_resource_group_name
  environment                            = var.environment
  division_code                          = var.division_short
  purpose                                = "WebApp Backend"
  purpose_abbrevation                    = "aibe"
  application_insights_retention_in_days = var.webapp_settings.application_insights_retention_in_days
  tags                                   = merge(var.tags, { "environment" = var.environment })

  # Required inputs
  azurerm_linux_web_app_service_plan_id       = module.appserviceplan.id
  application_insights_analytics_workspace_id = var.azurerm_log_analytics_workspace_id
  linux_web_app_sku_name                      = "P1v3" # NOTE: This is not currently used in the module, but required

  # Networking
  azurerm_private_endpoint_subnet_id = var.azurerm_private_endpoint_subnet_id
  azurerm_virtual_network_subnet_id  = var.azurerm_delegated_subnet_id

  linux_web_app_site_config = {
    application_stack = {
      dotnet_version = "8.0"
    }

    always_on                         = true
    ftps_state                        = "Disabled"
    http2_enabled                     = true
    websockets_enabled                = false
    health_check_path                 = "/healthz"
    health_check_eviction_time_in_min = 5
    minimum_tls_version               = "1.2"
    scm_minimum_tls_version           = "1.2"
  }

  linux_web_app_app_settings = {
    ApplicationInsightsAgent_EXTENSION_VERSION = "~3",
    ASPNETCORE_ENVIRONMENT                     = "${upper(var.region_instance)}-${upper(var.environment)}"
    WEBSITE_DNS_SERVER                         = var.outbound_dns_server
    WEBSITE_DNS_ALT_SERVER                     = var.outbound_alt_dns_server
    WEBSITES_ENABLE_APP_SERVICE_STORAGE        = true
  }
}

module "gallagher-ai-gott-linux-webapp" {
  source  = "app.terraform.io/ajg/linux-webapp/azurerm"
  version = "2.2.1"

  # Resource Metadata
  azurerm_location                       = var.azurerm_resource_group_location
  azurerm_resource_group_name            = var.azurerm_resource_group_name
  environment                            = var.environment
  division_code                          = var.division_short
  purpose                                = "Gottenberg"
  purpose_abbrevation                    = "aigo"
  application_insights_retention_in_days = var.webapp_settings.application_insights_retention_in_days
  tags                                   = merge(var.tags, { "environment" = var.environment })

  # Required inputs
  azurerm_linux_web_app_service_plan_id       = module.appserviceplan.id
  application_insights_analytics_workspace_id = var.azurerm_log_analytics_workspace_id
  linux_web_app_sku_name                      = "P1v3" # NOTE: This is not currently used in the module, but required

  # Networking
  azurerm_private_endpoint_subnet_id = var.azurerm_private_endpoint_subnet_id
  azurerm_virtual_network_subnet_id  = var.azurerm_delegated_subnet_id

  linux_web_app_site_config = {
    always_on                         = true
    ftps_state                        = "Disabled"
    http2_enabled                     = true
    websockets_enabled                = false
    health_check_path                 = "/health"
    health_check_eviction_time_in_min = 5
    minimum_tls_version               = "1.2"
    scm_minimum_tls_version           = "1.2"
    application_stack = {
      docker_image_name   = var.gotenberg_image_name
      docker_registry_url = "https://index.docker.io"
    }
  }

  linux_web_app_app_settings = {
    ApplicationInsightsAgent_EXTENSION_VERSION = "~3",
    ASPNETCORE_ENVIRONMENT                     = "${upper(var.region_instance)}-${upper(var.environment)}"
    WEBSITE_DNS_SERVER                         = var.outbound_dns_server
    WEBSITE_DNS_ALT_SERVER                     = var.outbound_alt_dns_server
  }
}