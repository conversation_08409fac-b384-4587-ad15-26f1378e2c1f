import { api } from '../../app/api';

// Define response types
interface TextTranslationResponse {
  translatedText: string;
}

export const translationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Translate text
    translateText: builder.mutation<TextTranslationResponse, { text: string; language: string }>({
      query: ({ text, language }) => {
        const formData = new FormData();
        formData.append('text', text);
        formData.append('language', language);

        return {
          url: '/translate/text',
          method: 'POST',
          body: formData,
        };
      },
    }),

    // Translate document
    translateDocument: builder.mutation<ReadableStream<Uint8Array>, { file: File; language: string }>({
      query: ({ file, language }) => {
        const formData = new FormData();
        formData.append('document', file);
        formData.append('language', language);

        return {
          url: '/translate/translate',
          method: 'POST',
          body: formData,
          responseHandler: 'content-type',
        };
      },
      invalidatesTags: ['Translation' as const],
    }),

    // Get translated documents
    getTranslatedDocuments: builder.query<any[], void>({
      query: () => '/translate/documents',
      providesTags: ['Translation' as const],
    }),

    // Delete translated document
    deleteTranslatedDocument: builder.mutation<void, string>({
      query: (documentId) => ({
        url: `/translate/documents/${documentId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Translation' as const],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useTranslateTextMutation,
  useTranslateDocumentMutation,
  useGetTranslatedDocumentsQuery,
  useDeleteTranslatedDocumentMutation,
} = translationApi;

// Export a utility function to handle streaming document translation
export const streamDocumentTranslation = async (
  file: File,
  language: string,
  token: string
): Promise<ReadableStream<Uint8Array>> => {
  const formData = new FormData();
  formData.append('document', file);
  formData.append('language', language);

  const baseUrl = import.meta.env.VITE_API_URL;
  const response = await fetch(`${baseUrl}/translate/translate`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText);
  }

  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('Response body is null');
  }

  return new ReadableStream({
    start(controller) {
      function push() {
        if (reader) {
          reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }
            controller.enqueue(value);
            push();
          }).catch((err) => {
            controller.error(err);
          });
        }
      }
      push();
    },
    cancel() {
      if (reader) {
        reader.cancel();
      }
    },
  });
};
