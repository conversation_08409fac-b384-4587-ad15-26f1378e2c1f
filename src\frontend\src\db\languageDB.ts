import { LANGUAGE_SETTINGS_ID, Stores, version as initialVersion } from '../constants/dbConstants';
import { LanguageSettingsType } from '../interfaces';

// Variable dedicated for this specific connection
let languageDB: IDBDatabase | null = null;

export const initLanguageSettingDB = async (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    // Close any existing connection
    if (languageDB) {
      languageDB.close();
      languageDB = null;
    }

    // First, open without version to get current version
    const versionRequest = indexedDB.open('Settings');
    
    versionRequest.onsuccess = () => {
      const currentVersion = versionRequest.result.version;
      versionRequest.result.close();
      
      // Now open with the correct version
      openDatabaseWithVersion(currentVersion, resolve, reject);
    };
    
    versionRequest.onerror = () => {
      // If we can't even open without version, try with default
      // console.log("Error getting current database version, using default");
      openDatabaseWithVersion(initialVersion, resolve, reject);
    };
  });
};

const openDatabaseWithVersion = (version: number, resolve: (value: boolean) => void, reject: (reason: any) => void) => {
  // console.log(`Opening Settings database with version ${version}`);
  const request = indexedDB.open('Settings', version);
  
  request.onupgradeneeded = () => {
    // console.log(`Database upgrade from v${event.oldVersion} to v${event.newVersion}`);
    const db = request.result;
    
    // Create stores as needed
    if (!db.objectStoreNames.contains(Stores.LanguageSettings)) {
      // console.log('Creating LanguageSettings store');
      db.createObjectStore(Stores.LanguageSettings, { keyPath: 'id' });
    }
    
    // Create other stores if needed
    if (!db.objectStoreNames.contains(Stores.UserSettings)) {
      // console.log('Creating UserSettings store');
      db.createObjectStore(Stores.UserSettings, { keyPath: 'id' });
    }
  };
  
  request.onsuccess = async () => {
    languageDB = request.result;
    
    // Check if stores exist
    const storeExists = Array.from(languageDB.objectStoreNames).includes(Stores.LanguageSettings);
    // console.log('Store LanguageSettings exists:', storeExists);
    
    if (!storeExists && version === request.result.version) {
      // Store doesn't exist and we're at the current version - need to upgrade
      languageDB.close();
      languageDB = null;
      
      // console.log(`Store LanguageSettings missing, incrementing version from ${version} to ${version + 1}`);
      // Try to open with a higher version
      openDatabaseWithVersion(version + 1, resolve, reject);
      return;
    }
    
    resolve(true);
  };
  
  request.onerror = () => {
    // console.error('Error opening language settings database:', request.error);
    reject(false);
  };
};

export const getLanguageSetting = async (id: string): Promise<LanguageSettingsType> => {
  // Make sure we have a valid connection
  if (!languageDB) {
    const initialized = await initLanguageSettingDB();
    if (!initialized) {
      // If initialization failed, try once more
      await initLanguageSettingDB();
    }
  }
  
  // Check if the store exists before trying to access it
  if (!languageDB || !Array.from(languageDB.objectStoreNames).includes(Stores.LanguageSettings)) {
    console.error('LanguageSettings store not available');
    // Return default settings if we can't access the store
    return {
      id: LANGUAGE_SETTINGS_ID,
      selectedLanguage: '',
    };
  }

  return new Promise((resolve) => {
    try {
      const transaction = languageDB!.transaction([Stores.LanguageSettings], 'readonly');
      const store = transaction.objectStore(Stores.LanguageSettings);
      const request = store.get(id);

      request.onsuccess = () => {
        const defaultSetting: LanguageSettingsType = {
          id: LANGUAGE_SETTINGS_ID,
          selectedLanguage: '',
        };
        const savedSetting = request.result;
        const mergedSetting = { ...defaultSetting, ...savedSetting };
        resolve(mergedSetting);
      };

      request.onerror = () => {
        console.error('Error getting language setting:', request.error);
        resolve({
          id: LANGUAGE_SETTINGS_ID,
          selectedLanguage: '',
        });
      };
    } catch (error) {
      console.error('Exception in getLanguageSetting:', error);
      resolve({
        id: LANGUAGE_SETTINGS_ID,
        selectedLanguage: '',
      });
    }
  });
};

export const saveLanguageSetting = async (setting: LanguageSettingsType): Promise<boolean> => {
  // Make sure we have a valid connection
  if (!languageDB) {
    const initialized = await initLanguageSettingDB();
    if (!initialized) {
      // If initialization failed, try once more
      await initLanguageSettingDB();
    }
  }
  
  // Check if the store exists before trying to access it
  if (!languageDB || !Array.from(languageDB.objectStoreNames).includes(Stores.LanguageSettings)) {
    console.error('LanguageSettings store not available for saving');
    return false;
  }

  return new Promise((resolve) => {
    try {
      const transaction = languageDB!.transaction([Stores.LanguageSettings], 'readwrite');
      const store = transaction.objectStore(Stores.LanguageSettings);

      // No need to delete first, put will overwrite if it exists
      const request = store.put(setting);

      request.onsuccess = () => {
        // console.log('Language setting saved successfully');
        resolve(true);
      };

      request.onerror = () => {
        console.error('Error saving language setting:', request.error);
        resolve(false);
      };
      
      transaction.oncomplete = () => {
        // console.log('Transaction completed successfully');
      };
      
      transaction.onerror = () => {
        console.error('Transaction error:', transaction.error);
      };
    } catch (error) {
      console.error('Exception in saveLanguageSetting:', error);
      resolve(false);
    }
  });
};