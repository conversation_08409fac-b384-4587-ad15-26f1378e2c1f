environment                 = "test"
region                      = "us"
azurerm_resource_group_name = "corp-gallagherai-us-main-rg"

rbac_devadmin = ["07691ecb-a8e5-4036-b1ae-af3d13b48463",     #u-Corp-Architects
"e4c13c82-2c7f-481d-ad69-daa108781f8f"]                      #u-CorpOD_CORP_SmartMarket_Developers (<PERSON>, <PERSON>)]
rbac_contributors = ["6a5bd903-1b3e-4e71-8dce-8897e58ffb9e"] #corp-openai-test-gallaghergpt-cicd (Az Devops CICD Service Principal)

additional_storage_regions = ["Canada Central"]

webapp_settings = {
  application_insights_retention_in_days = 30
}

search_settings = {
  sku_name      = "standard"
  semantic_sku  = "standard"
  replica_count = 1
}

docintelligence_settings = {
  sku_name                   = "S0"
  kind                       = "FormRecognizer"
  dynamic_throttling_enabled = false
}

nw_vnet_name               = "corp-test-pvnet"
nw_rg_name                 = "network-rg"
nw_subnet_privatelink_name = "pe1"                     # **************/26
nw_subnet_webapp_name      = "gallagherai-integration" # **************/28