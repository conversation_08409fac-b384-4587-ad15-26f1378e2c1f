﻿using Backend.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace Backend.Controllers
{
    [Authorize]
    [EnableCors]
    [ApiController]
    [Route("/encryption")]
    public class EncryptionController : ControllerBase
    {
        private readonly IEncryptionService _encService;
        private readonly IConfiguration _config;
        private string _key; 

        public EncryptionController(IEncryptionService encService, IConfiguration config)
        {
            _encService = encService;
            _config = config;
            _key = _config["GallagherAI-HistoryKey"] ?? ""; // from kv
        }

        [HttpPost]
        [Route("encrypt")]
        public IActionResult OnPostEncrypt([FromBody] EncryptItem item, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var response = string.Empty;
                
                try
                {
                    response = _encService.Encrypt(item.Data, _key, item.Email);
                }
                catch (Exception ex)
                {
                    return BadRequest(ex.Message);
                }                
                
                return Ok(response);
            }
            return new EmptyResult();
        }

        [HttpPost]
        [Route("decrypt")]
        public IActionResult OnPostDecrypt([FromBody] EncryptItem item, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var response = string.Empty;

                try
                {
                    response = _encService.Decrypt(item.Data, _key, item.Email);
                }
                catch (Exception ex)
                {
                    return BadRequest("Error decrypting. " + ex.Message);
                }

                return Ok(response);
            }
            return new EmptyResult();
        }
    }
}
