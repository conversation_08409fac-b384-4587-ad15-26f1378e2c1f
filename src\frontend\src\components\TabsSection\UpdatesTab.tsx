import React from "react";

const updates = [
  {
    date: "May 29, 2025",
    changes: [
      "Improved handling of context in responses from the AI.",
      "Custom prompt templates added in Settings.",
      "Stability and bug fixes."
    ],
  },
  {
    date: "April 24, 2025",
    changes: [
      "Text Translation feature.",
      "Stability and bug fixes."
    ],
  },
  {
    date: "March 27, 2025",
    changes: [
      "Document Translation feature.",      
      "Predictive prompts on responses from the AI.",
      "Stability and bug fixes."
    ],
  },
  {
    date: "February 27, 2025",
    changes: [
      "New navigation layout.",
      "Dark mode support.",
      "User configurable settings for AI creativity.",
      "Stability and bug fixes.",
    ],
  },
  {
    date: "January 30, 2025",
    changes: [
      "Document expiration - If documents remain unused in the system, they will be deleted after 6 months.",
      "Allow files up to 300 pages and 40MB in size to be used in workspaces.",
      "Bug fixes.",
    ],
  },
  {
    date: "December 12, 2024",
    changes: [
      "Conversations limited to 50 prompts and responses.",
      "Bug fixes.",
    ],
  },
  {
    date: "November 21, 2024",
    changes: [
      "Support for Word and PowerPoint files.",
      "Allow files up to 200 pages.",
      "Bug fixes.",
    ],
  },
  {
    date: "October 31, 2024",
    changes: [
      "Chat with image feature.",
      "Drag-and-drop files to upload.",
      "Bug fixes.",
    ],
  },
  {
    date: "October 17, 2024",
    changes: ["Stability and bug fixes."],
  },
  {
    date: "October 4, 2024",
    changes: ["Initial launch of a major update to Gallagher AI."],
  },
];

const UpdatesTab: React.FC = React.memo(() => {
  return (
    <div className="h-full flex justify-center bg-white dark:bg-zinc-800 dark:text-white">
      <div className="flex flex-col h-full p-4 space-y-4 overflow-y-auto w-full max-w-3xl mx-auto">
        <h2 className="text-lg font-semibold text-left">Application Updates</h2>
        <div>
          Reminder - Gallagher AI{" "}
          <a
            href="https://ping.ajg.com/idp/startSSO.ping?PartnerSpId=CornerStone&TargetResource=%252fDeepLink%252fProcessRedirect.aspx%253fmodule%253dlodetails%2526lo%253da4f61590-a2c9-4b88-b261-f73c89c55b81"
            target="_blank"
            className="text-gallagher-dark-300 dark:text-sky-400 underline underline-offset-6 decoration-gallagher-dark-300"
          >
            training videos are available
          </a>
          .
        </div>
        {updates.map((update, index) => (
          <div key={index}>
            <b>
              <i>{update.date}</i>
            </b>
            <ul className="list-disc pl-5 space-y-1">
              {update.changes.map((change, idx) => (
                <li key={idx}>{change}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
});

export default UpdatesTab;