import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface RegionState {
  region: string;
}

const initialState: RegionState = {
  region: "",
};

const regionSlice = createSlice({
  name: "region",
  initialState,
  reducers: {
    setRegion: (state, action: PayloadAction<string>) => {
      state.region = action.payload;
    },
  },
});

export const { setRegion } = regionSlice.actions;

export default regionSlice.reducer;