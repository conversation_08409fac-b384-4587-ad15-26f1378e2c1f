import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TranslationFileUploadFlagState } from "../interfaces";

const initialState: TranslationFileUploadFlagState = {
  status: false,
  newDocument: false,
};

const translationFilesUpload = createSlice({
  name: "status",
  initialState,
  reducers: {
    setTranslationFilesUpload: (state, action: PayloadAction<boolean>) => {
      state.status = action.payload;
    },
    setNewDocument: (state, action: PayloadAction<boolean>) => {
      state.newDocument = action.payload;
    }
  },
});

export const { setTranslationFilesUpload , setNewDocument} = translationFilesUpload.actions;

export default translationFilesUpload.reducer;
