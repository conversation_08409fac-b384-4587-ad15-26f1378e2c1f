using Backend.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json.Nodes;

namespace BackendIntegrationTests
{
    public class BackendIntegrationTests
    {
        internal readonly BackendWebAppFactory<Backend.Program> Factory;
        internal readonly HttpClient Client;
        internal readonly IStorageService StorageService;
        internal readonly InMemorySearch SearchService;
        internal JsonObject Settings;

        public BackendIntegrationTests()
        {
            Factory = new BackendWebAppFactory<Backend.Program>();
            Client = Factory.CreateClient();
            Client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", GenerateJwtToken());
            Settings = GenerateSettings();
            StorageService = Factory.Services.GetRequiredService<IStorageService>();
        }

        private static readonly byte[] JwtSecretKey = GenerateRandomKey();

        private static byte[] GenerateRandomKey()
        {
            var key = new byte[32]; // 256 bits
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(key);
            }
            return key;
        }

        private string GenerateJwtToken()
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = JwtSecretKey;
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.Name, "userTest"),
                    new Claim("xms_pdl", "NAM"),
                    new Claim("OID", "userTest")
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private JsonObject GenerateSettings(int top = 3,
                                            bool sr = true,
                                            RetrievalMode mode = RetrievalMode.Hybrid,
                                            bool captions = false,
                                            float rscore = 0.01f,
                                            float sscore = 0.1f,
                                            bool followup = false)
        {
            return new JsonObject
            {
                ["search"] = new JsonObject
                {
                    ["top"] = top,
                    ["semantic_ranker"] = sr,
                    ["retrieval_mode"] = mode.ToString(),
                    ["semantic_captions"] = captions,
                    ["minimum_reranker_score"] = rscore,
                    ["minimum_search_score"] = sscore
                },
                ["suggest_followup_questions"] = followup
            };
        }
    }
}

//        //[Fact]
//        //public async Task GET_Documents_ReturnsOkResult()
//        //{
//        //    // Arrange
//        //    var documents = new Document
//        //    {
//        //        documents = new[] { "doc1.txt", "doc2.txt" }
//        //    };

//        //    _mockBlobService.Setup(service => service.GetDocuments(oid + "/" + id))
//        //                .ReturnsAsync(documents);

//        //    // Act
//        //    var result = await _documents.OnGetDocumentsAsync(id, CancellationToken.None);

//        //    // Assert
//        //    var okResult = Assert.IsType<OkObjectResult>(result);
//        //    var returnValue = Assert.IsType<Document>(okResult.Value);
//        //    Assert.Equal(2, returnValue.documents.Length);
//        //}

//        //[Fact]
//        //public async Task DOWNLOAD_Document_ReturnsOkResult()
//        //{
//        //    // Arrange
//        //    var documentStream = new MemoryStream();

//        //    _mockBlobService.Setup(service => service.GetDocumentStreamAsync(oid + "/" + id + "/" + doc))
//        //                .ReturnsAsync(documentStream);

//        //    // Act
//        //    var result = await _documents.OnDownloadDocumentAsync(id, doc, CancellationToken.None);

//        //    // Assert
//        //    var fileStreamResult = Assert.IsType<FileStreamResult>(result);
//        //    Assert.Equal("application/octet-stream", fileStreamResult.ContentType);
//        //    Assert.Equal(doc, fileStreamResult.FileDownloadName);
//        //}

//        //[Fact]
//        //public async Task UPLOAD_Documents_ReturnsOkResult()
//        //{
//        //    // Arrange
//        //    var documents = new FormFileCollection
//        //    {
//        //        new FormFile(new MemoryStream(), 0, 0, "doc1", "doc1.txt"),
//        //        new FormFile(new MemoryStream(), 0, 0, "doc2", "doc2.txt")
//        //    };
//        //    var uploadedDocuments = new Document
//        //    {
//        //        documents = new[] { "doc1.txt", "doc2.txt" }
//        //    };

//        //    _mockBlobService.Setup(service => service.UploadDocumentsAsync(oid + "/" + id, documents, It.IsAny<CancellationToken>()))
//        //                .ReturnsAsync(uploadedDocuments);

//        //    // Act
//        //    var result = await _documents.OnPostDocumentsAsync(id, documents, CancellationToken.None);

//        //    // Assert
//        //    var okResult = Assert.IsType<OkObjectResult>(result);
//        //    var returnValue = Assert.IsType<Document>(okResult.Value);
//        //    Assert.Equal(2, returnValue.documents.Length);
//        //}

//        //[Fact]
//        //public async Task DELETE_Document_ReturnsOkResult()
//        //{
//        //    // Arrange
//        //    _mockBlobService.Setup(service => service.DeleteBlobAsync(oid + "/" + id + "/" + doc))
//        //                .Returns(Task.CompletedTask);

//        //    // Act
//        //    var result = await _documents.OnDeleteDocumentAsync(id, doc, CancellationToken.None);

//    // Assert
//    Assert.IsType<NoContentResult>(result);
//}

/******************************************
 *   Helper functions to create objects
 ******************************************/