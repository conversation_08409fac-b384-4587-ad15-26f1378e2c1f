﻿using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Settings which will affect the behavior of the application..
    /// </summary>
    public class Settings
    {
        /// <summary>
        /// Azure OpenAI settings.
        /// </summary>
        [JsonPropertyName("OpenAI")]
        public OpenAISettings? OpenAI { get; set; }

        /// <summary>
        /// Gets or sets the search settings to be used.
        /// </summary>
        [JsonPropertyName("search")]
        public SearchSettings? Search { get; set; }

        /// <summary>
        /// Gets or sets the system prompt template to be used.
        /// </summary>
        [JsonPropertyName("prompt_template")] 
        public string? PromptTemplate { get; set; }

        /// <summary>
        /// Asks the LLM to suggest follow-up questions based on the user's query.
        /// </summary>
        [JsonPropertyName("suggest_followup_questions")]
        public bool? SuggestFollowupQuestions { get; set; }
    }
}
