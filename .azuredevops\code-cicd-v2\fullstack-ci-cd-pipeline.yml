name: Fullstack-CI-CD-Pipeline

trigger: none

variables:
  - template: variables.yml

parameters:
  - name: regions
    displayName: 'Regions to deploy'
    type: object
    default:
      - US
      - UK
      - AU

stages:
  # BUILD STAGE
  - stage: Build
    displayName: 'Build'
    jobs:
      # Backend Build Job (using Windows agent)
      - job: BuildBackend
        displayName: 'Build Backend'
        pool:
          vmImage: 'windows-latest'
        steps:
          # Install .NET 8
          - task: UseDotNet@2
            displayName: 'Install .NET 8'
            inputs:
              version: 8.x
              performMultiLevelLookup: true
              includePreviewVersions: false

          # Publish Backend
          - task: DotNetCoreCLI@2
            displayName: 'Publish Backend'
            inputs:
              command: 'publish'
              projects: '**/Backend.csproj'
              publishWebProjects: false
              arguments: '--configuration $(buildConfiguration) --output $(Agent.TempDirectory)'
              zipAfterPublish: false

          # Find and publish each WebJob to a separate folder
          - powershell: |
              Get-ChildItem -Path "$(Build.SourcesDirectory)/src/scripts" -Recurse -Filter *.csproj |
                Where-Object { $_.Name -notmatch 'common.csproj' } |
                ForEach-Object {
                  $projectName = $_.BaseName
                  Write-Host "Publishing $projectName"
                  $outputFolder = Join-Path "$(Agent.TempDirectory)/backend/App_Data/jobs/triggered" $projectName
                  dotnet publish $_.FullName --configuration $(buildConfiguration) --output $outputFolder
                }
            displayName: 'Publish WebJobs to separate folders'

          # Archive Backend
          - task: ArchiveFiles@2
            displayName: 'Zip Backend'
            inputs:
              rootFolderOrFile: '$(Agent.TempDirectory)/backend'
              includeRootFolder: false
              archiveType: 'zip'
              archiveFile: '$(Build.ArtifactStagingDirectory)/backend.zip'
              replaceExistingArchive: true

          # Security analysis with Snyk for backend
          - template: /.azuredevops/snyk/snyk-sast-sca-windows.yml

          # Publish artifacts
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Backend Artifacts'
            inputs:
              pathtoPublish: '$(Build.ArtifactStagingDirectory)/backend.zip'
              artifactName: 'backend'

      # Frontend Build Job (using Ubuntu agent)
      - job: BuildFrontend
        displayName: 'Build Frontend'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - script: |
              corepack enable
              corepack prepare pnpm@9.12.1 --activate
            displayName: 'Install pnpm'

          - script: |
              cd $(Build.SourcesDirectory)/src/frontend
              pnpm install --no-frozen-lockfile
              pnpm run build
            displayName: 'Install Node Dependencies with pnpm'

          - task: ArchiveFiles@2
            displayName: 'Archive dist folder'
            inputs:
              rootFolderOrFile: '$(Build.SourcesDirectory)/src/frontend/dist'
              includeRootFolder: false
              archiveFile: '$(Build.ArtifactStagingDirectory)/frontend.zip'
              replaceExistingArchive: false

          # Security analysis with Snyk for frontend
          - template: /.azuredevops/snyk/snyk-sast-sca-ubuntu.yml

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Frontend Artifacts'
            inputs:
              pathtoPublish: '$(Build.ArtifactStagingDirectory)/frontend.zip'
              artifactName: 'frontend'

  # DEV DEPLOYMENT STAGE
  - stage: DeployToDev
    displayName: 'Deploy to DEV'
    dependsOn: Build
    jobs:
      # Backend Deployment Jobs
      - ${{ each region in parameters.regions }}:
        - template: backend-deploy-job.yml
          parameters:
            environment: ${{ format('{0}_DEV', region) }}
            service_connection: ${{ variables[format('{0}_DEV_ServiceConnection', region)] }}
            deploy_agent_pool: ${{ variables[format('{0}_DEV_DeployAgentPool', region)] }}
            app_name: ${{ variables[format('{0}_DEV_beAppName', region)] }}
            job_name_suffix: 'BE'  # Add suffix to distinguish backend jobs

      # Frontend Deployment Jobs
      - ${{ each region in parameters.regions }}:
        - template: frontend-deploy-job.yml
          parameters:
            environment: ${{ format('{0}_DEV', region) }}
            api_url: ${{ variables[format('{0}_DEV_API_URL', region)] }}
            scopes: ${{ variables[format('{0}_DEV_Scopes', region)] }}
            client_id: ${{ variables[format('{0}_DEV_Client_ID', region)] }}
            authority: ${{ variables[format('{0}_DEV_Authority', region)] }}
            region: ${{ variables[format('{0}_DEV_Region', region)] }}
            connectionString: ${{ variables[format('{0}_DEV_ConnectionString', region)] }}
            service_connection: ${{ variables[format('{0}_DEV_ServiceConnection', region)] }}
            deploy_agent_pool: ${{ variables[format('{0}_DEV_DeployAgentPool', region)] }}
            app_name: ${{ variables[format('{0}_DEV_feAppName', region)] }}
            job_name_suffix: 'FE'  # Add suffix to distinguish frontend jobs

  # MANUAL VALIDATION BEFORE TEST
  - stage: ManualValidationForTest
    displayName: 'TEST deploy approval'
    dependsOn: DeployToDev
    jobs:
      - job: WaitForValidation
        displayName: 'Wait for Validation'
        pool: server
        timeoutInMinutes: 60
        steps:
          - task: ManualValidation@0
            displayName: 'Manual Validation'
            inputs:
              notifyUsers: ''
              instructions: 'Please validate the DEV deployment before deploying to TEST environment'
              onTimeout: 'reject'

  # TEST DEPLOYMENT STAGE
  - stage: DeployToTest
    displayName: 'Deploy to TEST'
    dependsOn: ManualValidationForTest
    jobs:
      # Backend Deployment Jobs
      - ${{ each region in parameters.regions }}:
        - template: backend-deploy-job.yml
          parameters:
            environment: ${{ format('{0}_TEST', region) }}
            service_connection: ${{ variables[format('{0}_TEST_ServiceConnection', region)] }}
            deploy_agent_pool: ${{ variables[format('{0}_TEST_DeployAgentPool', region)] }}
            app_name: ${{ variables[format('{0}_TEST_beAppName', region)] }}
            job_name_suffix: 'BE'  # Add suffix to distinguish backend jobs

      # Frontend Deployment Jobs
      - ${{ each region in parameters.regions }}:
        - template: frontend-deploy-job.yml
          parameters:
            environment: ${{ format('{0}_TEST', region) }}
            api_url: ${{ variables[format('{0}_TEST_API_URL', region)] }}
            scopes: ${{ variables[format('{0}_TEST_Scopes', region)] }}
            client_id: ${{ variables[format('{0}_TEST_Client_ID', region)] }}
            authority: ${{ variables[format('{0}_TEST_Authority', region)] }}
            region: ${{ variables[format('{0}_TEST_Region', region)] }}
            connectionString: ${{ variables[format('{0}_TEST_ConnectionString', region)] }}
            service_connection: ${{ variables[format('{0}_TEST_ServiceConnection', region)] }}
            deploy_agent_pool: ${{ variables[format('{0}_TEST_DeployAgentPool', region)] }}
            app_name: ${{ variables[format('{0}_TEST_feAppName', region)] }}
            job_name_suffix: 'FE'  # Add suffix to distinguish frontend jobs

  # MANUAL VALIDATION BEFORE PROD
  - stage: ManualValidationForProd
    displayName: 'Proceed to PROD deploy?'
    dependsOn: DeployToTest
    jobs:
      - job: WaitForValidation
        displayName: 'Wait for Validation'
        pool: server
        timeoutInMinutes: 180
        steps:
          - task: ManualValidation@0
            displayName: 'Manual Validation'
            inputs:
              notifyUsers: ''
              instructions: 'Please, approve or reject to start the PROD deployment stage'
              onTimeout: 'reject'

  # PROD DEPLOYMENT STAGE - WITH MANUAL TRIGGER AND IMPROVED NOTIFICATIONS
  - stage: DeployToProd
    displayName: 'Deploy to PROD'
    dependsOn: ManualValidationForProd
    jobs:
      - deployment: Approval
        displayName: 'Deployment Approval'
        environment: 'ProdApproval'
        strategy:
          runOnce:
            deploy:
              steps:
                - script: echo "Approval required to proceed with PROD deployment"
                  displayName: 'Notify Approval Required'

      # Backend Deployment Jobs
      - ${{ each region in parameters.regions }}:
        - template: backend-deploy-job.yml
          parameters:
            environment: ${{ format('{0}_PROD', region) }}
            service_connection: ${{ variables[format('{0}_PROD_ServiceConnection', region)] }}
            deploy_agent_pool: ${{ variables[format('{0}_PROD_DeployAgentPool', region)] }}
            app_name: ${{ variables[format('{0}_PROD_beAppName', region)] }}
            job_name_suffix: 'BE'  # Add suffix to distinguish backend jobs
            dependsOn: Approval

      # Frontend Deployment Jobs
      - ${{ each region in parameters.regions }}:
        - template: frontend-deploy-job.yml
          parameters:
            environment: ${{ format('{0}_PROD', region) }}
            api_url: ${{ variables[format('{0}_PROD_API_URL', region)] }}
            scopes: ${{ variables[format('{0}_PROD_Scopes', region)] }}
            client_id: ${{ variables[format('{0}_PROD_Client_ID', region)] }}
            authority: ${{ variables[format('{0}_PROD_Authority', region)] }}
            region: ${{ variables[format('{0}_PROD_Region', region)] }}
            connectionString: ${{ variables[format('{0}_PROD_ConnectionString', region)] }}
            service_connection: ${{ variables[format('{0}_PROD_ServiceConnection', region)] }}
            deploy_agent_pool: ${{ variables[format('{0}_PROD_DeployAgentPool', region)] }}
            app_name: ${{ variables[format('{0}_PROD_feAppName', region)] }}
            job_name_suffix: 'FE'  # Add suffix to distinguish frontend jobs
            dependsOn: Approval
