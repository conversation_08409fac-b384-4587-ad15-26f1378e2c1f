# Reference to the current connection used for 
data "azurerm_client_config" "current" {}

# Reference for resource groups that need to exist
data "azurerm_resource_group" "main" {
  name = var.azurerm_resource_group_name
}

# Private link subnet, comment out or rename if not required
data "azurerm_subnet" "private_link" {
  name                 = var.nw_subnet_privatelink_name
  virtual_network_name = var.nw_vnet_name
  resource_group_name  = var.nw_rg_name
}

# Private link webapp subnet, comment out or rename if not required
data "azurerm_subnet" "private_link_webapp" {
  name                 = var.nw_subnet_webapp_name
  virtual_network_name = var.nw_vnet_name
  resource_group_name  = var.nw_rg_name
}