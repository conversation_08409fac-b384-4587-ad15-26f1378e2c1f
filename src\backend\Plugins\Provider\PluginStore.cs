﻿#pragma warning disable SKEXP0001

using Backend.Models;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel;

namespace Backend.Plugins
{
    public class PluginStore(
        ITextEmbeddingGenerationService textEmbeddingGenerationService,
        IVectorStore vectorStore,
        IFunctionKeyProvider functionKeyProvider) : IPluginStore
    {
        public async Task SaveAsync(string collectionName, KernelPluginCollection plugins, CancellationToken cancellationToken = default)
        {
            // Collect data about imported functions in kernel.
            var functionRecords = new List<FunctionRecord>();
            var functionsData = GetFunctionsData(plugins);

            // Generate embedding for each function.
            var embeddings = await textEmbeddingGenerationService
                .GenerateEmbeddingsAsync(functionsData.Select(l => l.TextToVectorize).ToArray(), cancellationToken: cancellationToken);

            // Create vector store record instances with function information and embedding.
            for (var i = 0; i < functionsData.Count; i++)
            {
                var (function, functionInfo) = functionsData[i];

                functionRecords.Add(new FunctionRecord
                {
                    Id = functionKeyProvider.GetFunctionKey(function),
                    FunctionInfo = functionInfo,
                    FunctionInfoEmbedding = embeddings[i]
                });
            }

            // Create collection and upsert all vector store records for search.
            // It's possible to do it only once and re-use the same functions for future requests.
            var collection = vectorStore.GetCollection<string, FunctionRecord>(collectionName);
            await collection.CreateCollectionIfNotExistsAsync(cancellationToken);

            await collection.UpsertBatchAsync(functionRecords, cancellationToken: cancellationToken).ToListAsync(cancellationToken);
        }

        private static List<(KernelFunction Function, string TextToVectorize)> GetFunctionsData(KernelPluginCollection plugins)
            => plugins
                .SelectMany(plugin => plugin)
                .Select(function => (function, $"Plugin name: {function.PluginName}. Function name: {function.Name}. Description: {function.Description}"))
                .ToList();
    }
}
