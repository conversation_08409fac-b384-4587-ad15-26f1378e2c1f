variable "environment" {
  type        = string
  description = "Environment to deploy resources into"
}

variable "region" {
  type        = string
  description = "Region where resources will be deployed."
}

variable "azurerm_resource_group_name" {
  type        = string
  description = "Resource Group Name from Azure for deployment into this environment"
}

variable "rbac_devadmin" {
  description = "List of devadmin object ids"
  type        = list(string)
}

variable "rbac_contributors" {
  description = "List of contributor object ids"
  type        = list(string)
}

variable "blob_dns_zone_id" {
  description = "DNS Zone ID for Blob"
  type        = string
  default     = "/subscriptions/9b2f9fb3-2e3c-4a93-833a-9bdda2da2ecd/resourceGroups/dns-main-rg/providers/Microsoft.Network/privateDnsZones/privatelink.blob.core.windows.net"
}

variable "search_dns_zone_id" {
  description = "DNS Zone ID for Search"
  type        = string
  default     = "/subscriptions/9b2f9fb3-2e3c-4a93-833a-9bdda2da2ecd/resourceGroups/dns-main-rg/providers/Microsoft.Network/privateDnsZones/privatelink.search.windows.net"
}

variable "static_app_dns_zone_id" {
  description = "DNS Zone ID for Static App"
  type        = string
  default     = "/subscriptions/9b2f9fb3-2e3c-4a93-833a-9bdda2da2ecd/resourceGroups/dns-main-rg/providers/Microsoft.Network/privateDnsZones/privatelink.5.azurestaticapps.net"
}

variable "search_settings" {
  type = object({
    sku_name      = string,
    semantic_sku  = string,
    replica_count = number
  })
  description = "Settings for Azure Search"
}

variable "docintelligence_settings" {
  type = object({
    sku_name                   = string,
    kind                       = string,
    dynamic_throttling_enabled = bool
  })
  description = "Settings for Azure Document Intelligence"
}

variable "doctranslate_settings" {
  type = object({
    sku_name = string,
    kind     = string
  })
  description = "Settings for Azure AI Translation"
  default = {
    sku_name = "S1"
    kind     = "TextTranslation"
  }
}

#variable "websearch_settings" {
#  type = object({
#    sku_name = string,
#    kind     = string
#  })
#  description = "Settings for Azure Web Search"
#  default = {
#    sku_name = "S1"
#    kind     = "Bing.Search.v7"
#  }
#}

variable "appserviceplan_settings" {
  type = object({
    sku_name               = string,
    os_type                = string
    zone_balancing_enabled = bool
  })
  description = "Settings for App Service Plan"
  default = {
    sku_name               = "P1v3"
    os_type                = "Linux"
    zone_balancing_enabled = false
  }
}

variable "webapp_settings" {
  type = object({
    application_insights_retention_in_days = number
  })
  description = "Settings for web apps"
  default = {
    application_insights_retention_in_days = 90
  }
}

variable "additional_storage_regions" {
  description = "Additional regions for data to be stored"
  type        = list(string)
  default     = []
}

variable "storageaccount_settings" {
  type = object({
    account_tier     = string,
    account_kind     = string
    replication_type = string
  })
  description = "Settings for Storage Account"
  default = {
    account_tier     = "Standard"
    account_kind     = "StorageV2"
    replication_type = "RAGRS"
  }
}

variable "outbound_dns_server" {
  type        = string
  description = "Primary outbound DNS server"
  default     = "************"
}

variable "outbound_alt_dns_server" {
  type        = string
  description = "Secondary outbound DNS server"
  default     = "************"
}

variable "nw_vnet_name" {
  type        = string
  description = "Vnet name."
  default     = ""
}

variable "nw_rg_name" {
  type        = string
  description = "Resource group name of network resources."
  default     = ""
}

variable "nw_subnet_privatelink_name" {
  type        = string
  description = "Privatelink subnet name."
  default     = ""
}

variable "nw_subnet_webapp_name" {
  type        = string
  description = "Webapp subnet name."
  default     = ""
}