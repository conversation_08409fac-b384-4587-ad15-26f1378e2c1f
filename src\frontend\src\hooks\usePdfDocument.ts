import { useState, useEffect } from "react";
import { downloadDocument } from "../services/documentsService";
import { useAuth } from "./useAuth";

export const usePdfDocument = (workspaceId: string, fileName: string) => {
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { getAuthResult, activeAccount } = useAuth();

  useEffect(() => {
    let isMounted = true; // Avoids state updates on umounted components.

    const fetchDocument = async () => {
      try {
        if (!activeAccount || !workspaceId || !fileName) return;

        const authResult = await getAuthResult();

        if (!authResult) {
          throw new Error("Token acquisition failed.");
        }

        const blob = await downloadDocument(workspaceId, fileName, authResult.accessToken);

        if (isMounted) {
          setPdfBlob(blob); // Updates the state if the component stills mounted.
        }
      } catch (err) {
        if (isMounted) {
          setError("Failed to download document");
        }
      }
    };

    fetchDocument();

    return () => {
      isMounted = false; // Marks as umounted when cleaning.
    };
  }, [workspaceId, fileName, activeAccount, getAuthResult]);

  return { pdfBlob, error };
};
