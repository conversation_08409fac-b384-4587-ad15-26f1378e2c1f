# backend-deploy-job.yml

parameters:
  - name: environment
  - name: service_connection
  - name: deploy_agent_pool
  - name: app_name
  - name: job_name_suffix
    default: ''
  - name: dependsOn
    default: ''

jobs:
  - job: 'DeployJob_${{ parameters.environment }}_${{ parameters.job_name_suffix }}'
    pool: ${{ parameters.deploy_agent_pool }}
    displayName: 'Deploy Backend to ${{ parameters.environment }}'
    ${{ if parameters.dependsOn }}:
      dependsOn: ${{ parameters.dependsOn }}
    steps:

    - task: DownloadBuildArtifacts@1
      displayName: 'Download Build Artifacts'
      inputs:
        artifactName: 'backend'
        downloadPath: '$(Agent.BuildDirectory)'
        buildType: 'current'
        downloadType: 'single'
        continueOnError: false

    - task: AzureWebApp@1
      displayName: 'Deploy Backend'
      inputs:
        azureSubscription: ${{ parameters.service_connection }}
        appType: 'webAppLinux'
        appName: ${{ parameters.app_name }}
        package: '$(Agent.BuildDirectory)\backend\backend.zip'