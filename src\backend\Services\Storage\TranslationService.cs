﻿using Azure.AI.Translation.Document;
using Azure.AI.Translation.Text;
using Azure.Storage.Blobs;
using Backend.Models;
using Backend.Models.Context;

namespace Backend.Services
{
    public class TranslationService : StorageService, ITranslationService
    {
        private readonly DocumentTranslationClient _client;
        private readonly TextTranslationClient _textTranslationClient;
        private readonly Dictionary<string, string> _endpoints;

        public TranslationService(Dictionary<string, BlobContainerClient> containerClients,
                                  int days,
                                  DocumentTranslationClient client,
                                  TextTranslationClient textTranslationClient,
                                  Dictionary<string, string> endpoints)
            : base(containerClients, days)
        {
            _client = client;
            _textTranslationClient = textTranslationClient;
            _endpoints = endpoints;
        }

        private string GetEndpoint()
        {
            return _endpoints.TryGetValue(
                CurrentContext.User.PDL!, out var endpoint) ? endpoint : _endpoints["Default"];
        }

        /// <summary>
        /// Translate a document
        /// </summary>
        /// <param name="folder"></param>
        /// <param name="language"></param>
        public async Task DocumentTranslate(string file, string language)
        {
            // Translation logic here (should take documents in the OID/Documents folder and translate them to the OID/<lanuage> folder)

            string userOID = CurrentContext.User.OID!;

            Uri sourceUri = new Uri($"{GetEndpoint().TrimEnd('/')}/{userOID}/Documents/{file}");

            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(file);
            string extension = Path.GetExtension(file);
            string newFileName = $"{fileNameWithoutExtension}_{language}{extension}";

            Uri targetUri = new Uri($"{GetEndpoint().TrimEnd('/')}/{userOID}/Translated/{newFileName}"); 

            var input = new DocumentTranslationInput(sourceUri, targetUri, language) { StorageUriKind = StorageInputUriKind.File };

            DocumentTranslationOperation operation = await _client.StartTranslationAsync(input);

            await operation.WaitForCompletionAsync();
        }

        public async Task<string> TextTranslate(string text, string language)
        {
            var response = await _textTranslationClient.TranslateAsync(language, text);
            return response.Value?.FirstOrDefault()?.Translations?[0]?.Text ?? string.Empty;            
        }
    }
}
