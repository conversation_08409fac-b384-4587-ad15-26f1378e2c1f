﻿using Backend.Models;
using Backend.Services;
using System.Reflection;
using System.Security.Cryptography;

namespace BackendUnitTests
{
    public class EncryptionTests
    {
        private readonly IEncryptionService _encryptionService;
        private string _hash = "mz4mkDbfx57bIhruqB53htGOAD1ot1d5";
        private string _email = "<EMAIL>";

        public EncryptionTests()
        {
            _encryptionService = new EncryptionService();
        }

        [Fact]
        public void EncryptValidString()
        {
            // arrange
            string toEncrypt = "HelloWorld";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);

            // assert
            Assert.True(cipher.Length > 0);
        }

        [Fact]
        public void NullStringToEncrypt()
        {
            // arrange
            string toEncrypt = null!;

            // act            

            // assert
            Assert.Throws<NullReferenceException>(() => _encryptionService.Encrypt(toEncrypt, _hash, _email));
        }

        [Fact]
        public void NullSecret()
        {
            // arrange
            string toEncrypt = "this is a test string";

            // act            

            // assert
            Assert.Throws<NullReferenceException>(() => _encryptionService.Encrypt(toEncrypt, null!, _email));
        }

        [Fact]
        public void NullEmail()
        {
            // arrange
            string toEncrypt = "this is a test string";

            // act            

            // assert
            Assert.Throws<NullReferenceException>(() => _encryptionService.Encrypt(toEncrypt, _hash, null!));
        }

        [Fact]
        public void EmptyStringToEncrypt()
        {
            // arrange
            string toEncrypt = string.Empty;

            // act            
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, "");

            // assert
            Assert.True(cipher.Length > 0); // this should be ok. should come back with a proper cipher
        }

        [Fact]
        public void EmptyStringCanDecrypt()
        {
            // arrange
            string toEncrypt = string.Empty;

            // act            
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);
            var decrypted = _encryptionService.Decrypt(cipher, _hash, _email);

            // assert
            Assert.Equal(string.Empty, decrypted);
        }

        [Fact]
        public void EncryptAndDecryptValidString()
        {
            // arrange
            string toEncrypt = "HelloWorldiwh389ey3eduh";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, "");
            var decrypted = _encryptionService.Decrypt(cipher, _hash, "");

            // assert
            Assert.Equal(toEncrypt, decrypted);
        }

        [Fact]
        public void EncryptSameValuesTwiceShouldProduceDiffCipher()
        {
            // arrange
            string toEncrypt = "Now in this example, the action that is the heart of the test stands alone within the Act section of the test.";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);
            var cipher2 = _encryptionService.Encrypt(toEncrypt, _hash, _email);

            // assert
            Assert.NotEqual(cipher, cipher2);
        }

        [Fact]
        public void DecryptWithInvalidEmail()
        {
            // arrange
            string toEncrypt = "Now in this example, the action that is the heart of the test stands alone within the Act section of the test.";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);

            // assert
            Assert.Throws<CryptographicException>(() => _encryptionService.Decrypt(cipher, _hash, "<EMAIL>"));
        }

        [Fact]
        public void DecryptWithInvalidSecret()
        {
            // arrange
            string toEncrypt = "Now in this example, the action that is the heart of the test stands alone within the Act section of the test.";
            string hash = "w6NGzIkGMQoT35jfzK16t0TItqCpKrpK";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);

            // assert
            Assert.Throws<CryptographicException>(() => _encryptionService.Decrypt(cipher, hash, _email));
        }

        [Fact]
        public void DecryptWithEmptySecret()
        {
            // arrange
            string toEncrypt = "Now in this example, the action that is the heart of the test stands alone within the Act section of the test.";
            string invalidSecret = string.Empty;

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);

            // assert
            Assert.Throws<CryptographicException>(() => _encryptionService.Decrypt(cipher, invalidSecret, _email));
        }

        [Fact]
        public void DecryptWithEmailWithDifferentCasing()
        {
            // arrange
            string toEncrypt = "Now in this example, the action that is the heart of the test stands alone within the Act section of the test.";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);
            var decrypted = _encryptionService.Decrypt(cipher, _hash, _email.ToUpper());

            // assert
            Assert.Equal(toEncrypt, decrypted);
        }

        [Fact]
        public void DecryptOnLongSecret()
        {
            // arrange
            string hash = "hfdeiufheuihf73y4r73hfeuhf34yr73rbejfb37847ry34rjsbfehjbgsakljoQWJIE92U9R8Y3FEJSBF3F4";
            string toEncrypt = "Now in this example, the action that is the heart of the test stands alone within the Act section of the test.";

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, hash, _email);
            var decrypted = _encryptionService.Decrypt(cipher, hash, _email);

            // assert
            Assert.Equal(toEncrypt, decrypted);
        }

        [Fact]
        public void EncryptAndDecryptLargeText()
        {
            // arrange
            var assembly = Assembly.GetExecutingAssembly();
            var filepath = "BackendUnitTests.testfiles.sampletext.txt";
            var toEncrypt = string.Empty;

            using (Stream s = assembly.GetManifestResourceStream(filepath)!)
            using (var reader = new StreamReader(s))
            {
                toEncrypt = reader.ReadToEnd();
            }

            // act
            var cipher = _encryptionService.Encrypt(toEncrypt, _hash, _email);
            var decrypted = _encryptionService.Decrypt(cipher, _hash, _email);

            // assert
            Assert.Equal(toEncrypt, decrypted);
        }
    }
}
