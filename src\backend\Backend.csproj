﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.DocumentIntelligence" Version="1.0.0" />
    <PackageReference Include="Azure.AI.Translation.Document" Version="2.0.0" />
    <PackageReference Include="Azure.AI.Translation.Text" Version="1.0.0" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    <PackageReference Include="Azure.Search.Documents" Version="11.6.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.11.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.23.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.3" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="3.8.3" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.47.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PDFsharp" Version="6.1.1" />
    <PackageReference Include="ReverseMarkdown" Version="4.6.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="System.Linq.Async" Version="6.0.1" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="BackendIntegrationTests" />
    <InternalsVisibleTo Include="UnitTests" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Tests\**" />
    <Compile Remove="Tests\**" />
    <EmbeddedResource Remove="Tests\**" />
    <None Remove="Tests\**" />
  </ItemGroup>

  <PropertyGroup>
    <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>

</Project>
