﻿namespace Backend.Models.Context
{
    /// <summary>
    /// Class used for tracking user information.
    /// </summary>
    public class User
    {
        /// <summary>
        /// Gets or sets the user's OID.
        /// </summary>
        public string? OID { get; set; }

        /// <summary>
        /// Gets or sets the user's display name.
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// Used to determine whether the user is using images.
        /// </summary>
        public bool Images { get; set; }

        /// <summary>
        /// Used to track the user's chat count.
        /// </summary>
        public int ChatCount { get; set; }

        /// <summary>
        /// Used to track the user's preferred data location (for environments with multiple data regions).
        /// </summary>
        public string? PDL { get; set; }
    }
}