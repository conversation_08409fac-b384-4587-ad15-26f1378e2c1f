import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ApiCallFlagState } from "../interfaces";

const initialState: ApiCallFlagState = {
  status: false,
};

const apiCallFlagSlice = createSlice({
  name: "status",
  initialState,
  reducers: {
    setApiCallFlag: (state, action: PayloadAction<boolean>) => {
      state.status = action.payload;
    },
  },
});

export const { setApiCallFlag } = apiCallFlagSlice.actions;

export default apiCallFlagSlice.reducer;
