const apiBaseUrl = import.meta.env.VITE_API_URL;
// Empty stream created to be used in case of error
// const createEmptyStream = (): ReadableStream<Uint8Array> => {
//     return new ReadableStream({
//         start(controller) {
//             controller.close();
//         }
//     });
// };

export const translateText = async (text: string, languageCode: string, token: string): Promise<string> => {
    const formData = new FormData();
    formData.append("text", text);
    formData.append("language", languageCode);

    const response = await fetch(`${apiBaseUrl}/translate/text`, {
        method: "POST",
        headers: {
            "Authorization": `Bearer ${token}`,
        },
        body: formData,
    });
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);

    }
    const data = await response.json();
    return data.translatedText;
};

// Upload files to translation endpoint
export const uploadDocuments = async (languageCode: string, files: File[], token: string): Promise<ReadableStream<Uint8Array>> => {
    const formData = new FormData();
    for (const file of files) {
        formData.append("documents", file);
    }
    formData.append("language", languageCode);

    try {
        const response = await fetch(`${apiBaseUrl}/translate/translate`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${token}`,
            },
            body: formData,
        });

        if (!response.ok) {
            // If the response is not OK, try to extract the error message
            const errorText = await response.text();
            throw new Error(errorText)
        }
        const reader = response.body?.getReader();
        const stream = new ReadableStream({
            start(controller) {
                function push() {
                    reader?.read().then(({ done, value }) => {
                        if (done) {
                            controller.close();
                            return;
                        }
                        controller.enqueue(value);
                        push();
                    }).catch((err) => {
                        controller.error(err);
                    });
                }
                push();
            },
            cancel() {
                reader?.cancel();
            },
        });
        return stream;

    } catch (error) {
        throw error;
    }
};

// Get the languages 
export const fetchLanguages = async (token: string): Promise<{ code: string; name: string }[]> => {
    const response = await fetch(`${apiBaseUrl}/translate/languages`, {
        method: "GET",
        headers: {
            "Authorization": `Bearer ${token}`,
            "Content-Type": "application/json",
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch languages: ${response.statusText}`);
    }
    const languages = await response.json();

    return languages
        .filter((lang: any) => lang.Enabled === "True")
        .map((lang: any) => ({
            code: lang.LanguageCode,
            name: lang.LanguageName,

        }));
};

// Get the documents from translation folder
export const fetchDocuments = async (token: string) => {
    const response = await fetch(`${apiBaseUrl}/translate/documents`, {
        method: "GET",
        headers: {
            "Authorization": `Bearer ${token}`,
            "Content-Type": "application/json",
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
    }
    const data = await response.json();

    return data.map((doc: any) => ({
        name: doc.documentName || doc.name,
        processed: doc.processed = 1, //set to processed because if it's being fetched from translation folder than it has already been processed
        expires: doc.expires,
    }));
};

// Download a document
export const downloadDocument = async (documentId: string, token: string) => {
    const response = await fetch(`${apiBaseUrl}/translate/download/${documentId}`, {
        method: "GET",
        headers: {
            "Authorization": `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
    }

    return response.blob();
};

// Delete a document
export const deleteDocument = async (documentId: string, token: string) => {
    const response = await fetch(`${apiBaseUrl}/translate/delete/${documentId}`, {
        method: "DELETE",
        headers: {
            "Authorization": `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to delete document: ${response.statusText}`);
    }
};