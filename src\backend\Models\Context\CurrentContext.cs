﻿namespace Backend.Models.Context
{
    /// <summary>
    /// Class which can be used for tracking data local to a runtime flow.
    /// </summary>
    public static class CurrentContext
    {
        private static readonly AsyncLocal<User> _user = new AsyncLocal<User>();

        public static User User
        {
            get => _user.Value!;
            set => _user.Value = value;
        }
    }
}