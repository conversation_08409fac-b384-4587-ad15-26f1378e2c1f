import { MAX_CHAT_WINDOW_LENGTH } from "../constants/constants";
import { EncryptedDataProps } from "../utils/types";
import { version as initialVersion, Stores } from "../constants/dbConstants";

// Dedicated variable for this database connection
let chatDB: IDBDatabase | null = null;

export const initDB = async (): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        // Close any existing connection
        if (chatDB) {
            chatDB.close();
            chatDB = null;
        }

        // First, open without version to get current version
        const versionRequest = indexedDB.open('gAI');

        versionRequest.onsuccess = () => {
            const currentVersion = versionRequest.result.version;
            versionRequest.result.close();

            // Now open with the correct version
            openDatabaseWithVersion(currentVersion, resolve, reject);
        };

        versionRequest.onerror = () => {
            // If we can't even open without version, try with default
            // console.log("Error getting current database version, using default");
            openDatabaseWithVersion(initialVersion, resolve, reject);
        };
    });
};

const openDatabaseWithVersion = (version: number, resolve: (value: boolean) => void, reject: (reason: any) => void) => {
    // console.log(`Opening gAI database with version ${version}`);
    const request = indexedDB.open('gAI', version);

    request.onupgradeneeded = () => {
        // console.log(`Database upgrade from v${event.oldVersion} to v${event.newVersion}`);
        const db = request.result;

        // Create stores as needed
        if (!db.objectStoreNames.contains(Stores.Users)) {
            // console.log('Creating Users store');
            db.createObjectStore(Stores.Users, { keyPath: 'id' });
        }
    };

    request.onsuccess = async () => {
        chatDB = request.result;

        // Check if stores exist
        const storeExists = Array.from(chatDB.objectStoreNames).includes(Stores.Users);
        // console.log('Store Users exists:', storeExists);

        if (!storeExists && version === request.result.version) {
            // Store doesn't exist and we're at the current version - need to upgrade
            chatDB.close();
            chatDB = null;

            // console.log(`Store Users missing, incrementing version from ${version} to ${version + 1}`);
            // Try to open with a higher version
            openDatabaseWithVersion(version + 1, resolve, reject);
            return;
        }

        resolve(true);
    };

    request.onerror = () => {
        console.error('Error opening database:', request.error);
        reject(false);
    };
};

export const addData = async <T>(storeName: string, data: EncryptedDataProps[]): Promise<T | string | null | EncryptedDataProps[]> => {
    if (!chatDB) {
        const initialized = await initDB();
        if (!initialized) {
            // If initialization failed, try once more
            await initDB();
        }
    }

    // Check if the store exists before trying to access it
    if (!chatDB || !Array.from(chatDB.objectStoreNames).includes(storeName)) {
        console.error(`Store ${storeName} not available for adding data`);
        return null;
    }

    // Get all data from the database
    const allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

    // If we have reached the chat limit, delete the oldest one
    if(allDbData && allDbData.length >= MAX_CHAT_WINDOW_LENGTH){
        // Sort by date (oldest first)
        const sortedData = [...allDbData].sort((a, b) =>
            new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        // Delete the oldest chat
        if (sortedData.length > 0) {
            await deleteData(Stores.Users, sortedData[0].id);
        }
    }

    return new Promise((resolve, reject) => {
        try {
            const tx = chatDB!.transaction(storeName, 'readwrite');
            const store = tx.objectStore(storeName);
            const addPromises = data.map(item => {
                return new Promise<void>((resolve, reject) => {
                    const addRequest = store.add(item);
                    addRequest.onsuccess = () => resolve();
                    addRequest.onerror = () => reject(addRequest.error?.message || 'Unknown error');
                });
            });

            Promise.all(addPromises)
                .then(() => resolve(data))
                .catch(error => reject(error));
        } catch (error) {
            console.error('Exception in addData:', error);
            reject(error);
        }
    });
};

export const getAllData = async <T>(storeName: string): Promise<T[]> => {
    if (!chatDB) {
        const initialized = await initDB();
        if (!initialized) {
            // If initialization failed, try once more
            await initDB();
        }
    }

    // Check if the store exists before trying to access it
    if (!chatDB || !Array.from(chatDB.objectStoreNames).includes(storeName)) {
        console.error(`Store ${storeName} not available for getting data`);
        return [];
    }

    return new Promise((resolve) => {
        try {
            const tx = chatDB!.transaction(storeName, 'readonly');
            const store = tx.objectStore(storeName);

            const getAllRequest = store.getAll();
            getAllRequest.onsuccess = () => {
                resolve(getAllRequest.result);
            };

            getAllRequest.onerror = () => {
                console.error('Error getting all data:', getAllRequest.error);
                resolve([]);
            };
        } catch (error) {
            console.error('Exception in getAllData:', error);
            resolve([]);
        }
    });
};

export const deleteData = async (storeName: string, id: string): Promise<void> => {
    if (!chatDB) {
        const initialized = await initDB();
        if (!initialized) {
            // If initialization failed, try once more
            await initDB();
        }
    }

    // Check if the store exists before trying to access it
    if (!chatDB || !Array.from(chatDB.objectStoreNames).includes(storeName)) {
        console.error(`Store ${storeName} not available for deleting data`);
        return;
    }

    return new Promise((resolve) => {
        try {
            const tx = chatDB!.transaction(storeName, 'readwrite');
            const store = tx.objectStore(storeName);
            const deleteRequest = store.delete(id);

            deleteRequest.onsuccess = () => {
                resolve();
            };

            deleteRequest.onerror = () => {
                console.error('Error deleting data:', deleteRequest.error);
                resolve(); // Resolve anyway to prevent blocking
            };
        } catch (error) {
            console.error('Exception in deleteData:', error);
            resolve(); // Resolve anyway to prevent blocking
        }
    });
};

export const updateHash = async (storeName: string, id: string, newHash: string): Promise<void> => {
    if (!chatDB) {
        await initDB();
    }

    return new Promise((resolve, reject) => {
        if (!chatDB) {
            reject('Database is not initialized');
            return;
        }
        const tx = chatDB.transaction(storeName, 'readwrite');
        const store = tx.objectStore(storeName);
        const getRequest = store.get(id);

        getRequest.onsuccess = () => {
            const data = getRequest.result;
            if (data) {
                data.date = new Date().toISOString(); // Update the date to current time
                data.hash = newHash;
                const updateRequest = store.put(data);
                updateRequest.onsuccess = () => resolve();
                updateRequest.onerror = () => reject(updateRequest.error?.message || 'Unknown error');
            } else {
                reject('Record not found');
            }
        };

        getRequest.onerror = () => {
            reject(getRequest.error?.message || 'Unknown error');
        };
    });
};