# API Documentation

All endpoints require authentication, and will return `401 Unauthorized` for unauthorized requests.<br><br> Ensure you include a valid token in the request headers (refer to the AUTH_README.md for help).

## Base URL
`http://localhost:5280`

`https://localhost:7006`

## Chat

Supported parameters:
<small>
* temperature <sup>(Optional)</sup>
* top_p <sup>(aka nucleusSamplingFactor) (Optional)</sup>
* maxTokens <sup>(Optional)</sup>
* frequencyPenalty <sup>(Optional)</sup>
* presencePenalty <sup>(Optional)</sup>
* messages ***(REQUIRED)***
</small>

**Example Request Body:** 
~~~json
{
	"temperature": 0.01,
	"top_p": 0.95,
	"maxTokens": 800
	"frequencyPenalty" 0: 
	"presencePenalty": 0,
	"messages": [{ "Role": "user", "Content": "What is the longest interstate in the US?" }]
}
~~~

#### Streaming

- **URL:** `/Chat`
- **Method:** `POST`
- **Description:** This endpoint streams chat completions from the OpenAI API.

#### REST

- **URL:** `/Chat/1`
- **Method:** `POST`
- **Description:** This endpoint returns a single chat completion from the OpenAI API.


## Workspaces

### Get Workspaces

- **URL:** `/workspaces`
- **Method:** `GET`
- **Description:** Retrieves all workspaces for the authenticated user.
- **Response:**
  - `200 OK` with a list of workspaces.

### Get Workspace by ID

- **URL:** `/workspaces/{id}`
- **Method:** `GET`
- **Description:** Retrieves workspace info by its ID.
- **Response:**
  - `200 OK` with the workspace details.
  
### Create Workspace

- **URL:** `/workspaces`
- **Method:** `POST`
- **Description:** Creates a new workspace.
- **Request Body:** 
~~~json
{
  "name": "MyWorkspace",
  "description": "..."
}
~~~
- **Response:**
  - `201 Created` with the created workspace.
  - `400 Bad Request` if the workspace data is invalid.


### Edit Workspace

- **URL:** `/workspaces/{id}`
- **Method:** `PATCH`
- **Description:** Updates an existing workspace.
- **Request Body:**
~~~json
{
    "id": "40172cca-c3e4-437e-bf8b-d43b2c30cdf6",   *Note: id must be provided either in the url or request body*
    "name": "...",
    "description": "..."
}
~~~
- **Response:**
  - `202 Accepted` with the updated workspace.
  - `400 Bad Request` if the workspace data is invalid.

### Delete Workspace

- **URL:** `/workspaces/{id}`
- **Method:** `DELETE`
- **Description:** Deletes a specific workspace by its ID.
- **Response:**
  - `204 No Content` if the workspace is deleted.

## Documents

### Get Documents

- **URL:** `/workspaces/{workspaceId}/documents`
- **Method:** `GET`
- **Description:** Retrieves the names of all documents within a specific workspace.
- **Response:**
  - `200 OK` with a list of documents.
  
### Upload Document(s)

- **URL:** `/workspaces/{workspaceId}/documents`
- **Method:** `POST`
- **Description:** Uploads one or more documents to a specific workspace.
- **Request Headers:** `Content-Type: multipart/form-data`
- **Request Body:** `documents: files[]`
- **Response:**
  - `200 OK` with the uploaded document details.
  - `400 Bad Request` if no documents are provided.

### Download Document

- **URL:** `/workspaces/{workspaceId}/documents/{name}`
- **Example:** `/workspaces/40172cca-c3e4-437e-bf8b-d43b2c30cdf6/documents/Image.png`
- **Method:** `GET`
- **Description:** Downloads a specific document from a workspace.
- **Response:**
  - `200 OK` with the document stream.

### Delete Document

- **URL:** `/workspaces/{workspaceId}/documents/{name}`
- **Method:** `DELETE`
- **Description:** Deletes a specific document from a workspace.
- **Response:**
  - `204 No Content` if the document is deleted.

<br/><br/>