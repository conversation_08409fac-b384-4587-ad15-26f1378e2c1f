# IAC template
parameters:
  - name: orgName
    type: string
    default: 'your-org-name'  # Replace with your default organization name

#Minimum severity which would cause scan to fail
  - name: severityThreshold
    type: string
    default: 'low'
    values:
      - 'low'
      - 'medium'
      - 'high'

# determines if build should fail based on issues found
  - name: allowBuildCompletion
    type: boolean
    default: true

#publishes the result to snyk 
  - name: runMonitor
    type: boolean
    default: true

steps:
#Running IAC scan (pushing results to Web UI)
- ${{ if eq(parameters.runMonitor, true) }}:
  - script: |
       snyk iac test --report --org=${{ parameters.orgName }} --severity-threshold=${{ parameters.severityThreshold }} --json-file-output=$(Agent.TempDirectory)/IAC-report.json
    displayName: 'IAC SCAN'
    condition: always()
    continueOnError: ${{ parameters.allowBuildCompletion }}

#Running IAC scan ( Doesn't push results to Web UI)

- ${{ if eq(parameters.runMonitor, false) }}:    
  - script: |
      snyk iac test --org=${{ parameters.orgName }} --severity-threshold=${{ parameters.severityThreshold }} --json-file-output=$(Agent.TempDirectory)/IAC-report.json
    displayName: 'IAC Scan'
    condition: always()
    continueOnError: ${{ parameters.allowBuildCompletion }}

#Generating the IAC reports(html)

- script: |
    snyk-to-html -i $(Agent.TempDirectory)/IAC-report.json -o $(Agent.TempDirectory)/IAC-report.html
  condition: always()
  displayName: 'IAC HTML Report generation'
  continueOnError: ${{ parameters.allowBuildCompletion }}

#Moving the IAC reports(html & json)

- script: |
    report_name='IAC'
    temp_directory=$(Agent.TempDirectory)
    if [ ! -d "$temp_directory/snyk-reports" ]; then
        mkdir -p "$temp_directory/snyk-reports"
    else
        echo "snyk-reports folder exist"
    fi
    json_file=$(ls "$temp_directory"/$report_name*.json 2>/dev/null)
    html_json=$(ls "$temp_directory"/$report_name*.html 2>/dev/null)
    if [ -n "$json_file" ]; then
        mv $temp_directory/$report_name*.json $temp_directory/snyk-reports/$report_name-report.json
    else
        echo "$report_name JSON-Report not generated"
    fi
    if [ -n "$html_json" ]; then
        mv $temp_directory/$report_name*.html $temp_directory/snyk-reports/$report_name-report.html
    else
        echo "$report_name HTML-Report not generated"
    fi
  displayName: 'Getting IAC Reports'
  condition: always()
  continueOnError: ${{ parameters.allowBuildCompletion }}
