﻿using Backend.Models;
using Backend.Validators;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Backend.Extensions;
using System.Runtime.CompilerServices;
using Backend.Models.Context;

namespace Backend.Controllers
{
    [Authorize]
    [EnableCors]
    [ApiController]
    [Route("/workspaces/{workspaceId}/documents")]
    public class DocumentsController : ControllerBase
    {
        private readonly IStorageService _blobService;
        private readonly IConversionService _conversionService;
        private static readonly HashSet<string> _validExtensions = new HashSet<string> { ".docx", ".doc", ".pptx", ".ppt" };

        public DocumentsController(IStorageService blobService,
                                   IConversionService conversionService)
        {
            _blobService = blobService;
            _conversionService = conversionService;
        }

        // Get Documents endpoint
        [HttpGet]
        public async IAsyncEnumerable<DocumentResult> OnGetDocumentsAsync(
            string workspaceId,
            [EnumeratorCancellation] CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            Response.ContentType = "application/x-ndjson";

            await foreach (var result in ControllerExtensions.HandleDocumentProcessingAsync<List<Document>>(
                () => _blobService.GetDocuments(CurrentContext.User.OID + "/" + workspaceId, ct),
                () => _blobService.GetDocuments(CurrentContext.User.OID + "/" + workspaceId, ct),
                ct))
            {
                yield return result;
                await Task.Delay(1);
            }
        }

        // Download endpoint
        [HttpGet("{document}")]
        public async Task<IActionResult> OnDownloadDocumentAsync(
            string workspaceId,
            string? document,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            if (!ct.IsCancellationRequested)
            {
                var stream = await _blobService.GetDocumentStreamAsync(CurrentContext.User.OID + "/" + workspaceId + "/" + document, ct);
                return new FileStreamResult(stream, "application/octet-stream")
                {
                    FileDownloadName = document
                };
            }
            return new EmptyResult();
        }

        // Upload Document(s) endpoint
        [HttpPost]
        public async IAsyncEnumerable<DocumentResult> OnPostDocumentsAsync(
            string workspaceId,
            IFormFile document,
            [EnumeratorCancellation] CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);
            var prefix = CurrentContext.User.OID + "/" + workspaceId + "/" + document.FileName;

            if (document == null) { throw new BadHttpRequestException("No document(s) were provided in the request."); }

            Response.ContentType = "application/x-ndjson";

            await ProcessDocumentAsync(CurrentContext.User.OID!, workspaceId, document, ct);

            await foreach (var result in ControllerExtensions.HandleDocumentProcessingAsync<List<Document>>(
                () => _blobService.GetDocuments(prefix, ct),
                () => _blobService.GetDocuments(prefix, ct),
                ct))
            {
                yield return result;
                await Task.Delay(1, ct);
            }
        }

        // Delete Document endpoint
        [HttpDelete("{document}")]
        public async Task<IActionResult> OnDeleteDocumentAsync(
            string workspaceId,
            string document,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            if (!ct.IsCancellationRequested)
            {
                await _blobService.DeleteBlobAsync(CurrentContext.User.OID + "/" + workspaceId + "/" + document);
                return NoContent();
            }
            return new EmptyResult();
        }

        private async Task ProcessDocumentAsync(
            string oid,
            string workspaceId,
            IFormFile document,
            CancellationToken ct)
        {
            await using (var docStream = document.OpenReadStream())
            {
                var extension = Path.GetExtension(document.FileName).ToLower();

                if (_validExtensions.Contains(extension))
                {
                    await ProcessDocumentAsync(oid, workspaceId, document, docStream, ct);
                }
                else if (extension == ".pdf")
                {
                    await ProcessPdfDocumentAsync(oid, workspaceId, document, docStream, ct);
                }
                else
                {
                    throw new BadHttpRequestException("Unsupported file type.");
                }
            }
        }

        private async Task ProcessDocumentAsync(string oid,
            string workspaceId,
            IFormFile document,
            Stream docStream,
            CancellationToken ct)
        {
            using (var pdfStream = await _conversionService.ConvertToPDF(document.FileName, docStream))
            {
                // Validate PDF
                PdfValidator.Validate(pdfStream, document.Length, document.FileName);

                // Upload original .docx file
                await _blobService.UploadDocumentsAsync(oid, workspaceId, document.FileName, docStream, ct);

                // Upload converted .pdf file
                var pdfFileName = Path.ChangeExtension(document.FileName, ".pdf");
                await _blobService.UploadDocumentsAsync(oid, workspaceId, pdfFileName, pdfStream, ct, Path.GetExtension(document.FileName).ToLower());
            }
        }

        private async Task ProcessPdfDocumentAsync(
            string oid,
            string workspaceId,
            IFormFile document,
            Stream docStream,
            CancellationToken ct)
        {
            // Validate PDF
            PdfValidator.Validate(docStream, document.Length, document.FileName);

            // Upload PDF file
            await _blobService.UploadDocumentsAsync(oid, workspaceId, document.FileName, docStream, ct);
        }
    }
}
