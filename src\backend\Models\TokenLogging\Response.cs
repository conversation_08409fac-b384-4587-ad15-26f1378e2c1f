﻿using System.Text.Json.Serialization;

namespace Backend.Models.TokenLogging
{
    /// <summary>
    /// Formatted OpenAI response details.
    /// </summary>
    public class Response
    {
        [JsonPropertyName("object")]
        public string? Object { get; set; }

        [JsonPropertyName("data")]
        public List<Data>? Data { get; set; }

        [JsonPropertyName("model")]
        public string? Model { get; set; }

        [JsonPropertyName("usage")]
        public Usage? Usage { get; set; }
    }
}
