import { useState } from "react";

const useSettingsTemplate = () => {
    const [isDeleteTemplateModalOpen, setIsDeleteTemplateModalOpen] = useState(false);

    const openDeleteCustomTemplateModal = () => {
        setIsDeleteTemplateModalOpen(true);
      };
    
    const closeDeleteCustomTemplateModal = () => {
      setIsDeleteTemplateModalOpen(false);
    };

    return { isDeleteTemplateModalOpen, openDeleteCustomTemplateModal, closeDeleteCustomTemplateModal };
}

export default useSettingsTemplate;