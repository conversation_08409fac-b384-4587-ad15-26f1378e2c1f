import { useEffect, useState, useCallback } from "react";
import { useMsal } from "@azure/msal-react";
import { AuthenticationResult } from "@azure/msal-browser";
import { loginRequest } from "../authConfig";
import { setName } from "../features/userSlice";
import { setEmail } from "../features/emailSlice";
import { setToken } from "../features/tokenSlice";
import { setRegion } from "../features/regionSlice";
import { RegionKey } from "../interfaces";
import { useAppDispatch } from "../app/hooks";

const VITE_MSAL_REGION = import.meta.env.VITE_MSAL_REGION as RegionKey | undefined;

export const useAuth = () => {
  const { instance, accounts } = useMsal();
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  const [authResult, setAuthResult] = useState<AuthenticationResult>();
  const activeAccount = accounts[0];

  const acquireToken = useCallback(async () => {
    try {
      const tokenResponse = await instance.acquireTokenSilent({
        ...loginRequest,
        account: activeAccount,
        // extraQueryParameters: {
        //   token_lifetime: "30", // Sets the token expiration time to 30 seconds
        // },
      });

      if (!tokenResponse) {
        // if `acquireTokenSilent` fails, redirects to authenticate
        instance.acquireTokenRedirect(loginRequest);
        return null;
      }

      // Decode the token to extract the xms_pdl field and change region to CN if necessary
      let region = VITE_MSAL_REGION;
      if (region === "US" || region === "LOCALE") {
        const xms_pdl = JSON.parse(atob(tokenResponse.accessToken.split('.')[1]))["xms_pdl"];
        if (xms_pdl === "CAN") {
          region = "CN";
        }
      }

      // Dispatch actions to set the user's name, email, token, and region
      dispatch(setName(tokenResponse.account?.name || ""));
      dispatch(setEmail(tokenResponse.account?.username || ""));
      dispatch(setToken(tokenResponse.accessToken));
      dispatch(setRegion(region || ""));
      setAuthResult(tokenResponse);
      return tokenResponse;
    } catch (error) {
      console.error("Error acquiring token:", error);
      instance.acquireTokenRedirect(loginRequest);
      return null;
    }
  }, [instance, activeAccount, dispatch]);

  useEffect(() => {
    if (activeAccount) {
      acquireToken();
    }
  }, [activeAccount, acquireToken]);

  const getAuthResult = useCallback(async () => {
    if (!authResult) {
      const newAuthResult = await acquireToken();
      return newAuthResult;
    }
    return authResult;
  }, [authResult, acquireToken]);

  const getToken = useCallback(async () => {
    const result = await getAuthResult();
    return result?.accessToken;
  }, [getAuthResult]);

  return { instance, activeAccount, getAuthResult, authResult, getToken, acquireToken };
};