environment                 = "dev"
region                      = "au"
azurerm_resource_group_name = "corp-gallagherai-au-main-rg"

rbac_devadmin = ["d5e1daf2-a8bd-41db-931e-083ff2c9f977",     #u-ADO_AJG-CORP_GallagherGPT_Admins
  "9802beeb-ab97-4c8c-9085-956947408c87",                    #u-ADO_AJG-CORP_GallagherGPT_Contributors  
"eaf0c0be-7bec-4616-a4c1-f9ec069d15a1"]                      #<PERSON><PERSON>'s AVD's service principal (ameazus1ss-74e0)
rbac_contributors = ["ab69d54d-0ae3-40c5-9b6d-8d65da9a8d59"] #corp-openai-dev-gallaghergpt-cicd (Az Devops CICD Service Principal)

appserviceplan_settings = {
  sku_name               = "P0v3"
  os_type                = "Linux"
  zone_balancing_enabled = false
}

webapp_settings = {
  application_insights_retention_in_days = 30
}

docintelligence_settings = {
  sku_name                   = "F0"
  kind                       = "FormRecognizer"
  dynamic_throttling_enabled = false
}

search_settings = {
  sku_name      = "basic"
  semantic_sku  = "free" # specified only when "sku_name" is not set to "free"
  replica_count = 1
}

websearch_settings = {
  sku_name = "F0"
  kind     = "Bing.Search.v7"
}

# Networking
nw_vnet_name               = "corpau-dev-pvnet"
nw_rg_name                 = "network-rg"
nw_subnet_privatelink_name = "pe"                      # *************/26
nw_subnet_webapp_name      = "gallagherai-integration" # *************/28