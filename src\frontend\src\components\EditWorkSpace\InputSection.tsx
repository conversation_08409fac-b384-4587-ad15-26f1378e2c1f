import React from "react";
import { MANA<PERSON>_WORKSPACE_DESCRIPTION_LIMIT, MANAGE_WORKSPACE_NAME_LIMIT } from "../../constants/constants";
import { InputSectionProps } from "../../interfaces";
import Tooltip from "../Tooltip/Tooltip";

const InputSection = React.memo(({ workspaceIdFromUrl, name, description, inputChange }: InputSectionProps) => {
  return (
    <>
      <h2 className="text-xl font-bold mb-4 dark:text-gray-300">
        {workspaceIdFromUrl ? "Edit Workspace" : "Create Workspace"}
      </h2>
      <Tooltip message={`Maximum ${MANAGE_WORKSPACE_NAME_LIMIT} characters allowed`}>
        <input
          type="text"
          name="name"
          placeholder="Workspace name"
          value={name}
          maxLength={MANAGE_WORKSPACE_NAME_LIMIT}
          onChange={inputChange}
          className="w-full p-2 border-2 rounded-sm mb-4 focus:outline-hidden focus:border-blue-600 focus:bg-gray-50 hover:border-gray-300 dark:bg-zinc-600 dark:focus:bg-zinc-700 dark:text-white dark:placeholder-gray-400 dark:border-zinc-600 dark:focus:border-blue-500"
          aria-label="Workspace Name"
        />
      </Tooltip>
      <Tooltip message={`Maximum ${MANAGE_WORKSPACE_DESCRIPTION_LIMIT} characters allowed`}>
        <textarea
          name="description"
          placeholder="Workspace description"
          value={description}
          maxLength={MANAGE_WORKSPACE_DESCRIPTION_LIMIT}
          onChange={inputChange}
          className="w-full p-2 border-2 rounded-sm mb-4 focus:outline-hidden focus:border-blue-600 focus:bg-gray-50 hover:border-gray-300 dark:bg-zinc-600 dark:focus:bg-zinc-700 dark:text-white dark:placeholder-gray-400 dark:border-zinc-600 dark:focus:border-blue-500"
          aria-label="Workspace Description"
        ></textarea>
      </Tooltip>
    </>
  );
});

export default InputSection;