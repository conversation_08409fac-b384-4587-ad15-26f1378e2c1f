﻿using Azure.Search.Documents;

namespace Backend.Models
{
    /// <summary>
    /// Interface for Search Service
    /// </summary>
    public interface ISearchService
    {
        /// <summary>
        /// Queries the search index for information.
        /// </summary>
        Task<SectionItem[]> QueryDocumentsAsync(
            string workspace,
            SearchSettings settings,
            string? textQuery = null,
            float[]? vector = null,
            CancellationToken ct = default);

        /// <summary>
        /// Gets the search client.
        /// </summary>
        SearchClient GetSearchClient(string val);

        /// <summary>
        /// Adds sections to the search index.
        /// </summary>
        Task AddToSearchAsync(IEnumerable<Section> sections, string PDL = "Default");
    }
}
