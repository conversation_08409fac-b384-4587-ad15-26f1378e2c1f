﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Generic object for the handling of document processing.
    /// </summary>
    public record DocumentResult
    {
        /// <summary>
        /// Gets or sets the workspace info.
        /// </summary>
        [JsonPropertyName("workspace")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)] 
        public Workspace? Workspace { get; set; }

        /// <summary>
        /// Gets or sets the document info.
        /// </summary>
        [JsonPropertyName("documents")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)] 
        public List<Document>? Document { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="workspace"></param>
        public DocumentResult(Workspace? workspace)
        {
            Workspace = workspace;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="document"></param>
        public DocumentResult(List<Document>? document)
        {
            Document = document;
        }
    }
}
