import { useState } from "react";
import { Workspace } from "../interfaces";
import useApi<PERSON>ithAuth from "./useApiWithAuth";
import { useGetWorkspacesQuery } from "../services/api/workspacesApi";
import { WorkspaceListResponse } from "../types/apiTypes";

const useFetchWorkspaces = () => {
  // Ensure we have authentication before making API calls
  const { isAuthenticated } = useApiWithAuth();

  // Use RTK Query hook to fetch workspaces
  const {
    data: workspaces = [] as WorkspaceListResponse,
    isLoading: loading,
    error: queryError,
    refetch
  } = useGetWorkspacesQuery(undefined, {
    // Skip the query if not authenticated
    skip: !isAuthenticated,
    // Refetch on component mount
    refetchOnMountOrArgChange: true
  });

  // Convert RTK Query error to string for compatibility with existing code
  const error = queryError ?
    (typeof queryError === 'string' ? queryError : 'Failed to load workspaces.') :
    null;

  // For compatibility with existing code that expects setWorkspaces
  const [manualWorkspaces, setManualWorkspaces] = useState<Workspace[]>([]);

  // Combine RTK Query data with manual state for backward compatibility
  const combinedWorkspaces = workspaces.length > 0 ? workspaces : manualWorkspaces;

  return {
    workspaces: combinedWorkspaces,
    loading,
    error,
    setWorkspaces: setManualWorkspaces,
    refetch
  };
};

export default useFetchWorkspaces;