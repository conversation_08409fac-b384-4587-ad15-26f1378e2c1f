pool:
    vmImage : ubuntu-latest

variables:
- group: SNYK_TOKEN

steps:
  - template: ./Snyk-CLI-Setup.yml
  - template: ./Snyk-IAC-Templates.yml 
    parameters:
      orgName: 'corp-info-serv-and-appdev-ajg-corp'
      severityThreshold: 'medium'
      runMonitor: true
  - task: PublishPipelineArtifact@1
    displayName: 'Publish Snyk Reports'
    inputs:
      targetPath: '$(Agent.TempDirectory)/snyk-reports'
      artifact: 'snyk-report'
    condition: always()
    continueOnError: true          