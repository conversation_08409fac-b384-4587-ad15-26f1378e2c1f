﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Encryption Service
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// 
        /// </summary>
        string Encrypt(string plainText, string key, string email);

        /// <summary>
        /// 
        /// </summary>
        string Decrypt(string cipherText, string key, string email);
    }
}
