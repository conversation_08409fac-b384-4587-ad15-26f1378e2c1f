﻿using Microsoft.AspNetCore.Mvc;
using Backend.Models;
using Microsoft.AspNetCore.Cors;
using Backend.Extensions;
using Microsoft.AspNetCore.Authorization;
using System.Text;
using Backend.Models.Context;
using System.Text.Json;

namespace Backend.Controllers
{
    [Authorize]
    [ApiController]
    [Route("/Chat")]
    public class ChatController : ControllerBase
    {
        private readonly IOpenAIService _OpenAIService;
        private readonly IStorageService _blobService;
        private readonly IConfiguration _config;

        public ChatController(IOpenAIService openAIService, IStorageService blobStorageService, IConfiguration config)
        {
            _OpenAIService = openAIService;
            _blobService = blobStorageService;
            _config = config;
        }

        /// <summary>
        /// Streaming endpoint for chat
        /// </summary>
        [HttpPost]
        [EnableCors]
        public async Task<IActionResult> OnChatStream(
            [FromBody] Chat request,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext,
                (request.Messages.Count + 1) / 2,
                request.Messages.Any(message => message.Content?.Any(content => content.Type is cType.Image) == true));

            request.Settings = _config.GetSettings(request.Settings, !string.IsNullOrEmpty(request.Workspace));

            Response.Headers.Append("Content-Type", "application/x-ndjson");
            await using var writer = new StreamWriter(HttpContext.Response.Body, Encoding.UTF8, leaveOpen: true);

            try
            {
                if (!ct.IsCancellationRequested && string.IsNullOrEmpty(request.Workspace))
                {
                    await foreach (var result in _OpenAIService.PromptAI(request, ct))
                    {
                        await writer.WriteAsync(JsonSerializer.Serialize(result));
                        await writer.FlushAsync();
                    }
                }
                else
                {
                    await foreach (var result in _OpenAIService.PromptAIAboutDocumentation(request, ct))
                    {
                        await writer.WriteAsync(JsonSerializer.Serialize(result));
                        await writer.FlushAsync();
                    }
                    _ = _blobService.Refresh(CurrentContext.User.OID! + "/" + request.Workspace!);
                }
            }
            catch (OperationCanceledException)
            {
            }
            return new EmptyResult();
        }
    }
}