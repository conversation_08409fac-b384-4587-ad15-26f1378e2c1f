import { CHAT_MSSG_ROLE_ASSISTANT } from "../constants/constants";
import { getAllData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { addMessage, clearMessages } from "../features/chatSlice";
import { setCompleteMessage } from "../features/completeMessageSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { setCurrentWorkspaceId } from "../features/currentWorkspaceIdSlice";
import { CompleteMessageState } from "../interfaces";
import { AppDispatch } from "../store";
import { chatDecryption } from "./chatDecryption";
import { apiResponseMessagesTransform } from "./chatMessagesTransform";
import { ChatMessageProps, EncryptedDataProps } from "./types";

export const getChatHistory = async(chatId: string,email:string, token: string, dispatch: AppDispatch)=>{
    dispatch(setApiCallFlag(false));

    const allDbData = await getAllData(Stores.Users);
    if(allDbData && allDbData.length>0){
        const hash = getParticularChat(allDbData as EncryptedDataProps[], chatId);
        const workspaceId = getParticularWorkspaceId(allDbData as EncryptedDataProps[], chatId);
        dispatch(setCurrentWorkspaceId(workspaceId));
        const dbParsedMessage = await chatDecryption(hash, email, token);
        const messages = apiResponseMessagesTransform(dbParsedMessage);
        dispatch(clearMessages());
        messages.forEach(message => {
            dispatch(addMessage(message));
        });
        
        const completeMessage = getCompleteMessage(messages);
        dispatch(setCompleteMessage(completeMessage));
        dispatch(setCurrentChatId(chatId));
    }
}

const getParticularChat = (allDbData: EncryptedDataProps[], chatId: string): string =>{
    for(const elem of allDbData){
        if(elem.id === chatId){
            return elem.hash
        }
    }
    return ""
}

const getCompleteMessage = (message: ChatMessageProps[])=>{
    const outputObject : CompleteMessageState = {};

    message.forEach((item, index) => {
        if (item.user === CHAT_MSSG_ROLE_ASSISTANT) {
            outputObject[index] = item.isStreamComplete;
        }
    });

    return outputObject;
}

const getParticularWorkspaceId =(allDbData: EncryptedDataProps[], chatId: string): string =>{
    const id ="";
    for(const elem of allDbData){
        if(elem.id === chatId){
            return elem.workspaceId
        }
    }
    return id;
}