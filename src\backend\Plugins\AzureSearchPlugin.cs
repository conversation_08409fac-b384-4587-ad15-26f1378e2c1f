//#pragma warning disable SKEXP0010
//#pragma warning disable SKEXP0001

//using Azure.Core;
//using Backend.Models;
//using Backend.Models.Context;
//using Microsoft.SemanticKernel;
//using System.ComponentModel;

//namespace Backend.Plugins
//{
//    public sealed class AzureSearchPlugin
//    {
//        private static ISearchService? _searchService;

//        public AzureSearchPlugin(TokenCredential credential, ISearchService searchService)
//        {
//            _searchService = searchService;
//        }

//        [KernelFunction("Query"), Description("Returns information from the index.")]
//        public async Task<string> SemanticSearch(
//            [Description("The search query to execute.")] string query)
//        {
//            var filter = _searchService.CreateFilter(CurrentContext.User.OID!, workspace);

//        }


//            public async Task<SectionItem[]> QueryDocumentsAsync(
//            string workspace,
//            SearchSettings settings,
//            string? textQuery = null,
//            float[]? vector = null,
//            CancellationToken ct = default)
