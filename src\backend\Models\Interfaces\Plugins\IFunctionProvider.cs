﻿using Microsoft.SemanticKernel;

namespace Backend.Models
{
    /// <summary>
    /// Helper function provider to get best functions for specific request.
    /// </summary>
    public interface IFunctionProvider
    {
        Task<List<KernelFunction>> GetBestFunctionsAsync(
            string collectionName,
            string request,
            KernelPluginCollection plugins,
            int numberOfBestFunctions,
            CancellationToken cancellationToken = default);
    }
}
