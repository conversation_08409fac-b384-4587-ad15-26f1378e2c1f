environment                 = "prod"
region                      = "us"
azurerm_resource_group_name = "corp-gallagherai-us-main-rg"

rbac_devadmin = ["07691ecb-a8e5-4036-b1ae-af3d13b48463",     #u-Corp-Architects
"e4c13c82-2c7f-481d-ad69-daa108781f8f"]                      #u-CorpOD_CORP_SmartMarket_Developers (<PERSON>, <PERSON>)]
rbac_contributors = ["5f233a8b-c417-42c7-a181-7ad7aca5c9fe"] #corp-openai-prod-gallaghergpt-cicd (Az Devops CICD Service Principal)

additional_storage_regions = ["Canada Central"]

appserviceplan_settings = {
  sku_name               = "P1mv3"
  os_type                = "Linux"
  zone_balancing_enabled = false
}

webapp_settings = {
  application_insights_retention_in_days = 180
}

search_settings = {
  sku_name      = "standard"
  semantic_sku  = "standard"
  replica_count = 3
}

docintelligence_settings = {
  sku_name                   = "S0"
  kind                       = "FormRecognizer"
  dynamic_throttling_enabled = true
}

nw_vnet_name               = "corp-prod-pvnet"
nw_rg_name                 = "network-rg"
nw_subnet_privatelink_name = "pe1"                     # **************/26
nw_subnet_webapp_name      = "gallagherai-integration" # **************/28