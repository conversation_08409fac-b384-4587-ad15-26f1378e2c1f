import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatMessageProps , AllChatState} from '../interfaces';


const initialState: AllChatState = {
  allChat: {},
};

const allChatSlice = createSlice({
  name: 'allChat',
  initialState,
  reducers: {
    addChatArray: (state, action: PayloadAction<{ key: string; chatArray: ChatMessageProps[] }>) => {
      const { key, chatArray } = action.payload;
      state.allChat[key] = chatArray;
    },
    deleteChatArray: (state, action: PayloadAction<{ key: string }>) => {
      const { key } = action.payload;
      delete state.allChat[key];
    },
    updateChatArray: (state, action: PayloadAction<{ key: string; chatArray: ChatMessageProps[] }>) => {
      const { key, chatArray } = action.payload;
      if (state.allChat[key]) {
        state.allChat[key] = chatArray;
      }
    },
  },
});

export const { addChatArray, deleteChatArray, updateChatArray } = allChatSlice.actions;

export default allChatSlice.reducer;