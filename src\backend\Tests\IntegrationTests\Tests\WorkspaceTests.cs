//using Backend.Models;
//using Backend.Models.Context;
//using System.Text;
//using System.Text.Json;
//using System.Text.Json.Nodes;
//using Xunit;

//namespace BackendIntegrationTests
//{
//    [CollectionDefinition("Workspace Tests")]
//    public class WorkspaceTestsCollection : ICollectionFixture<BackendIntegrationTests> { }

//    [Collection("Workspace Tests")]
//    public class WorkspacesControllerTests
//    {
//        private readonly BackendIntegrationTests _fixture;
//        private Workspace _workspace1;
//        private Workspace _workspace2;
//        private Workspace _workspace3;

//        public WorkspacesControllerTests(BackendIntegrationTests fixture)
//        {
//            _fixture = fixture;

//            // Preliminary data
//            _workspace1 = new Workspace 
//            { 
//                Name = "workspace1", 
//                Description = "description1", 
//                Expires = DateTime.UtcNow.AddDays(30).ToString("MM/dd/yyyy") 
//            };

//            _workspace2 = new Workspace 
//            { 
//                Name = "workspace2", 
//                Description = "description2", 
//                Expires = DateTime.UtcNow.AddDays(30).ToString("MM/dd/yyyy") 
//            };

//            _workspace3 = new Workspace 
//            { 
//                Name = "workspace3", 
//                Description = "description3", 
//                Expires = DateTime.UtcNow.AddDays(30).ToString("MM/dd/yyyy") 
//            };

//            CurrentContext.User = new User
//            {
//                OID = "userTest",
//                DisplayName = "userTest",
//                Images = false,
//                ChatCount = 0,
//                PDL = "NAM"
//            };

//            // Add data to the in-memory storage service
//            _workspace1 = _fixture.StorageService.CreateWorkspaceAsync("userTest", _workspace1).Result;
//            _workspace2 = _fixture.StorageService.CreateWorkspaceAsync("userTest", _workspace2).Result;
//            _workspace3 = _fixture.StorageService.CreateWorkspaceAsync("userTest", _workspace3).Result;
//        }

//        [Fact]
//        public async Task GET_Workspaces_ReturnsOkResult()
//        {
//            // Act
//            var response = await _fixture.Client.GetAsync("/workspaces");

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();
//            Assert.Contains("[", responseString);
//            Assert.Contains("]", responseString);
//        }

//        [Fact]
//        public async Task GET_Workspace_ReturnsOkResult()
//        {
//            // Act
//            var response = await _fixture.Client.GetAsync($"/workspaces/{_workspace1.Id}");

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();

//            using (var document = JsonDocument.Parse(responseString))
//            {
//                var root = document.RootElement;
//                var workspaceElement = root[0].GetProperty("workspace");
//                var returnedWorkspace = JsonSerializer.Deserialize<Workspace>(workspaceElement.GetRawText());

//                Assert.NotNull(returnedWorkspace);
//                Assert.Equal(_workspace1.Id, returnedWorkspace.Id);
//                Assert.Equal(_workspace1.Name, returnedWorkspace.Name);
//                Assert.Equal(_workspace1.Description, returnedWorkspace.Description);
//                Assert.Equal(_workspace1.Expires, returnedWorkspace.Expires);
//            }
//        }

//        [Fact]
//        public async Task CREATE_Workspace_ReturnsOkResult()
//        {
//            // Arrange
//            var jsonObject = new JsonObject
//            {
//                ["name"] = "CreateWorkspaceTest",
//                ["description"] = "CreateWorkspaceTest Description"
//            };

//            var content = new StringContent(jsonObject.ToString(), Encoding.UTF8, "application/json");

//            // Act
//            var response = await _fixture.Client.PostAsync("/workspaces", content);

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();
//            var returnedWorkspace = JsonSerializer.Deserialize<Workspace>(responseString);

//            Assert.NotNull(returnedWorkspace);
//            Assert.NotNull(returnedWorkspace.Id);
//            Assert.Equal("CreateWorkspaceTest", returnedWorkspace.Name);
//            Assert.Equal("CreateWorkspaceTest Description", returnedWorkspace.Description);
//        }

//        [Fact]
//        public async Task EDIT_Workspace_ReturnsOkResult()
//        {
//            // Arrange
//            var jsonObject = new JsonObject
//            {
//                ["id"] = $"{_workspace2.Id}",
//                ["name"] = "Changed Named",
//                ["description"] = "Changed Description"
//            };

//            var content = new StringContent(jsonObject.ToString(), Encoding.UTF8, "application/json");

//            // Act
//            var response = await _fixture.Client.PatchAsync($"/workspaces/", content);

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var responseString = await response.Content.ReadAsStringAsync();
//            var returnedWorkspace = JsonSerializer.Deserialize<Workspace>(responseString);

//            Assert.NotNull(returnedWorkspace);
//            Assert.Equal(_workspace2.Id, returnedWorkspace.Id);
//            Assert.Equal("Changed Named", returnedWorkspace.Name);
//            Assert.Equal("Changed Description", returnedWorkspace.Description);
//        }

//        [Fact]
//        public async Task DELETE_Workspace_ReturnsOkResult()
//        {
//            // Act
//            var response = await _fixture.Client.DeleteAsync($"/workspaces/{_workspace3.Id}");

//            // Assert
//            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
//            var workspaces = await _fixture.StorageService
//                .GetWorkspaces("userTest", CancellationToken.None);
//            Assert.DoesNotContain(workspaces, w => w.Id == _workspace3.Id);
//        }
//    }
//}
