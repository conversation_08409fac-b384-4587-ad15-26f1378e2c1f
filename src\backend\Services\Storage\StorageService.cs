﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Backend.Models;
using Backend.Extensions;
using Backend.Models.Context;

namespace Backend.Services
{
    public abstract class StorageService : IStorageService
    {
        protected readonly Dictionary<string, BlobContainerClient> _containerClients;
        protected readonly int _days;

        public StorageService(Dictionary<string, BlobContainerClient> containerClients,
                              int days)
        {
            _containerClients = containerClients;
            _days = days;
        }

        protected BlobContainerClient GetContainerClient()
        {
            return _containerClients.TryGetValue(
                CurrentContext.User.PDL!, out var client) ? client : _containerClients["Default"];
        }

        /// <summary>
        /// Gets all workspaces for the user from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        public async Task<List<Workspace>> GetWorkspaces(string prefix, CancellationToken ct)
        {
            var items = new List<Workspace>();

            await foreach (BlobItem item in
                GetContainerClient()
                    .GetBlobsAsync(BlobTraits.Metadata, prefix: prefix, cancellationToken: ct))
            {
                if (item.Name.CountChar('/') == 1 &&
                    item.Metadata.ContainsKey("id") &&
                    item.Metadata.ContainsKey("hdi_isfolder") &&
                    item.Metadata["hdi_isfolder"] == "true")
                {
                    items.Add(new Workspace
                    {
                        Id = item.Metadata["id"],
                        Name = item.Metadata["name"],
                        Description = item.Metadata.ContainsKey("description") ?
                                      item.Metadata["description"] : ""
                    });
                }
            }

            return items;
        }

        /// <summary>
        /// Gets all documents in a workspace from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        public async Task<List<Document>> GetDocuments(string prefix, CancellationToken ct, bool fetchAll = false)
        {
            Document result = new Document();
            var items = new List<Document>();

            await foreach (BlobItem item in
                GetContainerClient()
                    .GetBlobsAsync(BlobTraits.Metadata, prefix: prefix, cancellationToken: ct))
            {
                var document = ProcessBlobItem(item, fetchAll);
                if (document != null)
                {
                    items.Add(document);
                }
            }

            return items;
        }

        /// <summary>
        /// Gets information about a workspace from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        public async Task<Workspace> GetWorkspace(string prefix, CancellationToken ct)
        {
            try
            {
                BlobClient blobClient = GetContainerClient().GetBlobClient(prefix);
                var blob = await blobClient.GetPropertiesAsync();
                var items = new List<Document>();

                await foreach (BlobItem item in
                    GetContainerClient().GetBlobsAsync(BlobTraits.Metadata, prefix: prefix, cancellationToken: ct))
                {
                    var document = ProcessBlobItem(item);
                    if (document != null)
                    {
                        items.Add(document);
                    }
                }

                return new Workspace
                {
                    Id = blob.Value.Metadata["id"],
                    Name = blob.Value.Metadata["name"],
                    Description = blob.Value.Metadata.ContainsKey("description") ?
                                  blob.Value.Metadata["description"] : "",
                    Documents = items.Any() ? items.ToList() : null,
                    Expires = blob.Value.LastModified.AddDays(_days).DateTime.ToString("MM/dd/yyyy")!
                };
            }
            catch
            {
                return null!;
            }
        }

        /// <summary>
        /// Creates a new workspace in Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the workspace</param>
        /// <param name="item">Workspace item</param>
        /// <returns></returns>
        public async Task<Workspace> CreateWorkspaceAsync(string oid, Workspace item)
        {
            item.Id = Guid.NewGuid().ToString();
            BlobClient blobClient = GetContainerClient().GetBlobClient(oid + "/" + item.Id + "/empty.txt");

            using (var stream = new MemoryStream(Array.Empty<byte>()))
            {
                await blobClient.UploadAsync(stream);
            }

            blobClient.DeleteIfExists();
            return await UpdateWorkspaceAsync(oid, item);
        }


        /// <summary>
        /// Updates an existing workspace in Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the workspace</param>
        /// <param name="item">Workspace item</param>
        /// <returns></returns>
        public async Task<Workspace> UpdateWorkspaceAsync(string oid, Workspace item)
        {
            BlobClient blobClient = GetContainerClient().GetBlobClient(oid + "/" + item.Id);

            await blobClient.SetMetadataAsync(new Dictionary<string, string>
            {
                { "id", item.Id ?? "" },
                { "name", item.Name ?? "" },
                { "description", item.Description ?? "" }
            });

            BlobProperties properties = await blobClient.GetPropertiesAsync();
            item.Expires = properties.LastModified.AddDays(_days).DateTime.ToString("MM/dd/yyyy")!;

            return item;
        }

        /// <summary>
        /// Refreshes the last modified date of an object and it's children in Blob Storage.
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        public async Task Refresh(string prefix)
        {
            BlobClient blobClient = GetContainerClient().GetBlobClient(prefix);

            await foreach (var item in GetContainerClient()!.GetBlobsByHierarchyAsync(prefix: prefix + "/", delimiter: "/"))
            {
                if (item.Blob != null && item.Blob.Name != prefix)
                {
                    await Refresh(item.Blob.Name);
                }
            }

            BlobProperties properties = await blobClient.GetPropertiesAsync();
            var metadata = new Dictionary<string, string>(properties.Metadata);
            metadata.Remove("hdi_isfolder");
            await blobClient.SetMetadataAsync(metadata);
        }

        /// <summary>
        /// Deletes an object from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <returns></returns>
        public virtual async Task DeleteBlobAsync(string prefix, bool isFile = true)
        {
            BlobClient blobClient = GetContainerClient().GetBlobClient(prefix);
            await blobClient.DeleteAsync();
        }

        /// <summary>
        /// Downloads a document from Blob Storage.
        /// </summary>
        /// <param name="document"></param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        public async Task<Stream> GetDocumentStreamAsync(string document, CancellationToken ct)
        {
            BlobClient blobClient = GetContainerClient().GetBlobClient(document);
            return await blobClient.OpenReadAsync(cancellationToken: ct);
        }

        /// <summary>
        /// Uploads a document to Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the document</param>
        /// <param name="workspaceId">The workspace the document falls under</param>
        /// <param name="fileName">The name of the document</param>
        /// <param name="documentStream">Stream object holding document content</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        public virtual async Task<Document> UploadDocumentsAsync(string oid,
            string workspaceId,
            string fileName,
            Stream documentStream,
            CancellationToken ct,
            string original = ".pdf")
        {
            var result = new Document();
            var blobClient = GetContainerClient().GetBlobClient(oid + "/" + workspaceId + "/" + fileName);
            if (ct.IsCancellationRequested) { return result; }

            try
            {
                documentStream.Seek(0, SeekOrigin.Begin);
                await blobClient.UploadAsync(documentStream, cancellationToken: ct);
            }
            catch (Azure.RequestFailedException)
            {
                await DeleteBlobAsync(oid + "/" + workspaceId + "/" + fileName);
                documentStream.Seek(0, SeekOrigin.Begin);
                await blobClient.UploadAsync(documentStream, cancellationToken: ct);
            }

            result = new Document
            {
                Name = fileName,
                Processed = Processed.False,
                Expires = DateTime.UtcNow.AddDays(_days).ToString("MM/dd/yyyy")!
            };
            _ = Refresh(oid + "/" + workspaceId);
            return result;
        }

        /// <summary>
        /// Process a BlobItem and return a Document object.
        /// </summary>
        /// <param name="item"></param>
        /// <param name="fetchAll"></param>
        /// <returns></returns>
        private Document ProcessBlobItem(BlobItem item, bool fetchAll = false)
        {
            bool isFile = !item.Metadata.ContainsKey("hdi_isfolder") || item.Metadata["hdi_isfolder"] != "true";
            bool isOriginal = !item.Metadata.ContainsKey("original") || item.Metadata["original"] == "true";

            if (isFile && (fetchAll || isOriginal))
            {
                Processed processedStatus = Processed.False;
                if (item.Metadata.ContainsKey("processed"))
                {
                    processedStatus = item.Metadata["processed"] switch
                    {
                        "true" => Processed.True,
                        "error" => Processed.Error,
                        _ => Processed.False
                    };
                }

                return new Document
                {
                    Name = item.Name.Split('/').Last(),
                    Processed = processedStatus,
                    Expires = item.Properties?.LastModified?.AddDays(_days).DateTime.ToString("MM/dd/yyyy")!
                };
            }

            return null!;
        }
    }
}
