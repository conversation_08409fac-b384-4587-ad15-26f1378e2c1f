﻿using Backend.Models;
using System.Security.Cryptography;
using System.Text;

namespace Backend.Services
{
    public class EncryptionService : IEncryptionService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="plainText"></param>
        /// <param name="secret"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        /// <exception cref="NullReferenceException"></exception>
        public string Encrypt(string plainText, string secret, string email)
        {
            if (plainText is null || secret is null || email is null)
            {
                throw new NullReferenceException("All arguments are expected.");
            }

            byte[] bytes = Encoding.UTF8.GetBytes(plainText);

            using (SymmetricAlgorithm crypt = Aes.Create())
            using (HashAlgorithm hash = MD5.Create())
            using (MemoryStream memoryStream = new MemoryStream())
            {
                crypt.Key = hash.ComputeHash(Encoding.UTF8.GetBytes(secret + email.ToLower()));
                // This is really only needed before you call CreateEncryptor the second time,
                // since it starts out random.  But it's here just to show it exists.
                crypt.GenerateIV();

                using (CryptoStream cryptoStream = new CryptoStream(
                    memoryStream, crypt.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    cryptoStream.Write(bytes, 0, bytes.Length);
                }

                string base64IV = Convert.ToBase64String(crypt.IV);
                string base64Ciphertext = Convert.ToBase64String(memoryStream.ToArray());

                return base64IV + "!" + base64Ciphertext;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cipherText"></param>
        /// <param name="secret"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public string Decrypt(string cipherText, string secret, string email)
        {
            string[] splitted = cipherText.Split("!");

            // create a hash from sent secret and email
            using (SymmetricAlgorithm crypt = Aes.Create())
            using (HashAlgorithm hash = MD5.Create())
            using (MemoryStream memoryStream = new MemoryStream(Convert.FromBase64String(splitted[1])))
            {
                crypt.Key = hash.ComputeHash(Encoding.UTF8.GetBytes(secret + email.ToLower()));
                crypt.IV = Convert.FromBase64String(splitted[0]);

                using (CryptoStream cryptoStream = new CryptoStream((Stream)memoryStream, crypt.CreateDecryptor(), CryptoStreamMode.Read))
                {
                    // if the following fails, it's probably because of invalid secret or email. CryptographicException will be thrown.                    
                    using (StreamReader streamReader = new StreamReader((Stream)cryptoStream))
                    {
                        return streamReader.ReadToEnd();
                    }
                }
            }
        }
    }
}
