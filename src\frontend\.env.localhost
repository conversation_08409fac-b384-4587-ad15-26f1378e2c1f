## Backend service for local environment
VITE_API_URL=http://localhost:5280
VITE_MSAL_CLIENT_ID=55d09dfe-6677-4c40-9883-e3eaab0ca31f
VITE_MSAL_AUTHORITY=https://login.microsoftonline.com/6cacd170-f897-4b19-ac58-46a23307b80a
VITE_MSAL_SCOPES=cc093f6b-04fb-4164-8265-cfe975f5a1e2/access_as_user
VITE_MSAL_REGION=LOCALE
VITE_MSAL_CONNECTION_STRING=InstrumentationKey=b2cc7875-3247-4581-9cb9-ee0e3dc0b864;IngestionEndpoint=https://eastus2-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus2.livediagnostics.monitor.azure.com/;ApplicationId=1e09544b-c67e-4359-974a-e2a8d1ba2138

# PDF analysis panel variables
VITE_PDF_WORKER = /node_modules/pdfjs-dist/build/pdf.worker.min.mjs