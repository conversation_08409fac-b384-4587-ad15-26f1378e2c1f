name: Backend-CD-v2

trigger: none

variables:
  - template: variables.yml
  - name: SelectedBranch
    value: $(Build.SourceBranch)

parameters:
  - name: environment
    displayName: 'Environment to deploy'
    type: string
    default: 'DEV'
    values:
      - DEV
      - TEST
      - PROD

  - name: regions
    displayName: 'Regions to deploy'
    type: object
    default:
      - US
      - UK
      - AU

stages:
  - stage: Build
    pool: $(buildAgentPool)
    jobs:
      - job: BuildAndPublishJob
        displayName: 'Build and Publish Backend'
        pool:
          vmImage: 'windows-latest'
        steps:
          # Install .NET 8
          - task: UseDotNet@2
            displayName: 'Install .NET 8'
            inputs:
              version: 8.x
              performMultiLevelLookup: true
              includePreviewVersions: false 

          # Build Backend
          - task: DotNetCoreCLI@2
            displayName: 'Build Backend'
            inputs:
              command: 'build'
              projects: '**/Backend.csproj'
              arguments: '--configuration $(buildConfiguration)'

          # Build all WebJobs
          - task: DotNetCoreCLI@2
            displayName: 'Build WebJobs'
            inputs:
              command: 'build'
              projects: '**/src/scripts/**/*.csproj'
              arguments: '--configuration $(buildConfiguration)'
              exclude: '**/src/scripts/common/common.csproj'

          # Publish Backend
          - task: DotNetCoreCLI@2
            displayName: 'Publish Backend'
            inputs:
              command: 'publish'
              projects: '**/Backend.csproj'
              publishWebProjects: false
              arguments: '--configuration $(buildConfiguration) --output $(Agent.TempDirectory)'
              zipAfterPublish: false

          # Find and publish each WebJob to a separate folder
          - powershell: |
              $csprojFiles = Get-ChildItem -Path "$(Build.SourcesDirectory)/src/scripts" -Recurse -Filter *.csproj | Where-Object { $_.FullName -notlike '*common.csproj' }
              foreach ($csproj in $csprojFiles) {
                  $projectName = [System.IO.Path]::GetFileNameWithoutExtension($csproj.FullName)
                  Write-Host "Publishing $projectName"
                  dotnet publish $csproj.FullName --configuration $(buildConfiguration) --output $(Agent.TempDirectory)/backend/App_Data/jobs/triggered/$projectName
              }
            displayName: 'Publish WebJobs to separate folders'

          # Archive Backend
          - task: ArchiveFiles@2
            displayName: 'Zip Backend'
            inputs:
              rootFolderOrFile: '$(Agent.TempDirectory)/backend'
              includeRootFolder: false
              archiveType: 'zip'
              archiveFile: '$(Build.ArtifactStagingDirectory)/backend.zip'
              replaceExistingArchive: true

          # Security analysis with Snyk
          - template: /.azuredevops/snyk/snyk-sast-sca-windows.yml

          # Publish artifacts
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifacts'
            inputs:
              pathtoPublish: '$(Build.ArtifactStagingDirectory)/backend.zip'
              artifactName: 'backend'

  - stage: Deploy
    dependsOn: Build
    variables:
      environmentVar: ${{ parameters.environment }}
    jobs:
    # Approval job for PROD
      - job: Approval
        displayName: 'Deployment Approval'
        condition: eq(variables['environmentVar'], 'PROD')
        pool: server
        steps:
          - task: ManualValidation@0
            inputs:
              notifyUsers: ''
              instructions: 'Review the deployment and approve or deny.'

      # Deployment job for all regions (dependsOn Approval for PROD only)
      - ${{ each region in parameters.regions }}:
          - ${{ if eq(parameters.environment, 'PROD') }}:
              - template: backend-deploy-job.yml
                parameters:
                  environment: ${{ format('{0}_{1}', region, parameters.environment) }}
                  service_connection: ${{ variables[format('{0}_{1}_ServiceConnection', region, parameters.environment)] }}
                  deploy_agent_pool: ${{ variables[format('{0}_{1}_DeployAgentPool', region, parameters.environment)] }}
                  app_name: ${{ variables[format('{0}_{1}_beAppName', region, parameters.environment)] }}
                  dependsOn: Approval

          - ${{ if ne(parameters.environment, 'PROD') }}:
              - template: backend-deploy-job.yml
                parameters:
                  environment: ${{ format('{0}_{1}', region, parameters.environment) }}
                  service_connection: ${{ variables[format('{0}_{1}_ServiceConnection', region, parameters.environment)] }}
                  deploy_agent_pool: ${{ variables[format('{0}_{1}_DeployAgentPool', region, parameters.environment)] }}
                  app_name: ${{ variables[format('{0}_{1}_beAppName', region, parameters.environment)] }}
