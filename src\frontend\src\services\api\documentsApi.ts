import { api } from '../../app/api';

// Define custom types for the document API
interface DocumentQueryArg {
  workspaceId: string;
  fileName: string;
}

interface DocumentUploadArg {
  workspaceId: string;
  file: File;
}

export const documentsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Upload a document to a workspace
    uploadDocument: builder.mutation<ReadableStream<Uint8Array>, DocumentUploadArg>({
      query: ({ workspaceId, file }) => {
        const formData = new FormData();
        formData.append('document', file);

        return {
          url: `/workspaces/${workspaceId}/documents`,
          method: 'POST',
          body: formData,
          responseHandler: 'content-type',
        };
      },
      // Use underscore prefix to indicate intentionally unused parameters
      invalidatesTags: (_result, _error, { workspaceId }) => [
        { type: 'Workspaces' as const, id: workspaceId },
        'Workspaces' as const,
      ],
    }),

    // Download a document from a workspace
    downloadDocument: builder.query<Blob, DocumentQueryArg>({
      // Use a string URL instead of an object with responseHandler
      // RTK Query will handle the blob response automatically
      query: ({ workspaceId, fileName }) =>
        `/workspaces/${workspaceId}/documents/${fileName}`,
      // Use underscore prefix to indicate intentionally unused parameters
      providesTags: (_result, _error, { workspaceId, fileName }) => [
        { type: 'Documents' as const, id: `${workspaceId}-${fileName}` },
      ],
      // Add a custom transform response to handle the blob
      transformResponse: (response: Response) => response.blob(),
    }),

    // Delete a document from a workspace
    deleteDocument: builder.mutation<void, DocumentQueryArg>({
      query: ({ workspaceId, fileName }) => ({
        url: `/workspaces/${workspaceId}/documents/${fileName}`,
        method: 'DELETE',
      }),
      // Use underscore prefix to indicate intentionally unused parameters
      invalidatesTags: (_result, _error, { workspaceId }) => [
        { type: 'Workspaces' as const, id: workspaceId },
        'Workspaces' as const,
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useUploadDocumentMutation,
  useLazyDownloadDocumentQuery,
  useDeleteDocumentMutation,
} = documentsApi;
