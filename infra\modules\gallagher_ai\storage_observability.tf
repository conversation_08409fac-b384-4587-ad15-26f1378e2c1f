# ****************************************************
#       Need to add diagnostic per resource as
#   OPA fails to find references if done by for_each
# ****************************************************

resource "azurerm_monitor_diagnostic_setting" "asr_diagnostic" {
  count = contains(var.additional_storage_regions, "Canada Central") ? 1 : 0

  name                       = "${azurerm_storage_account.asr["Canada Central"].name}-diagnostic"
  target_resource_id         = azurerm_storage_account.asr["Canada Central"].id
  log_analytics_workspace_id = var.azurerm_log_analytics_workspace_id

  dynamic "metric" {
    for_each = data.azurerm_monitor_diagnostic_categories.sad["Canada Central"].metrics
    content {
      category = metric.value
      enabled  = true
    }
  }

  dynamic "enabled_log" {
    for_each = data.azurerm_monitor_diagnostic_categories.sad["Canada Central"].log_category_types
    content {
      category = enabled_log.value
    }
  }
}