﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Settings which will affect the behavior of search results. 
    /// </summary>
    public class SearchSettings
    {
        /// <summary>
        /// Sets the number of search results to retrieve from Azure AI search.
        /// </summary>
        [JsonPropertyName("top")]
        public Int32 Top { get; set; } = 5;

        /// <summary>
        /// Enables the Azure AI Search semantic ranker, a model that re-ranks 
        /// search results based on semantic similarity to the user's query
        /// </summary>
        [JsonPropertyName("semantic_ranker")]
        public bool UseSemanticRanker { get; set; } = true;

        /// <summary>
        /// The retrieval mode to use. Text uses the text search engine, 
        /// Vector uses the vector search engine, and Hybrid uses both.
        /// </summary>
        [JsonPropertyName("retrieval_mode")] 
        public RetrievalMode RetrievalMode { get; set; } = Models.RetrievalMode.Hybrid;

        /// <summary>
        /// Sends semantic captions to the LLM instead of the full search result. 
        /// A semantic caption is extracted from a search result during the process of semantic ranking.
        /// </summary>
        [JsonPropertyName("semantic_captions")]
        public bool UseSemanticCaptions { get; set; } = false;

        /// <summary>
        /// Sets a minimum score for search results coming back from the semantic reranker. 
        /// The score always ranges between 0-4. The higher the score, the more semantically 
        /// relevant the result is to the question.
        /// </summary>
        [JsonPropertyName("minimum_reranker_score")]
        public double MinimumRerankerScore { get; set; } = 0;

        /// <summary>
        /// Sets a minimum score for search results coming back from Azure AI search.
        /// </summary>
        [JsonPropertyName("minimum_search_score")]
        public double MinimumSearchScore { get; set; } = 0;
    }
}
