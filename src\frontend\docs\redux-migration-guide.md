# Redux Migration Guide

## Overview

This guide assists developers in migrating from legacy Redux patterns to Redux Toolkit (RTK) with RTK Query.

> Effective immediately:
> all new development work in this project should utilize Redux Toolkit and RTK Query, replacing any legacy Redux patterns.


## Key Changes Summary

### Legacy Implementation
- Basic Redux with manual API calls
- Manual loading and error state management
- Individual fetch functions for each API endpoint
- Manual token management
- Basic useSelector and useDispatch hooks

### Modern Implementation
- Redux Toolkit with RTK Query
- Automatic loading and error states
- Centralized API service layer
- Automatic token refresh and management
- Typed Redux hooks for better TypeScript support

## Migration Steps

### 1. Update Hook Imports

**Before:**
```typescript
import { useSelector, useDispatch } from 'react-redux';
```

**After:**
```typescript
import { useAppSelector, useAppDispatch } from '../app/hooks';
```

### 2. Replace Manual API Calls with RTK Query Hooks

**Legacy Pattern:**
```typescript
const [workspaces, setWorkspaces] = useState([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
  const fetchData = async () => {
    try {
      setLoading(true);
      const token = await acquireToken();
      const data = await fetchWorkspaces(token.accessToken);
      setWorkspaces(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  fetchData();
}, []);
```

**Modern Pattern:**
```typescript
import { useGetWorkspacesQuery } from '../services/api/workspacesApi';

const { data: workspaces = [], isLoading: loading, error } = useGetWorkspacesQuery();
```

### 3. Update Component Authentication

**Legacy Pattern:**
```typescript
const { activeAccount, acquireToken } = useAuth();

useEffect(() => {
  const getToken = async () => {
    if (activeAccount) {
      const token = await acquireToken();
      // Use token for API calls
    }
  };
  getToken();
}, [activeAccount, acquireToken]);
```

**Modern Pattern:**
```typescript
import useApiWithAuth from '../hooks/useApiWithAuth';

const { isAuthenticated, refreshToken } = useApiWithAuth();

useEffect(() => {
  if (!isAuthenticated) {
    refreshToken();
  }
}, [isAuthenticated, refreshToken]);
```

### 4. Update Mutation Patterns

**Legacy Pattern:**
```typescript
const handleCreate = async () => {
  try {
    setLoading(true);
    const token = await acquireToken();
    const result = await createWorkspace(workspaceData, token.accessToken);
    setWorkspaces(prev => [...prev, result]);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

**Modern Pattern:**
```typescript
import { useCreateWorkspaceMutation } from '../services/api/workspacesApi';

const [createWorkspace, { isLoading }] = useCreateWorkspaceMutation();

const handleCreate = async () => {
  try {
    await createWorkspace(workspaceData).unwrap();
    // Cache automatically updates, no manual state management needed
  } catch (error) {
    console.error('Failed to create workspace:', error);
  }
};
```

### 5. Update Error Handling

**Legacy Pattern:**
```typescript
const [error, setError] = useState(null);

// Manual error handling in each API call
try {
  const result = await apiCall();
} catch (err) {
  setError(err.message);
}
```

**Modern Pattern:**
```typescript
const { data, isLoading, error } = useGetWorkspacesQuery();

// RTK Query provides structured error handling
if (error) {
  if ('status' in error) {
    // Handle different error types
    if (error.status === 401) {
      // Handle authentication error
    }
  }
}
```

## Component Migration Examples

### Example 1: Workspaces List Component

**Legacy Implementation:**
```typescript
const WorkspacesList = () => {
  const { activeAccount, acquireToken } = useAuth();
  const [workspaces, setWorkspaces] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchWorkspaces = async () => {
      try {
        if (!activeAccount) return;

        setLoading(true);
        const token = await acquireToken();
        const data = await workspacesService.fetchWorkspaces(token.accessToken);
        setWorkspaces(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkspaces();
  }, [activeAccount, acquireToken]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {workspaces.map(workspace => (
        <div key={workspace.id}>{workspace.name}</div>
      ))}
    </div>
  );
};
```

**Modern Implementation:**
```typescript
import { useGetWorkspacesQuery } from '../services/api/workspacesApi';
import useApiWithAuth from '../hooks/useApiWithAuth';

const WorkspacesList = () => {
  const { isAuthenticated } = useApiWithAuth();
  const { data: workspaces = [], isLoading, error } = useGetWorkspacesQuery(undefined, {
    skip: !isAuthenticated
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading workspaces</div>;

  return (
    <div>
      {workspaces.map(workspace => (
        <div key={workspace.id}>{workspace.name}</div>
      ))}
    </div>
  );
};
```

### Example 2: Settings Component

**Legacy Implementation:**
```typescript
const SettingsComponent = () => {
  const dispatch = useDispatch();
  const settings = useSelector(state => state.settings);

  const handleSettingChange = (key, value) => {
    dispatch(setSetting({ [key]: value }));
    // Manual persistence to IndexedDB
    saveToIndexedDB(key, value);
  };

  return (
    <div>
      <input
        value={settings.theme}
        onChange={(e) => handleSettingChange('theme', e.target.value)}
      />
    </div>
  );
};
```

**Modern Implementation:**
```typescript
import { useAppSelector, useAppDispatch } from '../app/hooks';
import { useUpdateSettingsMutation } from '../services/api/settingsApi';

const SettingsComponent = () => {
  const dispatch = useAppDispatch();
  const settings = useAppSelector(state => state.settings);
  const [updateSettings] = useUpdateSettingsMutation();

  const handleSettingChange = async (key: string, value: any) => {
    // Update Redux state
    dispatch(setSetting({ [key]: value }));

    // Persist to IndexedDB automatically
    try {
      await updateSettings({ [key]: value }).unwrap();
    } catch (error) {
      console.error('Failed to save setting:', error);
    }
  };

  return (
    <div>
      <input
        value={settings.theme}
        onChange={(e) => handleSettingChange('theme', e.target.value)}
      />
    </div>
  );
};
```

## Common Migration Patterns

### 1. Loading States
**Legacy:** Manual loading state management
**Modern:** Automatic loading states from RTK Query

### 2. Error Handling
**Legacy:** Manual error state management
**Modern:** Structured error objects from RTK Query

### 3. Cache Management
**Legacy:** Manual state updates after mutations
**Modern:** Automatic cache invalidation and updates

### 4. Authentication
**Legacy:** Manual token management in each component
**Modern:** Centralized authentication with `useApiWithAuth`

### 5. Type Safety
**Legacy:** Basic TypeScript support
**Modern:** Full type safety with typed hooks and API responses

## Key Changes to Consider

### 1. Hook Return Values
- RTK Query hooks return different object structures
- Loading states are now `isLoading` instead of `loading`
- Error objects have different structure

### 2. Mutation Patterns
- Mutations now return promises that need `.unwrap()` for error handling
- Automatic cache invalidation replaces manual state updates

### 3. Authentication Flow
- Components must use `useApiWithAuth` for API calls
- Token management is now centralized

### 4. Error Types
- RTK Query errors have specific structure with `status` and `data` properties
- Different error handling patterns required

## Testing Updates

### Legacy Testing:
```typescript
// Mock individual API functions
jest.mock('../services/workspacesService', () => ({
  fetchWorkspaces: jest.fn()
}));
```

### Modern Testing:
```typescript
// Mock RTK Query hooks
jest.mock('../services/api/workspacesApi', () => ({
  useGetWorkspacesQuery: jest.fn()
}));
```

## Benefits of Migration

1. **Automatic Caching**: Reduces redundant API calls
2. **Background Refetching**: Keeps data fresh automatically
3. **Optimistic Updates**: Immediate UI feedback
4. **Reduced Bundle Size**: Tree-shaking eliminates unused code
5. **Better Re-render Control**: More granular state subscriptions

## Troubleshooting Common Issues

### Issue 1: "Cannot read property of undefined"
**Cause:** RTK Query data might be undefined during loading
**Solution:** Use default values: `const { data: workspaces = [] } = useGetWorkspacesQuery()`

### Issue 2: Authentication errors
**Cause:** Missing `useApiWithAuth` hook
**Solution:** Add the hook to components making API calls

### Issue 3: Cache not updating after mutations
**Cause:** Missing or incorrect cache tag invalidation
**Solution:** Verify `invalidatesTags` in mutation definitions

### Issue 4: TypeScript errors
**Cause:** Type mismatches between old and new implementations
**Solution:** Update component props and state types to match RTK Query patterns

## Best Practices

1. **Test thoroughly**: Ensure all functionality works with RTK implementation
2. **Update tests**: Modify unit tests to work with RTK Query mocks
3. **Monitor performance**: Check for improvements in loading times and user experience
4. **Follow patterns**: Maintain consistency with established RTK patterns
5. **Use TypeScript**: Leverage full type safety with typed hooks

## Resources

- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)
- [RTK Query Documentation](https://redux-toolkit.js.org/rtk-query/overview)
- [TypeScript with RTK Query](https://redux-toolkit.js.org/rtk-query/usage/typescript)
- [Project API Documentation](../src/services/api/README.md)
