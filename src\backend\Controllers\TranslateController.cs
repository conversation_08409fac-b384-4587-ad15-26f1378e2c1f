﻿using Backend.Models;
using Backend.Validators;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Backend.Extensions;
using Backend.Models.Context;
using System.Net.Http;

namespace Backend.Controllers
{
    [Authorize]
    [EnableCors]
    [ApiController]
    [Route("/translate")]
    public class TranslateController : ControllerBase
    {
        private readonly IConversionService _conversionService;
        private readonly ITranslationService _translationService;

        private readonly ILogger _logger;

        private static readonly HashSet<string> _validExtensions = new HashSet<string> { ".docx", ".doc", ".pptx", ".ppt" };

        public TranslateController(IConversionService conversionService,
                                   ITranslationService translationService,
                                   ILogger<TranslateController>logger)
        {
            _conversionService = conversionService;
            _translationService = translationService;
            _logger = logger;
        }

        [HttpPost("text")]
        public async Task<IActionResult> OnTextTranslateAsync(
            [FromForm] string language,
            [FromForm] string text,
                        CancellationToken ct)
        {

            ControllerExtensions.SetContext(HttpContext);
            
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return BadRequest("Text cannot be empty.");
            }

           var translatedText = await _translationService.TextTranslate(text, language);
            if (string.IsNullOrEmpty(translatedText))
                return BadRequest("Translation failed");
           return Ok(new {TranslatedText= translatedText});
        }}

        // Get a list of all support languages
        [HttpGet("languages")]
        public string languages()
        {
            string jsonFilePath = "LanguageConfig.json";
            return System.IO.File.ReadAllText(jsonFilePath);

        }

        // Translate endpoint
        [HttpPost("translate")]
        public async Task<IActionResult> OnTranslateAsync(
            //string workspaceId,
            [FromForm] IFormFileCollection documents,
            [FromForm] string language,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            if (documents.Count == 0)
            {
                throw new BadHttpRequestException("No document(s) were provided in the request.");
            }

            string userEmail = CurrentContext.User.DisplayName!;
;
            var documentCount = documents.Count;


            var tasks = documents.Select(async document =>
            {
                await using var docStream = document.OpenReadStream();
                var extension = Path.GetExtension(document.FileName).ToLower();

                if (_validExtensions.Contains(extension))
                {
                    using var pdfStream = await _conversionService.ConvertToPDF(document.FileName, docStream);
                    PdfValidator.Validate(pdfStream, document.Length, document.FileName);

                    //upload Document to Documents folder
                    await _translationService.UploadDocumentsAsync(CurrentContext.User.OID!, "/Documents", document.FileName, docStream, ct, extension);

                    //call translation
                    await _translationService.DocumentTranslate(document.FileName, language);

                    //Delete from Documents folder
                    await _translationService.DeleteBlobAsync(CurrentContext.User.OID + "/" + "Documents" + "/" + document.FileName);
                }
                else if (extension == ".pdf")
                {
                    PdfValidator.Validate(docStream, document.Length, document.FileName);

                    //upload Document to Documents folder
                    await _translationService.UploadDocumentsAsync(CurrentContext.User.OID!, "/Documents", document.FileName, docStream, ct, extension);

                    //call translation
                    await _translationService.DocumentTranslate(document.FileName, language);

                    //Delete from Documents folder
                    await _translationService.DeleteBlobAsync(CurrentContext.User.OID + "/" + "Documents" + "/" + document.FileName);

                }
            });

            await Task.WhenAll(tasks);

            // Sanitize log inputs to prevent log forging
            string? safeUserEmail = userEmail?.Replace("\r", string.Empty).Replace("\n", string.Empty);
            string? safeLanguage = language?.Replace("\r", string.Empty).Replace("\n", string.Empty);
            string safeDocumentCount = documentCount.ToString().Replace("\r", string.Empty).Replace("\n", string.Empty);

            _logger.LogInformation(
                "DocumentTranslation: User: {User}, Language: {Language} , DocumentCount: {DocumentCount}",
                safeUserEmail, safeLanguage, safeDocumentCount
            );

            return Ok("Translation completed.");
        }


        //Lists out the translated files
        [HttpGet("documents")]
        public async Task<IActionResult> Documents(
            CancellationToken ct)
        {

            ControllerExtensions.SetContext(HttpContext);
            var documents = await _translationService.GetDocuments($"{CurrentContext.User.OID}/Translated", ct);

            return Ok(documents);
        }



        // Download endpoint
        [HttpGet("download/{document}")]
        public async Task<IActionResult> OnDownloadDocumentAsync(
            //string workspaceId,
            string? document,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            if (!ct.IsCancellationRequested)
            {
                var stream = await _translationService.GetDocumentStreamAsync(CurrentContext.User.OID + "/" + "Translated" + "/" + document, ct);
                return new FileStreamResult(stream, "application/octet-stream")
                {
                    FileDownloadName = document
                };
            }
            return new EmptyResult();
        }


        // Delete Document endpoint
        [HttpDelete("delete/{document}")]
        public async Task<IActionResult> OnDeleteDocumentAsync(
            //string workspaceId,
            string document,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            if (!ct.IsCancellationRequested)
            {
                await _translationService.DeleteBlobAsync(CurrentContext.User.OID + "/" + "Translated" + "/" + document);
                return NoContent();
            }
            return new EmptyResult();
        }

    }
}