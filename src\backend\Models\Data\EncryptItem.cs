﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// 
    /// </summary>
    public record class EncryptItem
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("data")] 
        public required string Data { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonPropertyName("email")] 
        public required string Email { get; set; }
    }
}
