import { api } from '../../app/api';
import { UserSettingsTypes } from '../../interfaces';
import { USER_SETTINGS_ID } from '../../constants/dbConstants';
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY } from '../../constants/SettingsTabConstants';

// Define default settings
const defaultSettings: UserSettingsTypes = {
  id: USER_SETTINGS_ID,
  temperature: TEMPERATURE.defaultValue,
  topP: TOP_P.defaultValue,
  frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
  presencePenalty: PRESENCE_PENALTY.defaultValue,
  template: "",
};

// This is a local API service that interacts with IndexedDB
// It doesn't make HTTP requests to the backend
export const settingsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get user settings
    getUserSettings: builder.query<UserSettingsTypes, void>({
      queryFn: async () => {
        try {
          // Import the IndexedDB functions dynamically to avoid circular dependencies
          const { initUserSettingDB, getUserSetting } = await import('../../db/settingDB');

          await initUserSettingDB();
          const settings = await getUserSetting(USER_SETTINGS_ID);

          return { data: settings || defaultSettings };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Unknown error',
            }
          };
        }
      },
      providesTags: ['Settings' as const],
    }),

    // Save user settings
    saveUserSettings: builder.mutation<UserSettingsTypes, UserSettingsTypes>({
      queryFn: async (settings) => {
        try {
          // Import the IndexedDB functions dynamically to avoid circular dependencies
          const { saveUserSetting } = await import('../../db/settingDB');

          await saveUserSetting(settings);
          return { data: settings };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Unknown error',
            }
          };
        }
      },
      invalidatesTags: ['Settings' as const],
    }),

    // Reset user settings to defaults
    resetUserSettings: builder.mutation<UserSettingsTypes, void>({
      queryFn: async () => {
        try {
          // Import the IndexedDB functions dynamically to avoid circular dependencies
          const { saveUserSetting } = await import('../../db/settingDB');

          await saveUserSetting(defaultSettings);
          return { data: defaultSettings };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Unknown error',
            }
          };
        }
      },
      invalidatesTags: ['Settings' as const],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetUserSettingsQuery,
  useSaveUserSettingsMutation,
  useResetUserSettingsMutation,
} = settingsApi;
