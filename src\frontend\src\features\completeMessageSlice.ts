import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CompleteMessageState } from "../interfaces";

const initialState: CompleteMessageState = {};

const completeMessageSlice = createSlice({
  name: "completeMessage",
  initialState,
  reducers: {
    setCompleteMessage: (state, action: PayloadAction<{ [key: number]: boolean }>) => {
      Object.keys(action.payload).forEach((key) => {
        const numericKey = Number(key);
        state[numericKey] = action.payload[numericKey];
      });
    },
    clearCompleteMessage: () => {
      return initialState;
    }
  },
});

export const { setCompleteMessage, clearCompleteMessage } = completeMessageSlice.actions;

export default completeMessageSlice.reducer;
