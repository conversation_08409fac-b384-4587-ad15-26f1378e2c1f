# *****************************************
#          Primary Search Service
# *****************************************

resource "random_string" "srch_random" {
  length  = 5
  special = false
  upper   = false
  lower   = true
  numeric = true
}

resource "azurerm_search_service" "search" {
  name                          = "corp-${lower(var.project_name)}${lower(var.region_instance)}-srch-${var.environment}-${random_string.srch_random.result}"
  location                      = var.azurerm_resource_group_location
  resource_group_name           = var.azurerm_resource_group_name
  public_network_access_enabled = false
  tags = {
    environment = var.environment
  }

  identity {
    type = "SystemAssigned"
  }

  sku                          = var.search_settings.sku_name
  semantic_search_sku          = var.search_settings.semantic_sku
  replica_count                = var.search_settings.replica_count
  local_authentication_enabled = false
}

resource "azurerm_monitor_diagnostic_setting" "search" {
  name                       = "diag-${azurerm_search_service.search.name}"
  target_resource_id         = azurerm_search_service.search.id
  log_analytics_workspace_id = var.azurerm_log_analytics_workspace_id

  enabled_log {
    category_group = "audit"
  }

  metric {
    category = "AllMetrics"
  }
}

resource "azurerm_private_endpoint" "search_pe" {
  name                          = "${azurerm_search_service.search.name}-pe"
  location                      = azurerm_search_service.search.location
  resource_group_name           = var.azurerm_resource_group_name
  subnet_id                     = var.azurerm_private_endpoint_subnet_id
  custom_network_interface_name = "${azurerm_search_service.search.name}-pe-nic"
  tags = {
    environment = var.environment
  }

  private_service_connection {
    name                           = "psc-${azurerm_search_service.search.name}"
    is_manual_connection           = false
    private_connection_resource_id = azurerm_search_service.search.id
    subresource_names              = ["searchService"]
  }
  private_dns_zone_group {
    name                 = "default"
    private_dns_zone_ids = [var.search_dns_zone_id]
  }
}

# *****************************************
#   Additional Regional Search Service 
# *****************************************

resource "random_string" "add_srch_random" {
  for_each = toset(var.additional_storage_regions)
  length   = 5
  special  = false
  upper    = false
  lower    = true
  numeric  = true
}

resource "azurerm_search_service" "add_search" {
  for_each                      = toset(var.additional_storage_regions)
  name                          = "corp-${lower(var.project_name)}${lower(var.region_instance)}-srch-${var.environment}-${random_string.add_srch_random[each.key].result}"
  location                      = each.key
  resource_group_name           = var.azurerm_resource_group_name
  public_network_access_enabled = false
  tags = {
    environment = var.environment
  }

  identity {
    type = "SystemAssigned"
  }

  sku                          = var.search_settings.sku_name
  semantic_search_sku          = var.search_settings.semantic_sku
  replica_count                = var.search_settings.replica_count
  local_authentication_enabled = false
}

resource "azurerm_monitor_diagnostic_setting" "add_search" {
  for_each                   = azurerm_search_service.add_search
  name                       = "diag-${each.value.name}"
  target_resource_id         = each.value.id
  log_analytics_workspace_id = var.azurerm_log_analytics_workspace_id

  enabled_log {
    category_group = "audit"
  }

  metric {
    category = "AllMetrics"
  }
}

resource "azurerm_private_endpoint" "add_search_pe" {
  for_each                      = azurerm_search_service.add_search
  name                          = "${each.value.name}-pe"
  location                      = var.azurerm_resource_group_location
  resource_group_name           = var.azurerm_resource_group_name
  subnet_id                     = var.azurerm_private_endpoint_subnet_id
  custom_network_interface_name = "${each.value.name}-pe-nic"
  tags = {
    environment = var.environment
  }

  private_service_connection {
    name                           = "psc-${each.value.name}"
    is_manual_connection           = false
    private_connection_resource_id = each.value.id
    subresource_names              = ["searchService"]
  }
  private_dns_zone_group {
    name                 = "default"
    private_dns_zone_ids = [var.search_dns_zone_id]
  }
}