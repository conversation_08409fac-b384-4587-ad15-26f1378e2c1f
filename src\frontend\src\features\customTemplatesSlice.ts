import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type CustomTemplatePropType = {
    id: string;
    label: string;
    description: string;
    template: 'custom';
    default: false;
};

export interface CustomTemplateState {
    templates: CustomTemplatePropType[];
}

const initialState: CustomTemplateState = {
    templates: [],
};

const customTemplateSlice = createSlice({
    name: 'customTemplate',
    initialState,
    reducers: {
        addCustomTemplate: (state, action: PayloadAction<CustomTemplatePropType>) => {
            state.templates.push(action.payload);
        },
    },
});

export const { addCustomTemplate } = customTemplateSlice.actions;
export default customTemplateSlice.reducer;