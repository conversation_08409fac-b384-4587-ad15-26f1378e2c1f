﻿using System.ComponentModel.DataAnnotations;

namespace Backend.Validators
{
    public class RequiredProperty : ValidationAttribute
    {
        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return new ValidationResult($"{validationContext.DisplayName} is required.");
            }

            return ValidationResult.Success!;
        }
    }
}
