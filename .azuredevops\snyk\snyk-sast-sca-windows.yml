parameters:
  - name: scanDirectory
    type: string
    default: 'backend'

steps:
  # Ensure that the Snyk executables have execution permissions.
  - script: |
      if exist "$(Agent.TempDirectory)\snyk-win.exe" (
          echo "snyk-win.exe found. Granting full permissions..."
          icacls "$(Agent.TempDirectory)\snyk-win.exe" /grant Everyone:F
      ) else (
          echo "snyk-win.exe not found; it will be downloaded later."
      )
      if exist "$(Agent.TempDirectory)\snyk-to-html-win.exe" (
          echo "snyk-to-html-win.exe found. Granting full permissions..."
          icacls "$(Agent.TempDirectory)\snyk-to-html-win.exe" /grant Everyone:F
      ) else (
          echo "snyk-to-html-win.exe not found; it will be downloaded later."
      )
    displayName: 'Ensure Snyk executables are executable'
    condition: always()

  - script: |
      echo "Verifying snyk-win.exe right before SAST execution..."
      echo "Temp directory: $(Agent.TempDirectory)"
      dir "$(Agent.TempDirectory)"
      echo "--- Permissions snyk-win.exe ---"
      icacls "$(Agent.TempDirectory)\snyk-win.exe" /T
      echo "--- Verification END ---"
    displayName: 'Diagnostic: Verify snyk-win.exe before SAST'
    condition: always() # Execute always to see the status
    continueOnError: true # Allow to continue to Snyk task although this fails

  - task: SnykSecurityScan@1
    inputs:
      serviceConnectionEndpoint: 'Snyk_Connection'
      testType: 'code'
      failOnIssues: true
      organization: 'corp-info-serv-and-appdev-ajg-corp'
      testDirectory: '$(Build.SourcesDirectory)/src/${{ parameters.scanDirectory }}'
    condition: always()
    displayName: SAST
    continueOnError: true

  - script: |
      if exist $(Agent.TempDirectory)\snyk-reports (
          echo snyk-reports folder exists
      ) else (
          echo creatting snyk-reports folder
          mkdir "$(Agent.TempDirectory)\snyk-reports"
      )
      if exist $(Agent.TempDirectory)\report*.json (
          ping -n 5 localhost
          move  $(Agent.TempDirectory)\report*.json "$(Agent.TempDirectory)\snyk-reports\SAST-report.json"
      ) else (
          echo SAST JSON files do not exist
      )
      if exist $(Agent.TempDirectory)\report*.html (
          ping -n 5 localhost
          move  $(Agent.TempDirectory)\report*.html "$(Agent.TempDirectory)\snyk-reports\SAST-report.html"
      ) else (
          echo SAST HTML files do not exist
      )
    displayName: 'Getting SAST Reports'
    condition: always()
    continueOnError: true

  - task: SnykSecurityScan@1
    inputs:
      serviceConnectionEndpoint: 'Snyk_Connection'
      testType: 'app'
      monitorWhen: 'always'
      failOnIssues: true
      organization: 'corp-info-serv-and-appdev-ajg-corp'
      testDirectory: '$(Build.SourcesDirectory)/src/${{ parameters.scanDirectory }}'
    condition: always()
    displayName: SCA
    continueOnError: true

  # Generating the SCA reports(html and json)
  - script: |
      set temp_directory=$(Agent.TempDirectory)
      if exist $(Agent.TempDirectory)\snyk-reports (
        echo snyk-reports folder exists
      ) else (
        echo creatting snyk-reports folder
        mkdir "$(Agent.TempDirectory)\snyk-reports"
      )
      if exist $(Agent.TempDirectory)\report*.json (
        move  $(Agent.TempDirectory)\report*.json "$(Agent.TempDirectory)\snyk-reports\SCA-report.json"
      ) else (
        echo SCA JSON files do not exist
      )
      if exist $(Agent.TempDirectory)\report*.html (
        move  $(Agent.TempDirectory)\report*.html "$(Agent.TempDirectory)\snyk-reports\SCA-report.html"
      ) else (
        echo SCA HTML files do not exist
      )
    displayName: 'Getting SCA Reports'
    condition: always()
    continueOnError: true

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Snyk Reports'
    inputs:
      targetPath: '$(Agent.TempDirectory)\snyk-reports'
      artifact: 'snyk-report-backend'
    condition: always()
    continueOnError: true
