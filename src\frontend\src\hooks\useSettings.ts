import { useState, useEffect, useCallback } from 'react';
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY, SYSTEM_TEMPLATE, CUSTOM_TEMPLATE_LIMIT } from "../constants/SettingsTabConstants";
import { Stores, USER_SETTINGS_ID } from "../constants/dbConstants";
import { initUserSettingDB, getUserSetting, saveUserSetting, getCustomTemplate, updateOrSaveCustomTemplate, getSelectedCustomTemplate, saveSelectedCustomTemplate } from "../db/settingDB";
import { UserSettingsTypes } from '../interfaces';
import { setSetting } from '../features/settingsSlice';
import { useAppDispatch, useAppSelector } from '../app/hooks';
import {
  useGetUserSettingsQuery,
  useSaveUserSettingsMutation,
  useResetUserSettingsMutation
} from '../services/api/settingsApi';
import { CustomTemplatePropType, MergedTemplateListPropTypes } from '../types/dbTypes';
import { removeDuplicates } from '../utils/removeDuplicateTemplates';
import { setSessionCallFlag } from '../features/sessionFlagSlice';
import { RootState } from '../store';

const useSettings = () => {
  // Default settings for initial state
  const defaultSettings: UserSettingsTypes = {
    id: USER_SETTINGS_ID,
    temperature: TEMPERATURE.defaultValue,
    topP: TOP_P.defaultValue,
    frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
    presencePenalty: PRESENCE_PENALTY.defaultValue,
    template: SYSTEM_TEMPLATE.find((template) => template.default === true)?.description || ""
  };

  // Get settings from Redux store
  const reduxSettings = useAppSelector(state => state.settings);
  const newSessionFlag = useAppSelector((state: RootState) => state.sessionFlag.status);

  // RTK Query hooks
  const { data: fetchedSettings, isLoading } = useGetUserSettingsQuery(undefined, {
    // Skip initial fetch if we already have settings in Redux
    skip: false
  });
  const [saveUserSettings, { isLoading: isSaving }] = useSaveUserSettingsMutation();
  const [resetUserSettings, { isLoading: isResetting }] = useResetUserSettingsMutation();

  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();

  // Local state for settings that are being edited - initialize with default settings
  // This ensures we always have valid settings even before data is loaded
  const [editedSettings, setEditedSettings] = useState<UserSettingsTypes>(defaultSettings);

  // Custom template state
  const [selectedTemplateLabel, setSelectedTemplateLabel] = useState<string>("");
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
  const [isTextareaDisabled, setIsTextareaDisabled] = useState<boolean>(true);
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(false);
  const [textareaValue, setTextareaValue] = useState<string>(SYSTEM_TEMPLATE[0].description);
  const [templateList, setTemplateList] = useState<MergedTemplateListPropTypes[]>(SYSTEM_TEMPLATE);
  const [customTemplateDB, setCustomTemplateDB] = useState<CustomTemplatePropType[]>([]);
  const fetchTemplates = async () => {
    await initUserSettingDB();
    const customTemplate = await getCustomTemplate();
    setCustomTemplateDB(customTemplate);
    const uniqueTemplates = removeDuplicates([...SYSTEM_TEMPLATE, ...customTemplate]);
    setTemplateList(uniqueTemplates);
  };

  useEffect(() => {
    if (newSessionFlag) {
      async function init() {
        await initUserSettingDB();
        await saveSelectedCustomTemplate(SYSTEM_TEMPLATE[0]);
        const defaultSettings = {
          id: USER_SETTINGS_ID,
          temperature: TEMPERATURE.defaultValue,
          topP: TOP_P.defaultValue,
          frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
          presencePenalty: PRESENCE_PENALTY.defaultValue,
          template: "",
        };
        const settings = await getUserSetting(USER_SETTINGS_ID);
        if (!settings) {
          saveUserSetting(defaultSettings);
          dispatch(setSetting(defaultSettings));
        } else {
          settings.template = "";
          saveUserSetting(settings);
        }
        dispatch(setSessionCallFlag(false));
        dispatch(setSetting(settings));
      }
      init();
    }
  }, [newSessionFlag, dispatch]);

  useEffect(() => {
    fetchTemplates();
  }, []);

  useEffect(() => {
    const customTemplates = templateList.filter((template) => template.template === "custom");
    setIsButtonDisabled(customTemplates.length >= CUSTOM_TEMPLATE_LIMIT);
  }, [templateList]);

  // Update local state when Redux or fetched settings change
  useEffect(() => {
    // Use fetched settings if available, otherwise use Redux settings, or fall back to defaults
    const currentSettings = fetchedSettings || reduxSettings || defaultSettings;
    setEditedSettings(currentSettings);
  }, [fetchedSettings, reduxSettings]);

  // Update Redux store when settings are fetched
  useEffect(() => {
    if (fetchedSettings && !isLoading) {
      dispatch(setSetting(fetchedSettings));
    }
  }, [fetchedSettings, isLoading, dispatch]);

  useEffect(() => {
    const fetchData = async () => {
      await initUserSettingDB();
      const savedSettings = await getUserSetting(Stores.UserSettings);
      const customTemplate = await getCustomTemplate();
      const selectedTemplate = await getSelectedCustomTemplate();

      if (savedSettings) {
        setEditedSettings(savedSettings);
        dispatch(setSetting(savedSettings));
        if (selectedTemplate) {
          setSelectedTemplateLabel(selectedTemplate.label);
          setSelectedTemplateId(selectedTemplate.id);
          setTextareaValue(selectedTemplate.description);
          setIsTextareaDisabled(selectedTemplate.template === "system");
        } else {
          const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.description === savedSettings.template);
          if (defaultTemplate) {
            setSelectedTemplateLabel(defaultTemplate.label);
            setSelectedTemplateId(defaultTemplate.id);
            setIsTextareaDisabled(defaultTemplate.template === "system");
            setTextareaValue(defaultTemplate.description);
          }
        }
      } else {
        const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
        if (defaultTemplate) {
          setSelectedTemplateLabel(defaultTemplate.label);
          setSelectedTemplateId(defaultTemplate.id);
          setIsTextareaDisabled(defaultTemplate.template === "system");
          setTextareaValue(defaultTemplate.description);
        }
      }
      const uniqueTemplates = removeDuplicates([...SYSTEM_TEMPLATE, ...customTemplate]);
      setTemplateList(uniqueTemplates);
    };
    fetchData();
  }, [dispatch, fetchedSettings, isLoading]);

  useEffect(() => {
    setTemplateList(prevTemplateList => {
      const updatedList = removeDuplicates([...prevTemplateList, ...customTemplateDB]);
      return updatedList;
    });
    const isNewSelectedTemplate = templateList.some((template) => template.id === selectedTemplateId);
    if (!isNewSelectedTemplate) {
      const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
      if (defaultTemplate) {
        setSelectedTemplateLabel(defaultTemplate.label);
        setSelectedTemplateId(defaultTemplate.id);
        setTextareaValue(defaultTemplate.description);
        setIsTextareaDisabled(defaultTemplate.template === "system");
      }
    }
  }, [customTemplateDB]);

  const handleTemplateChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedLabel = event.target.value;
    const selectedTemplate = templateList.find((template) => template.label === selectedLabel);
    if (selectedTemplate) {
      setTextareaValue(selectedTemplate.description);
      setSelectedTemplateLabel(selectedLabel);
      setSelectedTemplateId(selectedTemplate.id);
      setIsTextareaDisabled(selectedTemplate.template === "system");
    }
  };

  const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextareaValue(event.target.value);
  };

  // Function to update a single setting
  const updateSetting = useCallback((key: keyof UserSettingsTypes, value: number) => {
    setEditedSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // Save settings function - enhanced with template support
  const saveSettings = async (newSettings?: UserSettingsTypes) => {
    try {
      const settingsToSave = newSettings || editedSettings;
      const updatedSettings = {
        ...settingsToSave,
        template: textareaValue === SYSTEM_TEMPLATE[0].description ? "" : textareaValue
      };

      // Save to IndexedDB via RTK Query
      const result = await saveUserSettings(updatedSettings).unwrap();
      await updateOrSaveCustomTemplate(selectedTemplateId, textareaValue, templateList);

      // Update local state
      setEditedSettings(result);

      // Update Redux store
      dispatch(setSetting(result));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  // Reset settings function - enhanced with template support
  const resetSettings = async () => {
    try {
      // Reset to defaults via RTK Query
      const result = await resetUserSettings().unwrap();

      await saveSelectedCustomTemplate(SYSTEM_TEMPLATE.filter((template) => template.default === true)[0]);

      // Update local state
      setEditedSettings(result);

      // Update Redux store
      dispatch(setSetting(result));

      const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
      if (defaultTemplate) {
        setSelectedTemplateLabel(defaultTemplate.label);
        setSelectedTemplateId(defaultTemplate.id);
        setIsTextareaDisabled(defaultTemplate.template === "system");
        setTextareaValue(defaultTemplate.description);
      }
    } catch (error) {
      console.error('Failed to reset settings:', error);
    }
  };


  return {
    settings: editedSettings,
    updateSetting,
    saveSettings,
    resetSettings,
    isLoading: isLoading || isSaving || isResetting,
    // Template-related returns
    selectedTemplateLabel,
    isTextareaDisabled,
    isButtonDisabled,
    textareaValue,
    handleTemplateChange,
    handleTextareaChange,
    templateList,
  };
};

export default useSettings;