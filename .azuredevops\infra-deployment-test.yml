trigger: none
# trigger:
#  branches:
#    include:
#    - test

resources:
  repositories:
    - repository: iac-pipeline
      type: git
      name: GTS-InfrastructureAsCode-CenterOfExcellence\Pipeline-GenericInfrastructureDeployment
      ref: refs/heads/release
      endpoint: IaC-CoE-v2-Connection

variables:
- group: IaC-CenterOfExcellence-Integration-Variables-v2
- template: infra-variables.yml

extends:
  template: core-deployment.yml@iac-pipeline
  parameters:
    environmentsToTarget:
    - test_us
    - test_uk
    - test_au
    repositoryName: GallagherAI
    approvalUsers: u-AZURE_ AJG-CORP_OpenAIGlobalGPT_Contributors_Development
    terraformVersion: v1.11.1
    version: v3
    multiEnvironmentDeployment: true
    terraformSecretMapper:
      TF_TOKEN_app_terraform_io: $(READ_TFE_TOKEN)