import { marked } from "marked";

export class extractFromMessageUtils {
  // Function to escape HTML characters
  static escapeHtml(unsafe: string): string {
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;");
  }

  // Function to extract plain text from markdown
  static extractPlainText(cleanedText: string): string {
    const htmlFreeText = cleanedText.replace(/<[^>]*>|```[a-zA-Z]*\n?/g, "");
    const plainText = htmlFreeText.replace(
      /(\s\[\d+\]|\*\*|__|\*|_|`|~|#|\(|\)|!)/g,
      ""
    );
    return plainText;
  }

  // Function to convert markdown to HTML
  static async extractHtml(cleanedText: string): Promise<string> {
    return marked(cleanedText);
  }

  // Function to extract error details
  static extractErrorDetails(errorText: string): string {
    try {
      const errorObj = JSON.parse(errorText);
      const message = errorObj.Message || "No message available.";
      return message.replace(
        /\\u([\dA-F]{4})/gi,
        (_match: string, grp: string) => String.fromCharCode(parseInt(grp, 16))
      );
    } catch (e) {
      return "An error occurred regarding your prompt.";
    }
  }

  static extractCitations = (text: string) => {
    const citationsArray: { label: string; link: string }[] = [];
    const citationsRegex = /\[(\d+)\. (.+?\.pdf#page=\d+)\]/g;
    let match;
    while ((match = citationsRegex.exec(text)) !== null) {
      citationsArray.push({
        label: `${match[1]}. ${match[2]}`,
        link: match[2],
      });
    }
    return citationsArray;
  };

  // Determine if a position is inside a code block
  static isInsideCodeBlock(text: string, position: number): boolean {
    // Check for code blocks (```...```)
    const codeBlockRegex = /```[\s\S]*?```/g;
    
    let match;
    while ((match = codeBlockRegex.exec(text)) !== null) {
      if (position >= match.index && position < match.index + match[0].length) {
        return true;
      }
    }
    
    // Check for inline code (`...`)
    const inlineCodeRegex = /`[^`]+`/g;
    while ((match = inlineCodeRegex.exec(text)) !== null) {
      if (position >= match.index && position < match.index + match[0].length) {
        return true;
      }
    }
    
    // Check for JSX comments
    const jsxCommentRegex = /\{\/\*[\s\S]*?\*\/\}/g;
    while ((match = jsxCommentRegex.exec(text)) !== null) {
      if (position >= match.index && position < match.index + match[0].length) {
        return true;
      }
    }
    
    // Check for JSX map expressions and other JSX expressions
    const jsxExpressionRegex = /\{(?:[\w.]+)(?:\.map\s*\([^)]*\)\s*(?:=>)?\s*\{?[\s\S]*?\}?\}|\{(?:\w+)(?:\.\w+)*\})/g;
    while ((match = jsxExpressionRegex.exec(text)) !== null) {
      if (position >= match.index && position < match.index + match[0].length) {
        return true;
      }
    }
    
    // Check for JSX/HTML-like code
    const jsxRegex = /<[a-zA-Z][a-zA-Z0-9]*(?:\s+[^>]*)?(?:\/?>[\s\S]*?<\/[a-zA-Z][a-zA-Z0-9]*>|\/?>)/g;
    while ((match = jsxRegex.exec(text)) !== null) {
      if (position >= match.index && position < match.index + match[0].length) {
        return true;
      }
    }
    
    // Check for ES6 arrow functions or function declarations
    const functionRegex = /(?:const|let|var)?\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*(?:\([^)]*\)|\w+)\s*=>\s*(?:\{[\s\S]*?\}|[^\n;]+)|function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[\s\S]*?\}/g;
    while ((match = functionRegex.exec(text)) !== null) {
      if (position >= match.index && position < match.index + match[0].length) {
        return true;
      }
    }
    
    return false;
  }

  // Enhanced version of replaceCitationsInText that preserves code blocks
  static replaceCitationsInTextEnhanced = (text: string, messageLength: number): string => {
    let result = '';
    let lastIndex = 0;
    let citationCounter = 0;
    
    // Regex to find citation references [number]
    const citationRegex = /\[(\d+)\]/g;
    
    // First, locate all code blocks and inline code to avoid modifying them
    const codeSegments: {start: number, end: number}[] = [];
    
    // Find code blocks (```...```)
    const codeBlockRegex = /```([^\n]*)\n([\s\S]*?)\n\s*```(?!\S)/gm;
    let match;
    while ((match = codeBlockRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Find inline code (`...`)
    const inlineCodeRegex = /`[^`]+`/g;
    while ((match = inlineCodeRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Find JSX comments
    const jsxCommentRegex = /\{\/\*[\s\S]*?\*\/\}/g;
    while ((match = jsxCommentRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Find JSX expressions with map
    const jsxMapRegex = /\{(?:[\w.]+)\.map\s*\([^)]*\)\s*(?:=>)?\s*\{?[\s\S]*?\}?\}/g;
    while ((match = jsxMapRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Find JSX variable expressions
    const jsxVarRegex = /\{(?:\w+)(?:\.\w+)*\}/g;
    while ((match = jsxVarRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Find JSX/HTML-like code
    const jsxRegex = /<[a-zA-Z][a-zA-Z0-9]*(?:\s+[^>]*)?(?:\/?>[\s\S]*?<\/[a-zA-Z][a-zA-Z0-9]*>|\/?>)/g;
    while ((match = jsxRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Find ES6 arrow functions
    const arrowFuncRegex = /(?:const|let|var)?\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*(?:\([^)]*\)|\w+)\s*=>\s*(?:\{[\s\S]*?\}|[^\n;]+)/g;
    while ((match = arrowFuncRegex.exec(text)) !== null) {
      codeSegments.push({start: match.index, end: match.index + match[0].length});
    }
    
    // Sort segments by start position
    codeSegments.sort((a, b) => a.start - b.start);
    
    // Merge overlapping segments
    for (let i = 0; i < codeSegments.length - 1; i++) {
      if (codeSegments[i].end >= codeSegments[i + 1].start) {
        codeSegments[i].end = Math.max(codeSegments[i].end, codeSegments[i + 1].end);
        codeSegments.splice(i + 1, 1);
        i--; // Recheck current position
      }
    }
    
    // Process citation references
    while ((match = citationRegex.exec(text)) !== null) {
      const matchStart = match.index;
      const matchEnd = match.index + match[0].length;
      
      // Check if this citation is inside a code segment
      const isInCode = codeSegments.some(
        segment => matchStart >= segment.start && matchEnd <= segment.end
      );
      
      if (!isInCode) {
        // Add text before the citation
        result += text.substring(lastIndex, matchStart);
        
        // Add the citation as a superscript link
        const citationNumber = match[1];
        const citationLink = `<a href="#citation-${messageLength}${citationCounter}" class="citation-link text-gallagher-blue-400 text-xs font-bold align-super" data-index="${messageLength}${citationCounter}">[${citationNumber}]</a>`;
        result += citationLink;
        citationCounter++;
      } else {
        // If inside code, leave it unchanged
        result += text.substring(lastIndex, matchEnd);
      }
      
      lastIndex = matchEnd;
    }
    
    // Add the remaining text
    result += text.substring(lastIndex);
    
    // Remove citation section from the text
    const citationsRegex = /Citations:\s*(\[(\d+)\. (.+?\.pdf#page=\d+)\]\s*,?\s*)+/g;
    return result.replace(citationsRegex, "").trim();
  };

  static extractImagesFromText = (text: string): string[] => {
    const imgTags: string[] = [];
    const imgTagRegex = /<img\s+[^>]*src="([^"]+)"[^>]*>/g;
    let matchRegex;
    while ((matchRegex = imgTagRegex.exec(text)) !== null) {
      imgTags.push(matchRegex[1]);
    }
    return imgTags;
  };

  static cleanTextFromImages = (text: string): string => {
    const imgTagRegex = /<img\s+[^>]*src="([^"]+)"[^>]*>/g;
    return text.replace(imgTagRegex, "");
  };

  static parseContent(content: string, messageId: number): {
    parsedText: string;
    citations: string[];
    followUpQuestions: string[];
    pdfName: string[];
  } {
    try {
      if (!content || typeof content !== 'string') {
        return {
          parsedText: '',
          citations: [],
          followUpQuestions: [],
          pdfName: []
        };
      }
      
      // Clean up the input if it starts and ends with quotes
      let processedContent = content.trim();
      if (processedContent.startsWith('"') && processedContent.endsWith('"')) {
        try {
          // Try to unquote the string (this handles the case where the content is a JSON string literal)
          processedContent = JSON.parse(processedContent);
        } catch (e) {
          // If unquoting fails, just use the original
          console.warn("Failed to parse content as JSON string:", e);
        }
      }
  
      // Detectar y manejar patrones específicos de código JSX
      const jsxPatterns = [
        /\{\/\*.*?\*\/\}/gs,                                 // Comentarios JSX {/* ... */}
        /\{notes(?:\.map)?\s*\(.*?\)\s*\}/gs,                // Patrones como {notes.map(...)}
        /\{(?:\w+)(?:\.\w+)*\}/g,                            // Variables JSX simples como {varName} o {obj.prop}
        /<[a-zA-Z][a-zA-Z0-9]*(?:\s+[^>]*)?\/>/g,            // Etiquetas JSX auto-cerradas como <Component />
        /<[a-zA-Z][a-zA-Z0-9]*(?:\s+[^>]*)?>[^<]*<\/[a-zA-Z][a-zA-Z0-9]*>/g, // Etiquetas JSX con contenido
        /\(\s*<.*?>.*?<\/.*?>\s*\)/gs,                       // JSX dentro de paréntesis
        /=>\s*\(\s*<.*?>.*?<\/.*?>\s*\)/gs,                  // Arrow functions que devuelven JSX
        /className="[^"]*"/g                                 // Atributos className
      ];
  
      // Reemplazar temporalmente los patrones JSX con placeholders
      const jsxBlocks: string[] = [];
      let jsxBlockIndex = 0;
      
      // Función para reemplazar patrones JSX
      const replaceJsxWithPlaceholder = (match: string) => {
        jsxBlocks.push(match);
        return `__JSX_BLOCK_${jsxBlockIndex++}__`;
      };
      
      // Aplicar reemplazos para todos los patrones JSX
      for (const pattern of jsxPatterns) {
        processedContent = processedContent.replace(pattern, replaceJsxWithPlaceholder);
      }
      
      // Reemplazar bloques de código
      const codeBlocks: string[] = [];
      let codeBlockIndex = 0;
      const codeBlockPattern = /```[\s\S]*?```/g;
      
      processedContent = processedContent.replace(codeBlockPattern, (match) => {
        codeBlocks.push(match);
        return `__CODE_BLOCK_${codeBlockIndex++}__`;
      });
      
      // Reemplazar código inline
      const inlineCodeBlocks: string[] = [];
      let inlineCodeIndex = 0;
      const inlineCodePattern = /`[^`]+`/g;
      
      processedContent = processedContent.replace(inlineCodePattern, (match) => {
        inlineCodeBlocks.push(match);
        return `__INLINE_CODE_${inlineCodeIndex++}__`;
      });
  
      // Find JSON objects in the content
      const jsonRegex = /\{(?:[^{}]|(?:\{[^{}]*\}))*\}/g;
      const jsonMatches = processedContent.match(jsonRegex) || [];
      
      let parsedText = '';
      let citations: string[] = [];
      let followUpQuestions: string[] = [];
      let foundTextObject = false;
      
      for (const jsonStr of jsonMatches) {
        // Verificar si el jsonStr es un placeholder
        if (jsonStr.includes('__JSX_BLOCK_') || 
            jsonStr.includes('__CODE_BLOCK_') || 
            jsonStr.includes('__INLINE_CODE_')) {
          continue;
        }
        
        try {
          const item = JSON.parse(jsonStr);
          
          if (item.type === 'text' && item.value) {
            // Append to parsedText instead of replacing
            parsedText += item.value;
            foundTextObject = true;
          } else if (item.type === 'citations' && Array.isArray(item.value)) {
            citations = item.value;
          } else if (item.type === 'follow_up_questions' && Array.isArray(item.value)) {
            followUpQuestions = item.value.map((question: string) => question.trim());
          }
        } catch (e) {
          // En caso de error, simplemente ignoramos este objeto JSON
          // y continuamos con el siguiente
        }
      }
      
      // If we didn't find any valid text objects, use the entire content
      if (!foundTextObject) {
        // If no text object was found, extract content before any JSON-like parts
        const contentBeforeJson = processedContent.split('{')[0].trim();
        
        if (contentBeforeJson) {
          parsedText = contentBeforeJson;
        } else {
          // As a last resort, use the entire content
          parsedText = processedContent;
          
          // But make a final attempt to clean it up if it looks like JSON
          if (parsedText.includes('\\u') && parsedText.includes('"type":"text"')) {
            try {
              // Try to extract just the text value from the raw JSON
              const textValueMatch = parsedText.match(/\"value\":\"([^\"]+)\"/);
              if (textValueMatch && textValueMatch[1]) {
                // Replace Unicode escape sequences with actual characters
                parsedText = textValueMatch[1].replace(/\\u([0-9a-fA-F]{4})/g, 
                  (_, grp) => String.fromCharCode(parseInt(grp, 16)));
                
                // Clean up other escape sequences
                parsedText = parsedText.replace(/\\n/g, '\n')
                  .replace(/\\"/g, '"')
                  .replace(/\\\\/g, '\\');
              }
            } catch (e) {
              console.warn("Failed to extract text from raw JSON:", e);
            }
          }
        }
      }
      
      // Restaurar los bloques JSX, de código y código inline
      for (let i = 0; i < jsxBlocks.length; i++) {
        parsedText = parsedText.replace(`__JSX_BLOCK_${i}__`, jsxBlocks[i]);
      }
      
      for (let i = 0; i < codeBlocks.length; i++) {
        parsedText = parsedText.replace(`__CODE_BLOCK_${i}__`, codeBlocks[i]);
      }
      
      for (let i = 0; i < inlineCodeBlocks.length; i++) {
        parsedText = parsedText.replace(`__INLINE_CODE_${i}__`, inlineCodeBlocks[i]);
      }
      
      // Apply citation formatting with code block awareness
      parsedText = this.replaceCitationsInTextEnhanced(parsedText, messageId);
      
      const pdfName = citations.map((citation: string) => citation.replace(/^\d+\.\s*/, ''));
      return { parsedText, citations, followUpQuestions, pdfName };
    } catch (error) {
      console.error("Error parsing content:", error);
      return {
        parsedText: content,
        citations: [],
        followUpQuestions: [],
        pdfName: []
      };
    }
  }
}