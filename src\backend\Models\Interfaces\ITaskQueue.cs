﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Task Queue
    /// </summary>
    public interface ITaskQueue
    {
        /// <summary>
        /// Queues a task to be processed by a worker, assigned round robin style.
        /// </summary>
        ValueTask QueueItemAsync(TaskItem taskItem, DateTime? timestamp = null);

        /// <summary>
        /// Deserializes a task file and returns the task details.
        /// </summary>
        Task<TaskItem> FetchTasksAsync(int workerId, CancellationToken ct);

        /// <summary>
        /// Deletes a task file.
        /// </summary>
        void DeleteTaskFile(string filePath);
    }
}
