import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ManageWorkspaces from '../ManageWorkspaces';
import { Workspace } from '../../../interfaces';
import workspaceReducer from '../../../features/workspaceSlice';

// Mock window.alert
Object.defineProperty(window, 'alert', {
  writable: true,
  value: vi.fn(),
});

// Create mock objects that can be modified in tests
const mockNavigate = vi.fn();
const mockUseFetchWorkspaces = {
  workspaces: [] as Workspace[],
  loading: false,
  error: null as string | null,
  setWorkspaces: vi.fn(),
  refetch: vi.fn(),
};

const mockUseDeleteWorkspace = {
  handleDelete: vi.fn(),
  deleteError: null as string | null,
  deletingWorkspaceId: null as string | null,
};

const mockUseApiWithAuth = {
  refreshToken: vi.fn(),
};

const mockUseToast = {
  toasts: [] as any[],
  triggerToast: vi.fn(),
  closeToast: vi.fn(),
};

// Mock react-router hooks
vi.mock('react-router', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock external dependencies
vi.mock('../../../hooks/useFetchWorkspaces', () => ({
  default: () => mockUseFetchWorkspaces,
}));

vi.mock('../../../hooks/useDeleteWorkspace', () => ({
  default: () => mockUseDeleteWorkspace,
}));

vi.mock('../../../hooks/useApiWithAuth', () => ({
  default: () => mockUseApiWithAuth,
}));

vi.mock('../../../hooks/useToast', () => ({
  default: () => mockUseToast,
}));

// Mock child components
vi.mock('../../Tooltip/Tooltip', () => ({
  default: ({ children, title }: any) => <div data-testid="tooltip" title={title}>{children}</div>,
}));

vi.mock('../../LoadAnimation/DotsAnimation', () => ({
  default: () => <div data-testid="dots-animation">Loading...</div>,
}));

vi.mock('../../Modal/ToastModal/Toast', () => ({
  default: ({ text, id }: any) => <div data-testid={`toast-${id}`}>{text}</div>,
}));

vi.mock('../../Modal/ToastModal/ConfirmToast', () => ({
  default: ({ text, onCancel, onConfirm }: any) => (
    <div data-testid="confirm-toast">
      <p>{text}</p>
      <button data-testid="cancel-button" onClick={onCancel}>
        Cancel
      </button>
      <button data-testid="confirm-button" onClick={onConfirm}>
        Confirm
      </button>
    </div>
  ),
}));

describe('ManageWorkspaces', () => {
  const mockWorkspaces: Workspace[] = [
    {
      id: '1',
      name: 'Test Workspace 1',
      description: 'Description 1',
      documents: [],
    },
    {
      id: '2',
      name: 'Test Workspace 2',
      description: 'Description 2',
      documents: [],
    },
  ];

  // Mock store for Redux Provider (similar to EditWorkspace.test.tsx)
  const mockStore = configureStore({
    reducer: {
      workspace: workspaceReducer,
    },
    preloadedState: {
      workspace: {
        id: '',
        name: '',
        description: '',
        documents: [],
      },
    },
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <ManageWorkspaces />
        </MemoryRouter>
      </Provider>
    );
  };

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Reset mock objects to default state
    mockUseFetchWorkspaces.workspaces = mockWorkspaces;
    mockUseFetchWorkspaces.loading = false;
    mockUseFetchWorkspaces.error = null;

    mockUseDeleteWorkspace.deleteError = null;
    mockUseDeleteWorkspace.deletingWorkspaceId = null;

    mockUseToast.toasts = [];
  });

  describe('Component Rendering', () => {
    it('should render the main heading', () => {
      renderComponent();
      expect(screen.getByText('Document Workspaces')).toBeInTheDocument();
    });

    it('should render the Quick Start guide link with correct attributes', () => {
      renderComponent();
      const quickStartLink = screen.getByText('Quick Start guide');
      
      expect(quickStartLink).toBeInTheDocument();
      expect(quickStartLink).toHaveAttribute('href', expect.stringContaining('sharepoint.com'));
      expect(quickStartLink).toHaveAttribute('target', '_blank');
      expect(quickStartLink).toHaveClass('text-gallagher-dark-300', 'dark:text-sky-400', 'underline');
    });

    it('should render My Workspaces section', () => {
      renderComponent();
      expect(screen.getByText('My Workspaces:')).toBeInTheDocument();
    });

    it('should render Create new workspace button', () => {
      renderComponent();
      expect(screen.getByText('Create new workspace')).toBeInTheDocument();
    });

    it('should render Close button', () => {
      renderComponent();
      expect(screen.getByText('Close')).toBeInTheDocument();
    });

    it('should apply dark theme classes correctly', () => {
      renderComponent();
      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('dark:bg-zinc-800', 'dark:text-white');
    });
  });

  describe('Workspace List Display', () => {
    it('should display workspaces when available', () => {
      renderComponent();
      
      expect(screen.getByText('Test Workspace 1')).toBeInTheDocument();
      expect(screen.getByText('Description 1')).toBeInTheDocument();
      expect(screen.getByText('Test Workspace 2')).toBeInTheDocument();
      expect(screen.getByText('Description 2')).toBeInTheDocument();
    });

    it('should display "No workspaces found" when no workspaces exist', () => {
      mockUseFetchWorkspaces.workspaces = [];
      renderComponent();
      
      expect(screen.getByText('No workspaces found')).toBeInTheDocument();
    });

    it('should display loading state with dots animation', () => {
      mockUseFetchWorkspaces.loading = true;
      renderComponent();
      
      expect(screen.getByText('Loading')).toBeInTheDocument();
      expect(screen.getByTestId('dots-animation')).toBeInTheDocument();
    });

    it('should display error message when there is an error', () => {
      mockUseFetchWorkspaces.error = 'Failed to load workspaces';
      renderComponent();
      
      expect(screen.getByText('Failed to load workspaces')).toBeInTheDocument();
    });
  });

  describe('Workspace Creation', () => {
    it('should navigate to edit-workspace when create button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const createButton = screen.getByText('Create new workspace');
      await user.click(createButton);

      expect(mockNavigate).toHaveBeenCalledWith('/edit-workspace');
    });

    it('should render create button when workspace limit is not reached', () => {
      renderComponent();

      const createButton = screen.getByText('Create new workspace');
      expect(createButton).toBeInTheDocument();
    });

    it('should show tooltip container', () => {
      renderComponent();

      expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    });
  });

  describe('Workspace Editing', () => {
    it('should navigate to edit-workspace with id when workspace is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const workspace = screen.getByText('Test Workspace 1');
      await user.click(workspace);

      expect(mockNavigate).toHaveBeenCalledWith('/edit-workspace?id=1');
    });

    it('should have proper accessibility attributes for edit action', () => {
      renderComponent();

      const editableAreas = screen.getAllByLabelText('Edit Workspace');
      expect(editableAreas[0]).toHaveAttribute('title', 'Edit Workspace');
      expect(editableAreas).toHaveLength(2); // Two workspaces
    });
  });

  describe('Workspace Deletion', () => {
    it('should show confirmation dialog when delete button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();
      
      const deleteButtons = screen.getAllByTitle('Delete Workspace');
      await user.click(deleteButtons[0]);
      
      expect(screen.getByTestId('confirm-toast')).toBeInTheDocument();
      expect(screen.getByText(/Are you sure you want to delete this workspace/)).toBeInTheDocument();
    });

    it('should cancel deletion when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();
      
      // Click delete button to show confirmation
      const deleteButtons = screen.getAllByTitle('Delete Workspace');
      await user.click(deleteButtons[0]);
      
      // Click cancel
      const cancelButton = screen.getByTestId('cancel-button');
      await user.click(cancelButton);
      
      expect(screen.queryByTestId('confirm-toast')).not.toBeInTheDocument();
    });

    it('should call handleDelete when deletion is confirmed', async () => {
      const user = userEvent.setup();
      renderComponent();
      
      // Click delete button to show confirmation
      const deleteButtons = screen.getAllByTitle('Delete Workspace');
      await user.click(deleteButtons[0]);
      
      // Click confirm
      const confirmButton = screen.getByTestId('confirm-button');
      await user.click(confirmButton);
      
      expect(mockUseDeleteWorkspace.handleDelete).toHaveBeenCalledWith('1');
    });

    it('should show "Deleting..." text when deletion is in progress', () => {
      mockUseDeleteWorkspace.deletingWorkspaceId = '1';
      renderComponent();
      
      expect(screen.getByText('Deleting...')).toBeInTheDocument();
    });

    it('should display delete error when deletion fails', () => {
      mockUseDeleteWorkspace.deleteError = 'Failed to delete workspace';
      renderComponent();

      expect(screen.getByText('Failed to delete workspace')).toBeInTheDocument();
    });

    it('should show success toast after successful deletion', async () => {
      const user = userEvent.setup();
      mockUseDeleteWorkspace.handleDelete.mockResolvedValue(undefined);

      renderComponent();

      // Click delete button to show confirmation
      const deleteButtons = screen.getAllByTitle('Delete Workspace');
      await user.click(deleteButtons[0]);

      // Click confirm
      const confirmButton = screen.getByTestId('confirm-button');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockUseToast.triggerToast).toHaveBeenCalledWith({
          text: 'Workspace deleted successfully',
          icon: expect.any(Object),
          duration: 2,
          position: 'top-center',
        });
      });
    });

    it('should show error toast when deletion fails', async () => {
      const user = userEvent.setup();
      mockUseDeleteWorkspace.handleDelete.mockRejectedValue(new Error('Delete failed'));

      renderComponent();

      // Click delete button to show confirmation
      const deleteButtons = screen.getAllByTitle('Delete Workspace');
      await user.click(deleteButtons[0]);

      // Click confirm
      const confirmButton = screen.getByTestId('confirm-button');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockUseToast.triggerToast).toHaveBeenCalledWith({
          text: 'Failed to delete workspace. Please try again.',
          icon: expect.any(Object),
          duration: 3,
          position: 'top-center',
          bgColor: 'bg-red-500',
        });
      });
    });
  });

  describe('Navigation', () => {
    it('should navigate to home when Close button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const closeButton = screen.getByText('Close');
      await user.click(closeButton);

      expect(mockNavigate).toHaveBeenCalledWith('/');
    });

    it('should have proper accessibility attributes for navigation buttons', () => {
      renderComponent();

      const closeButton = screen.getByLabelText('Back to Chats');
      expect(closeButton).toHaveAttribute('title', 'Back to Chats');

      const createButton = screen.getByLabelText('Create New Workspace');
      expect(createButton).toHaveAttribute('title', 'Create New Workspace');
    });
  });

  describe('Toast Notifications', () => {
    it('should render toasts when they exist', () => {
      mockUseToast.toasts = [
        {
          id: 1,
          text: 'Test toast',
          position: 'top-center',
          duration: 3,
          onClose: vi.fn(),
        },
      ];

      renderComponent();

      expect(screen.getByTestId('toast-1')).toBeInTheDocument();
      expect(screen.getByText('Test toast')).toBeInTheDocument();
    });

    it('should handle toast interactions', () => {
      mockUseToast.toasts = [
        {
          id: 1,
          text: 'Test toast',
          position: 'top-center',
          duration: 3,
          onClose: vi.fn(),
        },
      ];

      renderComponent();

      const toast = screen.getByTestId('toast-1');
      expect(toast).toBeInTheDocument();
      // Note: Toast click behavior is handled by the actual Toast component
    });
  });

  describe('Authentication Integration', () => {
    it('should call refreshToken on component mount', () => {
      renderComponent();

      expect(mockUseApiWithAuth.refreshToken).toHaveBeenCalled();
    });

    it('should handle authentication errors gracefully', async () => {
      mockUseApiWithAuth.refreshToken.mockRejectedValue(new Error('Auth failed'));

      // Should not throw error during render
      expect(() => renderComponent()).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for interactive elements', () => {
      renderComponent();

      const createButton = screen.getByLabelText('Create New Workspace');
      expect(createButton).toBeInTheDocument();

      const closeButton = screen.getByLabelText('Back to Chats');
      expect(closeButton).toBeInTheDocument();

      const editButtons = screen.getAllByLabelText('Edit Workspace');
      expect(editButtons).toHaveLength(2);
    });

    it('should handle keyboard navigation properly', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Tab through interactive elements - first tab goes to the Quick Start link
      await user.tab();
      expect(screen.getByText('Quick Start guide')).toHaveFocus();

      // Second tab goes to create button
      await user.tab();
      expect(screen.getByLabelText('Create New Workspace')).toHaveFocus();
    });

    it('should prevent event propagation on delete button click', async () => {
      const user = userEvent.setup();
      renderComponent();

      const deleteButtons = screen.getAllByTitle('Delete Workspace');

      // Mock stopPropagation to verify it's called
      const mockStopPropagation = vi.fn();
      deleteButtons[0].addEventListener('click', (e) => {
        e.stopPropagation = mockStopPropagation;
      });

      await user.click(deleteButtons[0]);

      // Should show confirmation dialog without navigating to edit
      expect(screen.getByTestId('confirm-toast')).toBeInTheDocument();
      expect(mockNavigate).not.toHaveBeenCalledWith('/edit-workspace?id=1');
    });
  });

  describe('Theme Support', () => {
    it('should apply light theme classes by default', () => {
      renderComponent();

      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('bg-white');
    });

    it('should support dark theme classes', () => {
      renderComponent();

      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('dark:bg-zinc-800', 'dark:text-white');

      const createButtonText = screen.getByText('Create new workspace');
      expect(createButtonText).toHaveClass('dark:text-gray-300');
    });

    it('should apply hover states correctly', () => {
      renderComponent();

      const createButtonText = screen.getByText('Create new workspace');
      expect(createButtonText).toHaveClass('group-hover:text-white', 'dark:group-hover:text-white');

      const closeButton = screen.getByText('Close');
      expect(closeButton).toHaveClass('hover:bg-gallagher-dark-300', 'dark:hover:bg-gallagher-dark-300');
    });
  });

  describe('Component Lifecycle', () => {
    it('should cleanup properly on unmount', () => {
      const { unmount } = renderComponent();

      // Component should mount without errors
      expect(screen.getByText('Document Workspaces')).toBeInTheDocument();

      // Should unmount without errors
      unmount();
    });

    it('should refetch workspaces after successful deletion', async () => {
      const user = userEvent.setup();
      mockUseDeleteWorkspace.handleDelete.mockResolvedValue(undefined);

      renderComponent();

      // Click delete button to show confirmation
      const deleteButtons = screen.getAllByTitle('Delete Workspace');
      await user.click(deleteButtons[0]);

      // Click confirm
      const confirmButton = screen.getByTestId('confirm-button');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockUseFetchWorkspaces.refetch).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle workspace fetch errors gracefully', () => {
      mockUseFetchWorkspaces.error = 'Network error';
      mockUseFetchWorkspaces.workspaces = [];

      renderComponent();

      expect(screen.getByText('Network error')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toHaveClass('text-red-500');
    });

    it('should handle empty workspace list gracefully', () => {
      mockUseFetchWorkspaces.workspaces = [];
      mockUseFetchWorkspaces.error = null;

      renderComponent();

      expect(screen.getByText('No workspaces found')).toBeInTheDocument();
    });
  });
});
