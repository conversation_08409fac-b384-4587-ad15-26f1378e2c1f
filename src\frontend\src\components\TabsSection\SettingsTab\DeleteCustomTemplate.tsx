import React, { useState } from "react";
import { deleteCustomTemplate, deleteSelectedCustomTemplate, getCustomTemplate, getSelectedCustomTemplate, getUserSetting, saveSelectedCustomTemplate, saveUserSetting } from "../../../db/settingDB";
import { useNavigate } from "react-router";
import { CheckmarkCircle24Filled, Delete24Filled } from "@fluentui/react-icons";
import useToast from "../../../hooks/useToast";
import Toast from "../../Modal/ToastModal/Toast";
import { useEffect } from "react";
import { MergedTemplateListPropTypes } from "../../../types/dbTypes";
import { SYSTEM_TEMPLATE } from "../../../constants/SettingsTabConstants";
import { Stores } from "../../../constants/dbConstants";

const DeleteCustomTemplate: React.FC = () => {
  const navigate = useNavigate();
  const [customTemplate, setCustomTemplate] = useState<MergedTemplateListPropTypes[]>([]);
  const { toasts, triggerToast, closeToast } = useToast();

  useEffect(()=>{
    const fetchTemplates = async () => {
      const customTemplate = (await getCustomTemplate()).sort((a, b) => a.label.localeCompare(b.label));
      setCustomTemplate(customTemplate);
    };
    fetchTemplates();
  }, [])
    
  
    const handleDelete = async (id:string) => {
      const updatedTemplates = customTemplate.filter((template) => template.id !== id);
      setCustomTemplate(updatedTemplates);
      await deleteCustomTemplate(id); 
      const selectedCustomTemplate = await getSelectedCustomTemplate()
      if (selectedCustomTemplate && selectedCustomTemplate.id === id) {
        await deleteSelectedCustomTemplate();
        await saveSelectedCustomTemplate(SYSTEM_TEMPLATE[0]);
        const userSetting = await getUserSetting(Stores.UserSettings);
        if (userSetting) {
          userSetting.template = "";
          await saveUserSetting(userSetting)
        }
      }
      triggerToast({
          text: "An assistant instructions template was Deleted",
          icon: <CheckmarkCircle24Filled />,
          duration: 3,
          position: "bottom-center",
      });
    };
    return (
        <div className="relative flex flex-col p-8 items-center h-full w-full bg-white dark:bg-zinc-800">
            <div className="flex flex-col h-full p-4 mb-6 space-y-4 overflow-y-auto w-full max-w-(--breakpoint-lg) md:w-1/2 xl:w-1/3 pb-[56px]">
                <h2 className="text-xl font-semibold text-left dark:text-white">
                    Delete Assistant Instructions Setting
                </h2>
                {customTemplate && customTemplate.length > 0 ? (
                    <ul className="w-full mt-2 p-2 border-2 rounded dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300">
                        {customTemplate.map((template) => (
                            <li key={template.id} className="flex justify-between items-center p-2 border-b gap-8 last:border-b-0">
                                <span className="whitespace-normal break-words lg:w-[85%] w-[70%]">{template.label}</span>
                                <button
                                  className="bg-red-500 text-white px-2 py-1 rounded cursor-pointer"
                                  onClick={() => handleDelete(template.id)}
                                >
                                  <Delete24Filled className=" dark:text-black" />
                                </button>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <div className="flex justify-center items-center p-2 border-b">
                        <span className="text-gray-500">No custom templates available</span>
                    </div>
                )}
                <div className="flex space-x-4 w-full">
                    <button 
                        onClick={() => navigate("/settings")}
                        className="w-full cursor-pointer px-4 py-2 border-2 rounded-sm dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 hover:text-white dark:hover:text-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300">
                        Back
                    </button>
                    
                </div>
            </div>
            {toasts.map((toast) => (
                <Toast
                    key={toast.id}
                    id={toast.id}
                    position={toast.position}
                    text={toast.text}
                    icon={toast.icon}
                    duration={toast.duration}
                    onClose={() => closeToast(toast.id)}
                    bgColor={toast.bgColor}
                />
            ))}
        </div>
    );
};

export default DeleteCustomTemplate;


