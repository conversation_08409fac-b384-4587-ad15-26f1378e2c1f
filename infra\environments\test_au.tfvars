environment                 = "test"
region                      = "au"
azurerm_resource_group_name = "corp-gallagherai-au-main-rg"

rbac_devadmin = ["07691ecb-a8e5-4036-b1ae-af3d13b48463",     #u-Corp-Architects
"e4c13c82-2c7f-481d-ad69-daa108781f8f"]                      #u-CorpOD_CORP_SmartMarket_Developers (<PERSON>, <PERSON>)]
rbac_contributors = ["6a5bd903-1b3e-4e71-8dce-8897e58ffb9e"] #corp-openai-test-gallaghergpt-cicd (Az Devops CICD Service Principal)

appserviceplan_settings = {
  sku_name               = "P0v3"
  os_type                = "Linux"
  zone_balancing_enabled = false
}

webapp_settings = {
  application_insights_retention_in_days = 30
}

docintelligence_settings = {
  sku_name                   = "F0"
  kind                       = "FormRecognizer"
  dynamic_throttling_enabled = false
}

search_settings = {
  sku_name      = "basic"
  semantic_sku  = "free" # specified only when "sku_name" is not set to "free"
  replica_count = 1
}

doctranslate_settings = {
  sku_name = "F0"
  kind     = "TextTranslation"
}

websearch_settings = {
  sku_name = "F0"
}

nw_vnet_name               = "corpau-test-pvnet"
nw_rg_name                 = "network-rg"
nw_subnet_privatelink_name = "pe"                      # ************/26
nw_subnet_webapp_name      = "gallagherai-integration" # *************/28