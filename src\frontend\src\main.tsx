import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import AppRouter from "./router";
import { Provider } from "react-redux";
import store from "./store";
import "highlight.js/styles/atom-one-light.min.css";

import { Msal<PERSON>rovider } from "@azure/msal-react";
import { PublicClientApplication } from "@azure/msal-browser";
import { msalConfig } from "./authConfig";
import DisclaimerModal from "./components/Modal/DisclaimerModal/DisclaimerModal";
import { AppInsightsContext } from "@microsoft/applicationinsights-react-js";
import { reactPlugin } from "./services/appInsightService";
import useThemeSwitcher from "./hooks/useThemeSwitcher";

// Initialize MSAL
const pca = new PublicClientApplication(msalConfig);
await pca.initialize();

// Create the root only once
const rootElement = document.getElementById("root");
// Ensure the root element exists
if (!rootElement) {
  throw new Error("Root element not found");
}
// Create the root once
const root = ReactDOM.createRoot(rootElement);

const App = () => {
  useThemeSwitcher(); // Dark mode hook

  return (
    <MsalProvider instance={pca}>
      <Provider store={store}>
        <AppInsightsContext.Provider value={reactPlugin}>
          <AppRouter />
          <DisclaimerModal />
        </AppInsightsContext.Provider>
      </Provider>
    </MsalProvider>
  );
};

// Render to the existing root
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);