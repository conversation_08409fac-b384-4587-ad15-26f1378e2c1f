﻿namespace Backend.Extensions
{
    internal static class StringExtensions
    {
        /// <summary>
        /// Count the number of times a character appears in a string.
        /// </summary>
        /// <param name="s"></param>
        /// <param name="c"></param>
        /// <returns></returns>
        internal static int CountChar(this string s, char c)
        {
            int count = 0;
            foreach (var v in s.AsSpan())
            {
                if (v == c)
                    count++;
            }
            return count;
        }

        /// <summary>
        /// Trims and sanitizes a string by removing newlines, carriage returns, and tabs.
        /// </summary>
        /// <param name="s"></param>
        /// <returns></returns>
        internal static string Sanitize(this string? s)
        {
            return s?.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "") ?? "";
        }
    }
}
