﻿#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0001

using Moq;
using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Backend.Models;
using Backend.Models.Context;
using Backend.Services;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using System.Reflection;

namespace BackendUnitTests
{
    public class SearchTests
    {
        private readonly Dictionary<string, SearchClient> _mockSearchClients;
        private readonly Mock<ITextEmbeddingGenerationService> _mockTextEmbeddingService;
        private readonly Mock<ILogger<SearchService>> _mockLogger;
        private readonly SearchService _searchService;

        public SearchTests()
        {
            _mockSearchClients = new Dictionary<string, SearchClient>
            {
                { "Default", new Mock<SearchClient>().Object }
            };
            _mockTextEmbeddingService = new Mock<ITextEmbeddingGenerationService>();
            _mockLogger = new Mock<ILogger<SearchService>>();

            SetupMockTextEmbeddingService();

            CurrentContext.User = new User
            {
                OID = "test-oid",
                DisplayName = "test-oid",
                Images = false,
                ChatCount = 0,
                PDL = "NAM"
            };

            _searchService = new SearchService(
                _mockSearchClients,
                _mockTextEmbeddingService.Object,
                _mockLogger.Object,
                3);
        }

        private void SetupMockTextEmbeddingService()
        {
            _mockTextEmbeddingService
                .Setup(s => s.GenerateEmbeddingsAsync(
                    It.IsAny<IList<string>>(),
                    It.IsAny<Kernel>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ReadOnlyMemory<float>> { new float[] { 0.1f, 0.2f, 0.3f } });
        }

        private object InvokePrivateMethod(string methodName, object[] parameters, bool isStatic = false)
        {
            var method = typeof(SearchService).GetMethod(methodName, BindingFlags.NonPublic | (isStatic ? BindingFlags.Static : BindingFlags.Instance));
            return method!.Invoke(isStatic ? null : _searchService, parameters)!;
        }

        [Theory]
        [InlineData("test-oid", "test-workspace", null!, "oids/any(oid: oid eq 'test-oid') and workspace eq 'test-workspace'")]
        [InlineData("test-oid", "test-workspace", "test-file", "oids/any(oid: oid eq 'test-oid') and workspace eq 'test-workspace' and sourcefile eq 'test-file'")]
        public void CreateFilter_ReturnsExpectedFilter(string oid, string workspace, string file, string expected)
        {
            // Act
            var result = InvokePrivateMethod("CreateFilter", new object[] { oid, workspace, file }, true);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void ExtractPageNumber_ReturnsPageNumber()
        {
            // Act
            var result = InvokePrivateMethod("ExtractPageNumber", new object[] { "page=5" }, true);

            // Assert
            Assert.Equal(5, result);
        }

        [Fact]
        public void ConvertHtmlTablesToMarkdown_ConvertsCorrectly()
        {
            // Act
            var result = InvokePrivateMethod("ConvertHtmlTablesToMarkdown", new object[] { "<table><tr><td>Test</td></tr></table>" });

            // Assert
            Assert.Contains("| Test |", result!.ToString());
        }

        [Fact]
        public void GetExistingIds_ReturnsCorrect()
        {
            // Arrange
            var sections = new List<object>
            {
                new { Id = "1", Page = "page1", Sequence = 1, File = "file1", Content = "Test content 1" },
                new { Id = "2", Page = "page2", Sequence = 2, File = "file2", Content = "Test content 2" }
            };

            // Act
            var result = InvokePrivateMethod("GetExistingIds", new object[] { sections }, true);

            // Assert
            Assert.NotNull(result);
            var ids = result as HashSet<string>;
            Assert.Contains("1", ids!);
            Assert.Contains("2", ids!);
        }

        [Fact]
        public void CreateSection_ReturnsCorrectSection()
        {
            // Arrange
            var doc = SearchModelFactory.SearchResult(new SearchDocument
            {
                ["id"] = "1",
                ["content"] = "Test content",
                ["sourcepage"] = "page1",
                ["sourcefile"] = "file1"
            }, 0.6, null);
            var settings = new SearchSettings
            {
                UseSemanticCaptions = false
            };
            var existingIds = new HashSet<string>();

            // Act
            var result = InvokePrivateMethod("CreateSection", new object[] { doc, settings, existingIds });

            // Assert
            Assert.NotNull(result);
            Assert.Equal("1", result.GetType().GetProperty("Id")?.GetValue(result));
            Assert.Equal("page1", result.GetType().GetProperty("Page")?.GetValue(result));
            Assert.Equal("file1", result.GetType().GetProperty("File")?.GetValue(result));
            Assert.Equal("Test content", result.GetType().GetProperty("Content")?.GetValue(result));
            Assert.Equal(-1, result.GetType().GetProperty("Sequence")?.GetValue(result));
        }

        [Fact]
        public async Task QueryDocumentsAsync_ReturnsContentItems_WhenQueryIsProvided()
        {
            // Arrange
            var workspace = "test-workspace";
            var page = "file1#page=2";
            var content = "This is a test content.";
            var searchSettings = new SearchSettings
            {
                Top = 50,
                MinimumSearchScore = 0.2f,
                MinimumRerankerScore = 1.9f,
                UseSemanticRanker = false,
                UseSemanticCaptions = false,
                RetrievalMode = RetrievalMode.Hybrid
            };

            var searchDocuments = new List<SearchDocument>
            {
                new SearchDocument
                {
                    ["id"] = "1",
                    ["workspace"] = workspace,
                    ["sourcefile"] = "file1",
                    ["sourcepage"] = page,
                    ["content"] = content
                }
            };

            var searchResultItems = searchDocuments.Select(doc => CreateSearchResult(doc, 0.6, 2.0));
            var mockSearchResults = SearchModelFactory.SearchResults(searchResultItems, 77, null, null, null, null);

            var mockResponse = new Mock<Response<SearchResults<SearchDocument>>>();
            mockResponse.Setup(r => r.Value).Returns(mockSearchResults);

            var mockSearchClient = Mock.Get(_mockSearchClients["Default"]);

            mockSearchClient.Setup(client =>
                client.SearchAsync<SearchDocument>(
                    It.IsAny<string>(), It.IsAny<SearchOptions>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(mockResponse.Object);

            // Act
            var result = await _searchService.QueryDocumentsAsync(workspace, searchSettings, "test query");

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(page, result[0].Title);
            Assert.Equal(content, result[0].Content);
        }

        [Fact]
        public async Task QueryDocumentsAsync_BulkTest()
        {
            // Arrange
            var workspace = "test-workspace";
            var searchSettings = new SearchSettings
            {
                Top = 50,
                MinimumSearchScore = 0.2f,
                MinimumRerankerScore = 1.9f,
                UseSemanticRanker = true,
                UseSemanticCaptions = false,
                RetrievalMode = RetrievalMode.Hybrid
            };

            var searchDocuments = GenerateSearchDocuments(workspace, 50);
            var searchResultItems = GenerateSearchResultItems(searchDocuments, 50, 5);
            var mockSearchResults1 = SearchModelFactory.SearchResults(searchResultItems, 167, null, null, null, null);
            var mockResponse1 = new Mock<Response<SearchResults<SearchDocument>>>();
            mockResponse1.Setup(r => r.Value).Returns(mockSearchResults1);

            var searchDocuments2 = GenerateSearchDocuments(workspace, 50, 50);
            var searchResultItems2 = GenerateSearchResultItems(searchDocuments2, 20, 5);
            var mockSearchResults2 = SearchModelFactory.SearchResults(searchResultItems2, 167, null, null, null, null);
            var mockResponse2 = new Mock<Response<SearchResults<SearchDocument>>>();
            mockResponse2.Setup(r => r.Value).Returns(mockSearchResults2);

            var mockSearchClient = Mock.Get(_mockSearchClients["Default"]);

            mockSearchClient.SetupSequence(client =>
                client.SearchAsync<SearchDocument>(
                    It.IsAny<string>(), It.IsAny<SearchOptions>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse1.Object)
                .ReturnsAsync(mockResponse2.Object);

            // Act
            var result = await _searchService.QueryDocumentsAsync(workspace, searchSettings, "test query");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Length >= 5, "Expected at least 5 results with reranker score >= 2.0");
            Assert.True(result.Length < 50, "Expected less than 50 results with reranker score >= 2.0");
        }

        [Fact]
        public void GetModifier_ShouldReturnCorrectModifier()
        {
            // Act
            float result = (float)InvokePrivateMethod("GetModifier", new object[] { 8f, 100f }, true);

            // Assert
            Assert.True(Math.Abs(result - 0.04f) < 0.0001f, $"Expected 0.04 but got {result}");
        }

        [Fact]
        public void BuildAdjacenciesFilter_ShouldBuildCorrectFilter()
        {
            // Arrange
            var sections = new List<object>
            {
                new { Id = "1", Page = "page=1", Sequence = -1, File = "file1" },
                new { Id = "2", Page = "page=2", Sequence = 1, File = "file2" }
            };
            string initialFilter = "oids/any(oid: oid eq 'user_oid') and workspace eq 'workspace1'";

            // Act
            var result = InvokePrivateMethod("BuildAdjacenciesFilter", new object[] { sections, initialFilter }, true) as string;

            // Assert
            var expectedFilter = "oids/any(oid: oid eq 'user_oid') and workspace eq 'workspace1' and (" +
                                 "sourcepage eq 'page=1' or sourcepage eq 'page=2' or sourcepage eq 'page=3' or " +
                                 "(sourcefile eq 'file2' and sequence eq 0) or (sourcefile eq 'file2' and sequence eq 2))";
            Assert.Equal(expectedFilter, result);
        }

        [Fact]
        public void BuildAdjacenciesFilter_WorksForNoSections()
        {
            // Arrange
            var sections = new List<object>();
            string initialFilter = "oids/any(oid: oid eq 'user_oid') and workspace eq 'workspace1'";

            // Act
            var result = InvokePrivateMethod("BuildAdjacenciesFilter", new object[] { sections, initialFilter }, true) as string;

            // Assert
            var expectedFilter = "oids/any(oid: oid eq 'user_oid') and workspace eq 'workspace1'";
            Assert.Equal(expectedFilter, result);
        }

        [Fact]
        public async Task AddToSearchAsync_IndexesSuccessfully()
        {
            // Arrange
            var sections = new List<Section>
            {
                new Section
                {
                    Id = "1_1",
                    Content = "Test content 1",
                    Workspace = "Workspace1",
                    SourcePage = "Page1",
                    SourceFile = "File1"
                },
                new Section
                {
                    Id = "2_2",
                    Content = "Test content 2",
                    Workspace = "Workspace2",
                    SourcePage = "Page2",
                    SourceFile = "File2"
                }
            };

            var mockIndexDocumentsResult = SearchModelFactory.IndexDocumentsResult(
                new List<IndexingResult>
                {
                    SearchModelFactory.IndexingResult("1_1", It.IsAny<string>(), true, 200),
                    SearchModelFactory.IndexingResult("2_2", It.IsAny<string>(), true, 200)
                });

            var mockIndexDocumentsResponse = new Mock<Response<IndexDocumentsResult>>();
            mockIndexDocumentsResponse.Setup(r => r.Value).Returns(mockIndexDocumentsResult);

            var mockSearchClient = Mock.Get(_mockSearchClients["Default"]);
            mockSearchClient
                .Setup(client =>
                    client.IndexDocumentsAsync(It.IsAny<IndexDocumentsBatch<SearchDocument>>(),
                                               It.IsAny<IndexDocumentsOptions>(),
                                               It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockIndexDocumentsResponse.Object);

            // Act
            await _searchService.AddToSearchAsync(sections);

            // Assert
            mockSearchClient.Verify(client =>
                client.IndexDocumentsAsync(It.IsAny<IndexDocumentsBatch<SearchDocument>>(),
                                           It.IsAny<IndexDocumentsOptions>(),
                                           It.IsAny<CancellationToken>()),
                                           Times.AtLeastOnce);
        }

        /******************************************
         * Helper functions to create mock objects
         ******************************************/

        private static SearchResult<SearchDocument> CreateSearchResult(
            SearchDocument document,
            double? score,
            double? rerankerScore)
        {
            var result = SearchModelFactory.SearchResult(document, score, null!);
            var semanticSearchResult = new SemanticSearchResult();

            // Use reflection to set the internal RerankerScore property
            var rerankerScoreProperty = typeof(SemanticSearchResult)
                .GetProperty("RerankerScore", BindingFlags.Public | BindingFlags.Instance);
            rerankerScoreProperty!.SetValue(semanticSearchResult, rerankerScore);

            // Use reflection to set the internal SemanticSearch property
            var semanticSearchProperty = typeof(SearchResult<SearchDocument>)
                .GetProperty("SemanticSearch", BindingFlags.Public | BindingFlags.Instance);
            semanticSearchProperty!.SetValue(result, semanticSearchResult);

            return result;
        }

        private static List<SearchDocument> GenerateSearchDocuments(string workspace, int count, int startIndex = 0)
        {
            var searchDocuments = new List<SearchDocument>();
            for (int i = startIndex; i < startIndex + count; i++)
            {
                searchDocuments.Add(new SearchDocument
                {
                    ["id"] = i.ToString(),
                    ["workspace"] = workspace,
                    ["sourcefile"] = $"file{i}",
                    ["sourcepage"] = $"file{i}#page={i + 1}",
                    ["content"] = $"This is a test content {i}."
                });
            }
            return searchDocuments;
        }

        private static List<SearchResult<SearchDocument>> GenerateSearchResultItems(
            List<SearchDocument> searchDocuments, int count, int highScoreCount)
        {
            var random = new Random();
            var searchResultItems = new List<SearchResult<SearchDocument>>();

            for (int i = 0; i < count; i++)
            {
                double score = random.NextDouble() * (12.0 - 0.2) + 0.2;
                double rerankerScore = i < highScoreCount ? random.NextDouble() * (4.0 - 2.0) + 2.0 : random.NextDouble() * 2.0;
                searchResultItems.Add(CreateSearchResult(searchDocuments[i], score, rerankerScore));
            }

            return searchResultItems;
        }
    }
}
