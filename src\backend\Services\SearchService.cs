﻿#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0001

using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Backend.Models;
using Microsoft.SemanticKernel.Embeddings;
using Backend.Models.Context;
using System.Text;
using ReverseMarkdown;
using System.Text.RegularExpressions;

namespace Backend.Services
{
    public class SearchService : ISearchService
    {
        private readonly Dictionary<string, SearchClient> _searchClients;
        private readonly ITextEmbeddingGenerationService _textEmbeddingService;
        private readonly ILogger<SearchService> _logger;
        private readonly Converter _converter;
        private readonly int _top;

        public SearchService(Dictionary<string, SearchClient> searchClients,
                             ITextEmbeddingGenerationService textEmbeddingService,
                             ILogger<SearchService> logger,
                             int top)
        {
            _searchClients = searchClients;
            _textEmbeddingService = textEmbeddingService;
            _logger = logger;
            _top = top;
            _converter = new Converter();
        }

        /// <summary>
        /// Gets the search client based on the provided value.
        /// </summary>
        public SearchClient GetSearchClient(string val) =>
            _searchClients.TryGetValue(val, out var client) ? client : _searchClients["Default"];

        /// <summary>
        /// Adds sections to the search index.
        /// </summary>
        public async Task AddToSearchAsync(IEnumerable<Section> sections, string PDL = "Default")
        {
            var iteration = 0;
            var batch = new IndexDocumentsBatch<SearchDocument>();

            foreach (var section in sections)
            {
                var embedding = (await _textEmbeddingService.GenerateEmbeddingsAsync(
                    new List<string> { section.Content.Replace('\r', ' ') }, cancellationToken: CancellationToken.None))
                        .ToArray().Select(e => e.ToArray()).SelectMany(e => e).ToArray();

                batch.Actions.Add(new IndexDocumentsAction<SearchDocument>(
                    IndexActionType.MergeOrUpload,
                    new SearchDocument
                    {
                        ["id"] = section.Id,
                        ["content"] = section.Content,
                        ["workspace"] = section.Workspace,
                        ["sourcepage"] = section.SourcePage,
                        ["sourcefile"] = section.SourceFile,
                        ["embedding"] = embedding,
                        ["oids"] = new[] { section.Id.Split('_')[0] },
                        ["groups"] = new[] { section.Id.Split('_')[1] },
                        ["sequence"] = section.Sequence
                    }));

                iteration++;
                if (iteration % 1_000 is 0)
                {
                    // Every one thousand documents, batch create.
                    IndexDocumentsResult result = await GetSearchClient(PDL).IndexDocumentsAsync(batch);
                    int succeeded = result.Results.Count(r => r.Succeeded);
                    if (_logger.IsEnabled(LogLevel.Information) is true)
                    {
                        _logger.LogInformation("Indexed {Count} sections, {Succeeded} succeeded",
                            batch.Actions.Count, succeeded);
                    }

                    batch = new IndexDocumentsBatch<SearchDocument>();
                }
            }

            if (batch is { Actions.Count: > 0 })
            {
                // Any remaining documents, batch create.
                IndexDocumentsResult result = await GetSearchClient(PDL).IndexDocumentsAsync(batch);
                int succeeded = result.Results.Count(r => r.Succeeded);
                if (_logger.IsEnabled(LogLevel.Information))
                {
                    _logger.LogInformation("Indexed {Count} sections, {Succeeded} succeeded",
                        batch.Actions.Count, succeeded);
                }
            }
        }

        /// <summary>
        /// Queries the search index for information.
        /// </summary>
        public async Task<SectionItem[]> QueryDocumentsAsync(string workspace,
                                                             SearchSettings settings,
                                                             string? textQuery = null,
                                                             float[]? vector = null,
                                                             CancellationToken ct = default)
        {
            // Validate and prepare the query parameters
            ValidateQueryParameters(textQuery, vector);
            var filter = CreateFilter(CurrentContext.User.OID!, workspace);
            var options = CreateSearchOptions(settings, filter, 50, vector);

            // Search
            var searchResults = await FindResults(
                await ExecuteSearchAsync(textQuery!, options, ct),
                    workspace, settings, textQuery, vector, ct);

            // Collect only the top results
            var sections = new List<object>();
            foreach (var searchResult in searchResults)
            {
                sections = CollectResults(searchResult, settings, false, sections);
            }

            if (sections.Count > 0)
            {
                // Find adjacent items to the top results
                filter = BuildAdjacenciesFilter(sections, filter);
                options = CreateSearchOptions(settings, filter.ToString(), 50, vector);

                sections = CollectResults(
                    await ExecuteSearchAsync("*", options, ct),
                        settings, true, sections);

                // Sort 
                sections = sections
                    .OrderBy(s => s.GetType().GetProperty("Sequence")?.GetValue(s) is int seq && seq == -1 ? 0 : 1)
                    .ThenBy(s => ExtractPageNumber(s.GetType().GetProperty("Page")?.GetValue(s)?.ToString()))
                    .ThenBy(s => s.GetType().GetProperty("Page")?.GetValue(s)?.ToString())
                    .ThenBy(s => s.GetType().GetProperty("Sequence")?.GetValue(s) is int seq ? seq : int.MaxValue)
                    .ToList();
            }

            Console.WriteLine(sections.Count());

            return sections.Select(s => new SectionItem
            {
                Title = s.GetType().GetProperty("Page")?.GetValue(s) as string ?? string.Empty,
                Content = s.GetType().GetProperty("Content")?.GetValue(s) as string ?? string.Empty
            }).ToArray();
        }

        /// <summary>
        /// Searches for results. The reranker in Azure AI Search will only score the top 50 results. 
        /// As so, this method will re-query for more scores, when there are many good results (>45).
        /// </summary>
        private async Task<List<SearchResults<SearchDocument>>> 
            FindResults(SearchResults<SearchDocument> searchResult,
                        string workspace,
                        SearchSettings settings,
                        string? textQuery,
                        float[]? vector,
                        CancellationToken ct)
        {
            var searchResults = new List<SearchResults<SearchDocument>>();
            int count = CountValidResults(searchResult, settings);
            SearchOptions options;
            SearchResults<SearchDocument> fileSearchResult;

            if (count > 48)
            {
                foreach (var file in searchResult.Facets["sourcefile"])
                {
                    var fileName = file.Value.ToString();
                    if (fileName == null) continue;

                    var fileFilter = CreateFilter(CurrentContext.User.OID!, workspace, fileName);
                    if (searchResult.Facets["sourcefile"].Count > 1)
                    {
                        options = CreateSearchOptions(settings, fileFilter, 50, vector);
                        fileSearchResult = await ExecuteSearchAsync(textQuery!, options, ct);
                        count = CountValidResults(fileSearchResult, settings);
                    }
                    else
                    {
                        fileSearchResult = searchResult;
                    }

                    if (count > 48)
                    {
                        options = CreateSearchOptions(settings, fileFilter, 1000, vector);
                        var requeryResult = await ExecuteSearchAsync("*", options, ct);
                        searchResults.Add(requeryResult);
                    }
                    else
                    {
                        searchResults.Add(fileSearchResult);
                    }
                }
            }
            else
            {
                searchResults.Add(searchResult);
            }

            return searchResults;
        }

        /// <summary>
        /// Executes a search asynchronously.
        /// </summary>
        private async Task<SearchResults<SearchDocument>> ExecuteSearchAsync(string query,
                                                                             SearchOptions options,
                                                                             CancellationToken ct)
        {
            ct.ThrowIfCancellationRequested();

            var searchResultResponse = 
                await GetSearchClient(CurrentContext.User.PDL!)
                    .SearchAsync<SearchDocument>(query, options, ct);

            if (searchResultResponse.Value is null)
            {
                throw new InvalidOperationException("Failed to get search result.");
            }

            return searchResultResponse.Value;
        }

        /// <summary>
        /// Filters the search results, collecting only the best results.
        /// </summary>
        private List<object> CollectResults(SearchResults<SearchDocument> search,
                                            SearchSettings settings,
                                            bool fetchAll = false,
                                            List<object> sections = null!)
        {
            if (sections == null) { sections = new List<object>(); }
            var ids = GetExistingIds(sections);

            var searchCount = search.TotalCount;
            foreach (var doc in search.GetResults())
            {
                if (IsValidResult(doc, settings, GetModifier(ids.Count(), (float)search.TotalCount!)) || fetchAll)
                {
                    var section = CreateSection(doc, settings, ids);
                    if (section != null)
                    {
                        sections.Add(section);
                        ids.Add(section.Id);
                    }
                }
            }

            return sections;
        }

        /// <summary>
        /// Creates a section from the search result document.
        /// </summary>
        private dynamic? CreateSection(SearchResult<SearchDocument> doc,
                                       SearchSettings settings,
                                       HashSet<string> existingIds)
        {
            string? id = null;
            string? content = null;
            string? sourcepage = null;
            string? sourcefile = null;
            int sequence = -1;

            try // parse properties
            {
                id = doc.Document.TryGetValue("id", out var idValue) ?
                    idValue as string : null;

                if (id == null || existingIds.Contains(id)) return null;

                sourcepage = doc.Document.TryGetValue("sourcepage", out var sourcePageValue) ?
                    sourcePageValue as string : "";

                sequence = doc.Document.TryGetValue("sequence", out var sequenceValue) &&
                    sequenceValue is int seq ?
                seq : -1;

                sourcefile = doc.Document.TryGetValue("sourcefile", out var sourceFileValue) ?
                    sourceFileValue as string : "";

                content = settings.UseSemanticCaptions
                    ? string.Join(" . ", doc.SemanticSearch.Captions.Select(c => c.Text))
                    : doc.Document.TryGetValue("content", out var contentValue) ?
                        contentValue as string : null;
            }
            catch
            {
                content = null;
            }

            if (content != null)
            {
                content = ConvertHtmlTablesToMarkdown(content);
                return new { Id = id, Page = sourcepage, Sequence = sequence, File = sourcefile, Content = content };
            }

            return null;
        }

        /// <summary>
        /// Converts HTML tables to Markdown format.
        /// </summary>
        private string ConvertHtmlTablesToMarkdown(string content)
        {
            var htmlTablePattern = new Regex("<table>(.*?)</table>", RegexOptions.Singleline);
            var htmlTables = htmlTablePattern.Matches(content);

            foreach (Match table in htmlTables)
            {
                var markdown = _converter.Convert(table.Value);
                content = content.Replace(table.Value, markdown);
            }

            return content;
        }

        /// <summary>
        /// Creates search options based on the search settings.
        /// </summary>
        private static SearchOptions CreateSearchOptions(SearchSettings settings, string filter, int size, float[]? vector)
        {
            var options = new SearchOptions
            {
                Filter = filter,
                Size = size,
                IncludeTotalCount = true,
                Facets = { "sourcefile" },
                QueryType = settings.UseSemanticRanker ? SearchQueryType.Semantic : SearchQueryType.Simple,
                SemanticSearch = settings.UseSemanticRanker ? new SemanticSearchOptions
                {
                    SemanticConfigurationName = "default",
                    QueryCaption = new(settings.UseSemanticCaptions
                            ? QueryCaptionType.Extractive
                            : QueryCaptionType.None),
                } : null,
            };

            if (vector != null && settings!.RetrievalMode != RetrievalMode.Text)
            {
                var vectorQuery = new VectorizedQuery(vector)
                {
                    KNearestNeighborsCount = size,
                };
                vectorQuery.Fields.Add("embedding");
                options.VectorSearch = new VectorSearchOptions();
                options.VectorSearch.Queries.Add(vectorQuery);
            }

            return options;
        }

        /// <summary>
        /// Builds a filter for adjacent pages based on the sections provided.
        /// </summary>
        private static string BuildAdjacenciesFilter(List<object> sections, string filter)
        {
            var result = new StringBuilder(filter);

            if (sections.Count > 0)
            {
                result.Append(" and (");

                foreach (var item in sections)
                {
                    int sequence = item.GetType().GetProperty("Sequence")?.GetValue(item) is int value ? value : -1;
                    var sourcepage = item.GetType().GetProperty("Page")?.GetValue(item, null) as string;
                    var file = item.GetType().GetProperty("File")?.GetValue(item, null) as string;

                    if (sequence == -1)
                    {
                        int page = ExtractPageNumber(sourcepage);
                        page = Math.Max(page - 1, 1);

                        result.Append($"sourcepage eq '{sourcepage![0..sourcepage!.LastIndexOf("=")]}={page}' or ");
                        result.Append($"sourcepage eq '{sourcepage![0..sourcepage.LastIndexOf("=")]}={page + 1}' or ");
                        result.Append($"sourcepage eq '{sourcepage![0..sourcepage.LastIndexOf("=")]}={page + 2}' or ");
                    }
                    else
                    {
                        result.Append($"(sourcefile eq '{file}' and sequence eq {sequence - 1}) or ");
                        result.Append($"(sourcefile eq '{file}' and sequence eq {sequence + 1}) or ");
                    }
                }

                result.Length -= 4;
                result.Append(")");
            }
            
            return result.ToString();
        }

        /// <summary>
        /// Validates query parameters.
        /// </summary>
        private static void ValidateQueryParameters(string? textQuery, float[]? vector)
        {
            if (textQuery is null && vector is null)
            {
                throw new ArgumentException("Either a textual query or vector must be provided");
            }
        }

        /// <summary>
        /// Creates a filter to restrict results to a user and workspace.
        /// </summary>
        private static string CreateFilter(string oid, string? workspace, string? file = null)
        {
            return $"oids/any(oid: oid eq '{oid}')" +
                   (workspace != null ? $" and workspace eq '{workspace}'" : string.Empty) +
                   (file != null ? $" and sourcefile eq '{file}'" : string.Empty);
        }

        /// <summary>
        /// Gets the existing IDs from the sections.
        /// </summary>
        private static HashSet<string> GetExistingIds(List<object> sections) =>
            new HashSet<string>(sections
                .Select(s => s.GetType().GetProperty("Id")?.GetValue(s) as string)
                    .Where(id => id != null)!);

        /// <summary>
        /// Calculates a modifier which can be used to dynamically adjust the score threshold.
        /// </summary>
        private static float GetModifier(float count, float? totalCount) =>
            Math.Max(2*(0.1f - (float)(count / totalCount!)), 0f);

        /// <summary>
        /// Counts the number of valid results based on the settings.
        /// </summary>
        private static int CountValidResults(SearchResults<SearchDocument> searchResult, SearchSettings settings) =>
            searchResult.GetResults()
                .Count(doc => IsValidResult(doc, settings, GetModifier(50f, (float)searchResult.TotalCount!)));

        /// <summary>
        /// Checks if the search result is valid based on the settings.
        /// </summary>
        private static bool IsValidResult(SearchResult<SearchDocument> doc,
                                          SearchSettings settings,
                                          float modifier = 0.0f) =>
            settings.UseSemanticRanker
                ? doc.SemanticSearch?.RerankerScore == null || 
                    doc.SemanticSearch.RerankerScore >= settings.MinimumRerankerScore - modifier
                : doc.Score >= settings.MinimumSearchScore - modifier;

        /// <summary>
        /// Extracts the page number from the sourcepage.
        /// </summary>
        private static int ExtractPageNumber(string? sourcepage) =>
            int.TryParse(sourcepage?[(sourcepage!.LastIndexOf('=') + 1)..], out var page) ? page : -1;
    }
}
