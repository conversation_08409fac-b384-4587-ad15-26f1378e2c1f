import { useState, useEffect } from "react";
import { INITIAL_WIDTH, MAX_RESOLUTION_FOR_MOBILE, MAX_WIDTH, MIN_WIDTH } from "../constants/AdjustableWindowConstants";
import { AdjustableWindowProps } from "../interfaces";

export const AdjustableWindow = ({
  initialWidth = INITIAL_WIDTH,
  minWidth = MIN_WIDTH,
  maxWidth = MAX_WIDTH,
  
}: AdjustableWindowProps) => {
  const [width, setWidth] = useState(initialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const [initialMouseX, setInitialMouseX] = useState(0);
  const [initialWidthState, setInitialWidthState] = useState(initialWidth);

  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    setInitialMouseX(e.clientX);
    setInitialWidthState(width);
  };

  const handleResize = (e: MouseEvent) => {
    if (isResizing) {
      const delta = e.clientX - initialMouseX;
      const newWidth = Math.min(maxWidth, Math.max(minWidth, initialWidthState + delta));
      setWidth(newWidth);
    }
  };

  const handleResizeEnd = () => {
    setIsResizing(false);
  };

  useEffect(() => {
    const handleResize = () => {
        if (window.matchMedia(`(max-width: ${MAX_RESOLUTION_FOR_MOBILE}px)`).matches) {
            setWidth(MIN_WIDTH);
        }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
        window.removeEventListener("resize", handleResize);
    };
}, []);


  useEffect(() => {
    if (isResizing) {
      window.addEventListener("mousemove", handleResize);
      window.addEventListener("mouseup", handleResizeEnd);
      return () => {
        window.removeEventListener("mousemove", handleResize);
        window.removeEventListener("mouseup", handleResizeEnd);
      };
    }
  }, [isResizing]);

  return { width, handleResizeStart };
};
