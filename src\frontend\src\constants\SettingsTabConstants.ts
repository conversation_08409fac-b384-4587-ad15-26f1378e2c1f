export const TEMPERATURE = {
    defaultValue: 0.01,
    minValue:0.0,
    maxValue: 1.0,
    step:0.01,
    label: "Creativity (Temperature)",
    description: "Controls randomness. Lowering the temperature means that the model will produce more repetitive and deterministic responses. Increasing the temperature will result in more unexpected or creative responses. *Try not to adjust this while at the same time adjusting Top P. Try one or the other.*",
    minHint: "Predictable",
    maxHint: "Creative",
}

export const TOP_P = {
    defaultValue: 0.95,
    minValue:0.0,
    maxValue: 1.0,
    step:0.01,
    label: "Top word choices (Top P)",
    description: "Another way to control creativity, but works differently to Temperature. Instead of considering all possible words, it limits the choices to a top percentage based on probability. So, if you set Top P to 0.9, you’re only picking from the top 90% of the most likely words. It's like choosing from the best of the best, while ignoring the less likely ones.",
    minHint: "Less common",
    maxHint: "More likely",
}

export const FREQUENCY_PENALTY = {
    defaultValue: 0.0,
    minValue:0.0,
    maxValue: 2.0,
    step:0.01,
    label: "Frequency of word repetition (Frequency Penalty)",
    description: "Tell the model not to repeat a word that has already been used multiple times in the conversation. The smaller the value, the less chance of repeated words from the AI.",
    minHint: "Less repeated words",
    maxHint: "More repeated words",
}

export const PRESENCE_PENALTY = {
    defaultValue: 0.0,
    minValue:0.0,
    maxValue: 2.0,
    step:0.01,
    label: "Repeated Words (Presence Penalty)",
    description: "Prevents the model from repeating a word, even if it's only been used once.",
    minHint: "Prevent repeated words",
    maxHint: "Allow repeated words",
}

export const SYSTEM_TEMPLATE = [
    {
        label: "Default Gallagher",
        description: "You are an AI assistant that helps Arthur J. Gallagher employees with various task from summarizing text, translating text, generating detailed reports, creating presentations, to drafting emails. Gallagher is the third largest insurance brokerage company in the world with over 50 thousand employees across the world. Gallagher provides tailored and comprehensive insurance solutions designed for all business needs, develops effective risk management strategies to help protect your people and reduce your total cost of risk, and delivers a strategic approach to organizational wellbeing that invests in people's health, financial wellbeing and career growth to align with their goals. Answer all questions accurately.",
        template: "system",
        default: true,
        id:"1"
    },
]

export const TEMPLATE_SETTINGS = {
    label: "Assistant Instruction Template",
    description: "Give the assistant instructions on how to reply in response. Choose from pre-selected templates or create your own.",
}

export const addCustomTemplateButton = {
    label:"Add Assistant Instruction Template",
    tooltipLabel:"Assistant Instruction Template Limit:",
}

export const deleteCustomTemplateButton = {
    label:"Delete Assistant Instruction Template",
}

export const CUSTOM_TEMPLATE_LIMIT = 5; // Maximum number of custom templates allowed
