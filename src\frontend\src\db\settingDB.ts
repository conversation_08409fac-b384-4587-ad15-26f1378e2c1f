import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY, SYSTEM_TEMPLATE } from "../constants/SettingsTabConstants";
import { USER_SETTINGS_ID, settingVersion, Stores } from "../constants/dbConstants";
import { UserSettingsTypes } from "../interfaces";
import { CustomTemplatePropType, MergedTemplateListPropTypes } from "../types/dbTypes";

let db: IDBDatabase;

const initDB = async (): Promise<void> => {
  if (!db) {
    await initUserSettingDB();
  }
};

export const initUserSettingDB = async (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('Settings', settingVersion);

    request.onupgradeneeded = () => {
      db = request.result;
      if (!db.objectStoreNames.contains(Stores.UserSettings)) {
        db.createObjectStore(Stores.UserSettings, { keyPath: 'id' });
      }
      if (!db.objectStoreNames.contains(Stores.Template)) {
        db.createObjectStore(Stores.Template, { keyPath: 'id' });
      }
      if (!db.objectStoreNames.contains(Stores.SelectedCustomTemplate)) {
        db.createObjectStore(Stores.SelectedCustomTemplate, { keyPath: 'id' });
      }
      // Create other stores if needed
      if (!db.objectStoreNames.contains(Stores.LanguageSettings)) {
        // console.log('Creating LanguageSettings store');
        db.createObjectStore(Stores.LanguageSettings, { keyPath: 'id' });
      }
    };
    request.onsuccess = () => {
      db = request.result;
      resolve(true);
    };

    request.onerror = () => {
      console.error('Error opening database:', request.error);
      if (request.error?.name === 'VersionError') {
        const deleteRequest = indexedDB.deleteDatabase('Settings');
        deleteRequest.onsuccess = async () => {
          console.log('Database deleted successfully. Retrying initialization...');
          try {
        const success = await initUserSettingDB();
        resolve(success);
          } catch (error) {
        console.error('Error reinitializing database:', error);
        reject(false);
          }
        };
        deleteRequest.onerror = () => {
          console.error('Error deleting database:', deleteRequest.error);
          reject(false);
        };
      } else {
        reject(false);
      }
    };
  });
};

export const getUserSetting = async (id: string): Promise<UserSettingsTypes> => {
  await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([Stores.UserSettings], 'readonly');
    const store = transaction.objectStore(Stores.UserSettings);
    const request = store.get(id);

    request.onsuccess = () => {
      const defaultSettings = {
        id: USER_SETTINGS_ID,
        temperature: TEMPERATURE.defaultValue,
        topP: TOP_P.defaultValue,
        frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
        presencePenalty: PRESENCE_PENALTY.defaultValue,
        template: SYSTEM_TEMPLATE.find((template) => template.default === true)?.description || ""
      };

      const savedSettings = request.result;
      if (!savedSettings) {
        resolve(defaultSettings);
        return;
      }
      const isValid = (val: number, config: { minValue: number; maxValue: number }) =>
        typeof val === "number" && val >= config.minValue && val <= config.maxValue;

      if (
        !isValid(savedSettings.temperature, TEMPERATURE) ||
        !isValid(savedSettings.topP, TOP_P) ||
        !isValid(savedSettings.frequencyPenalty, FREQUENCY_PENALTY) ||
        !isValid(savedSettings.presencePenalty, PRESENCE_PENALTY)
      ) {
        // If there's no value on settings object, it loads the default values
        saveUserSetting(defaultSettings);
        resolve(defaultSettings);
        return;
      }
      const mergedSettings = { ...defaultSettings, ...savedSettings };
      resolve(mergedSettings);
      
    };

    request.onerror = () => {
      console.error('Error getting setting:', request.error);
      reject(undefined);
    };
  });
};

export const saveUserSetting = async (settings: UserSettingsTypes): Promise<void> => {
  await initDB();
  const existingSetting = await getUserSetting(settings.id);

  const transaction = db.transaction([Stores.UserSettings], 'readwrite');
  const store = transaction.objectStore(Stores.UserSettings);

  if (existingSetting) {
    store.delete(settings.id).onerror = (_event) => {
      console.error('Error deleting old setting:');
    };
  }

  store.put(settings).onerror = (_event) => {
    console.error('Error saving new setting:');
  };
};

export const getCustomTemplate = async (): Promise<CustomTemplatePropType[]> => {
  await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([Stores.Template], 'readonly');
    const store = transaction.objectStore(Stores.Template);
    const request = store.getAll();

    request.onsuccess = () => {
      resolve(request.result);
    };

    request.onerror = () => {
      console.error('Error getting templates:', request.error);
      reject([]);
    };
  });
};

export const saveCustomTemplate = async (template: MergedTemplateListPropTypes): Promise<void> => {
  await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([Stores.Template], 'readwrite');
    const store = transaction.objectStore(Stores.Template);
    const request = store.put(template);

    request.onsuccess = () => {
      console.log('Template saved successfully');
      resolve();
    };

    request.onerror = () => {
      console.error('Error saving template:', request.error);
      reject(request.error);
    };
  });
};

export const updateOrSaveCustomTemplate = async (id: string, textareaValue: string, templateList: MergedTemplateListPropTypes[]): Promise<void> => {
  await initDB();
  const customTemplates = templateList.filter((template) => template.template === "custom");
  const selectedTemplate = templateList.find((template) => template.id === id);

  for (const template of customTemplates) {
    await saveCustomTemplate(template);
  }

  const dbData = await getCustomTemplate();
  const updatedTemplate = dbData.find((template) => template.id === id);
  if (updatedTemplate) {
    updatedTemplate.description = textareaValue;
    await saveCustomTemplate(updatedTemplate);
  }

  if (selectedTemplate) {
    await saveSelectedCustomTemplate(selectedTemplate);
    const dbDataSelectedTemplate = await getSelectedCustomTemplate();
    if (dbDataSelectedTemplate) {
      dbDataSelectedTemplate.description = textareaValue;
      await saveSelectedCustomTemplate(dbDataSelectedTemplate);
    }
  }
};

export const getSelectedCustomTemplate = async (): Promise<MergedTemplateListPropTypes> => {
  await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([Stores.SelectedCustomTemplate], 'readonly');
    const store = transaction.objectStore(Stores.SelectedCustomTemplate);
    const request = store.getAll();

    request.onsuccess = () => {
      if (request.result.length > 0) {
        resolve(request.result[0]);
      } 
    };

    request.onerror = () => {
      console.error('Error getting selected template:', request.error);
      reject(undefined);
    };
  });
};

export const saveSelectedCustomTemplate = async (template: MergedTemplateListPropTypes): Promise<void> => {
  await initDB();
  const transaction = db.transaction([Stores.SelectedCustomTemplate], 'readwrite');
  const store = transaction.objectStore(Stores.SelectedCustomTemplate);

  const clearRequest = store.clear();
  clearRequest.onsuccess = () => {
    const request = store.put(template);
    request.onsuccess = () => {
      console.log('Selected Template saved successfully');
    };
    request.onerror = () => {
      console.error('Error saving selected template:', request.error);
    };
  };
  clearRequest.onerror = () => {
    console.error('Error clearing selected templates:', clearRequest.error);
  };
};

export const deleteCustomTemplate = async (id: string): Promise<void> => {
  await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([Stores.Template], 'readwrite');
    const store = transaction.objectStore(Stores.Template);
    const request = store.delete(id);

    request.onsuccess = () => {
      console.log(`Template with id ${id} deleted successfully`);
      resolve();
    };

    request.onerror = () => {
      console.error('Error deleting template:', request.error);
      reject(request.error);
    };
  });
};

export const deleteSelectedCustomTemplate = async (): Promise<void> => {
  await initDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([Stores.SelectedCustomTemplate], 'readwrite');
    const store = transaction.objectStore(Stores.SelectedCustomTemplate);
    const request = store.clear();

    request.onsuccess = () => {
      console.log('Selected custom template deleted successfully');
      resolve();
    };

    request.onerror = () => {
      console.error('Error deleting selected custom template:', request.error);
      reject(request.error);
    };
  });
};