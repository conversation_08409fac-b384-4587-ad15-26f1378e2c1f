
# Story/Bug/Spike

# Acceptance Criteria/Functionality

When creating PR, Please follow secuirty guide lines document.The guide lines documents can be found  https://dev.azure.com/ajg-corp/GTS-InfrastructureAsCode-CenterOfExcellence/_git/IaC-Documentation?path=/Security&version=GBmain

# Definition of Done

- [ ] Acceptance Criteria have been listed above and met
- [ ] If this affects a networking change, add the Iac Networking team to the required reviewers
- [ ] If this affects security or a highly privileged operation, add the IaC security team to the required reviewers
- [ ] Once merged into main, you, as the code writer, are responsible to ensure it gets into production
- [ ] Once merged into main, you need to update the task status in the appropriate platform

# Reviewer Checklist

Check one:
- [ ] You confirm that all tests exist in the solution that support the above acceptance criteria
- [ ] The solution has been demonstrated to you and the merging developer has shown they meet all the requirements for this story