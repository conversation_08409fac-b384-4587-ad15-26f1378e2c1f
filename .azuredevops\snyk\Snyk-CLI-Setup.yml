parameters:
  - name: SNY<PERSON>_TOKEN
    type: string   # This will be pulled from a variable group 
    default: $(SNYK_TOKEN)

# determines if build should fail or not
  - name: allowBuildCompletion
    type: boolean
    default: true

steps:
#installing snyk CLI
- script: |
    npm install -g snyk snyk-to-html
  displayName: 'Installing Snyk CLI'
  condition: always()
  continueOnError: ${{ parameters.allowBuildCompletion }}
  
#authenticating to snyk
- script: |
    snyk auth ${{ parameters.SNYK_TOKEN }}   
  displayName: 'Authenticating Snyk'
  condition: always()
  continueOnError: ${{ parameters.allowBuildCompletion }}