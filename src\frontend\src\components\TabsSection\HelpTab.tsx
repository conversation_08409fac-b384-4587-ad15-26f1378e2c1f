import React from "react";
import { Checkmark20Filled, Dismiss20Filled } from "@fluentui/react-icons";

const HelpTab: React.FC = React.memo(() => {
  return (
    <div className="h-full flex justify-center bg-white dark:bg-zinc-800 dark:text-white">
      <div className="flex flex-col h-full p-4 space-y-4 overflow-y-auto w-full max-w-3xl mx-auto">
        <h2 className="text-lg font-semibold text-left">Help</h2>
        <p>
          Help and contacts on{" "}
          <a
            href="https://ajg0.sharepoint.com/teams/GO-ai_at_Gallagher/SitePages/Gallagher-AI-Help.aspx"
            target="_blank"
            className="text-gallagher-dark-300 dark:text-sky-400 underline underline-offset-6 decoration-gallagher-dark-300"
          >
            Gallagher One
          </a>
        </p>
        <h2 className="text-lg font-semibold text-left">Quick Tips</h2>
        <div>
          <h3 className="flex items-center">
            <Checkmark20Filled className="mr-2" /> DO
          </h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>Be clear and concise.</li>
            <li>Provide context.</li>
            <li>Ask for clarification.</li>
            <li>Use it to write or edit emails.</li>
            <li>Use it to translate documents into different languages.</li>
            <li>Use it to convert bullet points to written paragraphs.</li>
            <li>Use real data – we've made the connection secure!</li>
          </ul>
        </div>
        <div>
          <h3 className="flex items-center">
            <Dismiss20Filled className="mr-2" /> DO NOT
          </h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>Use jargon.</li>
            <li>Rely solely on the response from the chat bot.</li>
            <li>
              Use for factual data. e.g. "Is Montana a direction of care state?"
            </li>
            <li>
              Assume Gallagher AI has knowledge or understanding of your
              specific topic.
            </li>
            <li>
              Expect Gallagher AI to provide legal or professional advice.
            </li>
            <li>Use it for mathematical computations.</li>
          </ul>
        </div>
      </div>
    </div>
  );
});

export default HelpTab;