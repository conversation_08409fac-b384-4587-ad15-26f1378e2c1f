import { render, screen } from '@testing-library/react';
import Toast from '../Toast';
import { describe, it, expect, vi } from 'vitest';
import { ToastProps } from '../../../../interfaces';
import { CheckmarkCircle24Filled } from '@fluentui/react-icons';

describe('Toast', () => {
  const defaultProps: ToastProps = {
    position: 'top-right',
    text: 'This is a toast message',
    duration: 3,
    onClose: vi.fn(),
    id: 1,
  };

  const renderComponent = (props = {}) =>
    render(<Toast {...defaultProps} {...props} />);

  it('renders correctly', () => {
    renderComponent();
    expect(screen.getByText('This is a toast message')).toBeInTheDocument();
  });

  it('displays title if provided', () => {
    renderComponent({ title: 'Toast Title' });
    expect(screen.getByText('Toast Title')).toBeInTheDocument();
  });

  it('displays icon if provided', () => {
    renderComponent({ icon: <CheckmarkCircle24Filled /> });
    expect(screen.getByLabelText('icon')).toBeInTheDocument();
  });

  it('applies correct position class', () => {
    renderComponent({ position: 'bottom-left' });
    const toast = screen.getByRole('alert');
    expect(toast).toHaveClass('bottom-0 left-0 mb-16 ml-6');
  });

  it('applies custom className', () => {
    renderComponent({ className: 'custom-class' });
    const toast = screen.getByRole('alert');
    expect(toast).toHaveClass('custom-class');
  });

  it('applies custom background color', () => {
    renderComponent({ bgColor: 'bg-red-500' });
    const toast = screen.getByRole('alert');
    expect(toast).toHaveClass('bg-red-500');
  });
});