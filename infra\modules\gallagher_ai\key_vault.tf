module "keyvault" {
  source  = "app.terraform.io/ajg/keyvault/azurerm"
  version = "5.0.4"

  # Resource Metadata
  azurerm_location                              = var.azurerm_resource_group_location
  azurerm_resource_group_name                   = var.azurerm_resource_group_name
  environment                                   = var.environment
  purpose                                       = "Secrets / Connection Management"
  short_division                                = lower(var.division_short)
  tags                                          = merge(var.tags, { "environment" = var.environment })
  azurerm_diagnostic_log_analytics_workspace_id = var.azurerm_log_analytics_workspace_id

  # Networking
  azurerm_private_endpoint_subnet_id = var.azurerm_private_endpoint_subnet_id
}

# Access Policy for Backend App Service
resource "azurerm_key_vault_access_policy" "app_service_backend" {
  key_vault_id = module.keyvault.key_vault_id
  tenant_id    = var.tenant_id
  object_id    = module.gallagher-ai-backend-linux-webapp.linux_web_app_identity_service_principal_id

  secret_permissions = [
    "Get", "List"
  ]
}

# Developers access to secrets (in dev only)
resource "azurerm_key_vault_access_policy" "dev_secrets" {
  count        = length(var.rbac_devadmin)
  key_vault_id = module.keyvault.key_vault_id
  tenant_id    = var.tenant_id
  object_id    = var.rbac_devadmin[count.index]

  secret_permissions = [
    "Get", "List", "Set", "Delete"
  ]
}