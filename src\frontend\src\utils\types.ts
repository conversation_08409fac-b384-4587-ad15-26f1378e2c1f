export interface ChatMessageProps {
    user: string;
    text: string;
    messageId: number;
    isStreamComplete: boolean;
    workspaceId: string;
    documentId: string;
    onPdfClick?: (pdfUrl: string) => void;
    copyButtonRef?: (element: HTMLDivElement | null) => void;
  }

  export interface ChatState {
    messages: ChatMessageProps[];
  }

export type TransformedMessageProps ={
    role: string;
    content: string;
}

export type EncryptedDataProps = {
    hash: string;
    id: string;
    date: string;
    workspaceId: string;
}

export type  ChatTabsProps = {
    text: string;
    id: string;
}