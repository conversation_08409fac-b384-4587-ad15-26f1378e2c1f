﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Represents a document record.
    /// </summary>
    public record Document()
    {
        /// <summary>
        /// Name of the document.
        /// </summary>
        [JsonPropertyName("name")] 
        public string Name { get; set; } = "";

        /// <summary>
        /// Flag indicating whether the document has been indexed in the search database.
        /// </summary>
        [JsonPropertyName("processed")] 
        public Processed Processed { get; set; } = Processed.False;

        /// <summary>
        /// Date the document expires.
        /// </summary>
        [JsonPropertyName("expires")]
        public string? Expires { get; set; }
    }
}
