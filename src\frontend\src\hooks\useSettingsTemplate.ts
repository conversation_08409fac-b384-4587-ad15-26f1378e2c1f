import { useState } from "react";

const useSettingsTemplate = () => {
    const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);

    const openCustomTemplateModal = () => {
        setIsTemplateModalOpen(true);
      };
    
    const closeCustomTemplateModal = () => {
    setIsTemplateModalOpen(false);
    };

    return { isTemplateModalOpen, openCustomTemplateModal, closeCustomTemplateModal };
}

export default useSettingsTemplate;