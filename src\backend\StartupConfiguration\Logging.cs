﻿

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureLogging(this WebApplicationBuilder builder)
        {
            builder.Logging.ClearProviders();

            if (builder.Environment.IsEnvironment("Local"))
            {
                builder.Logging.AddConsole();
            }
            else
            {
                builder.Logging.AddApplicationInsights(
                    configureTelemetryConfiguration: (config) =>
                        config.ConnectionString = builder.Configuration["ApplicationInsights:ConnectionString"],
                        configureApplicationInsightsLoggerOptions: (options) => { }
                );
            }
        }
    }
}