# *****************************************
# Team role Access for development purposes
# *****************************************

resource "azurerm_role_assignment" "devs_contributors" {
  count                = length(var.rbac_devadmin)
  scope                = var.azurerm_resource_group_id
  role_definition_name = "AJG Contributor"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Allows the contributor group to perform all actions on the resource group"
}

# *****************************************
#			Primary Storage Access
# *****************************************

resource "azurerm_role_assignment" "devs_storage_account_contributor" {
  count                = length(var.rbac_devadmin)
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Account Contributor"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Permits management of storage accounts."
}

resource "azurerm_role_assignment" "devs_storage_blob_data_owner" {
  count                = length(var.rbac_devadmin)
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Owner"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Provides full access to Azure Storage blob containers and data."
}

resource "azurerm_role_assignment" "devs_contributor_to_storage" {
  count                = length(var.rbac_devadmin)
  scope                = module.storage_account.resource_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Read, write, and delete Azure Storage containers and blobs."
}

# *****************************************
#		    Doc Intelligence Access
# *****************************************

resource "azurerm_role_assignment" "devs_cog_svcs_contributor" {
  count                = length(var.rbac_devadmin)
  scope                = module.cognitive-services.cognitive_account.id
  role_definition_name = "Cognitive Services Contributor"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Contributor to Document Intelligence resource."
}

resource "azurerm_role_assignment" "devs_cog_svcs_user" {
  count                = length(var.rbac_devadmin)
  scope                = module.cognitive-services.cognitive_account.id
  role_definition_name = "Cognitive Services User"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Lets you read and list keys of Cognitive Services."
}

# *****************************************
#		    Search Service Access
# *****************************************

resource "azurerm_role_assignment" "devs_search_service_data_contributor" {
  count                = length(var.rbac_devadmin)
  scope                = azurerm_search_service.search.id
  role_definition_name = "Search Index Data Contributor"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Grants full access to Index data."
}

resource "azurerm_role_assignment" "devs_search_service_data_reader" {
  count                = length(var.rbac_devadmin)
  scope                = azurerm_search_service.search.id
  role_definition_name = "Search Index Data Reader"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Grants read access to Azure Cognitive Search index data."
}

# *****************************************
#		      Translation Access
# *****************************************

resource "azurerm_role_assignment" "devs_translation_contributor" {
  count                = length(var.rbac_devadmin)
  scope                = module.cognitive-doc-translation.cognitive_account.id
  role_definition_name = "Cognitive Services Contributor"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Contributor to Document Intelligence resource."
}

resource "azurerm_role_assignment" "devs_translation_user" {
  count                = length(var.rbac_devadmin)
  scope                = module.cognitive-doc-translation.cognitive_account.id
  role_definition_name = "Cognitive Services User"
  principal_id         = var.rbac_devadmin[count.index]
  description          = "Managed by Terraform - Lets you read and list keys of Cognitive Services."
}

#resource "azurerm_role_assignment" "devs_web_search_reader" {
#  count                = length(var.rbac_devadmin)
#  scope                = module.cognitive-bing-search.cognitive_account.id
#  role_definition_name = "Search Index Data Reader"
#  principal_id         = var.rbac_devadmin[count.index]
#  description          = "Managed by Terraform - Read-only Access for querying search indexes."
#}

#resource "azurerm_role_assignment" "devs_web_search_contributor" {
#  count                = length(var.rbac_devadmin)
#  scope                = module.cognitive-bing-search.cognitive_account.id
#  role_definition_name = "Search Index Data Contributor"
#  principal_id         = var.rbac_devadmin[count.index]
#  description          = "Managed by Terraform - Read-write Access to content in indexes."
#}

# *****************************************
#	 Additional Regional Storage Access
# *****************************************

resource "azurerm_role_assignment" "add_devs_storage_account_contributor" {
  for_each             = { for idx, combo in local.rbac_assignments : "${combo.region}-${combo.admin}" => combo }
  scope                = azurerm_storage_account.asr[each.value.region].id
  role_definition_name = "Storage Account Contributor"
  principal_id         = each.value.admin
  description          = "Managed by Terraform - Permits management of storage accounts."
}

resource "azurerm_role_assignment" "add_devs_storage_blob_data_owner" {
  for_each             = { for idx, combo in local.rbac_assignments : "${combo.region}-${combo.admin}" => combo }
  scope                = azurerm_storage_account.asr[each.value.region].id
  role_definition_name = "Storage Blob Data Owner"
  principal_id         = each.value.admin
  description          = "Managed by Terraform - Provides full Access to Azure Storage blob containers and data."
}

resource "azurerm_role_assignment" "add_devs_contributor_to_storage" {
  for_each             = { for idx, combo in local.rbac_assignments : "${combo.region}-${combo.admin}" => combo }
  scope                = azurerm_storage_account.asr[each.value.region].id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = each.value.admin
  description          = "Managed by Terraform - Read, write, and delete Azure Storage containers and blobs."
}

resource "azurerm_role_assignment" "add_devs_cog_svcs_contributor" {
  for_each             = { for idx, combo in local.rbac_assignments : "${combo.region}-${combo.admin}" => combo }
  scope                = azurerm_search_service.add_search[each.value.region].id
  role_definition_name = "Search Index Data Contributor"
  principal_id         = each.value.admin
  description          = "Managed by Terraform - Grants full access to Index data."
}

resource "azurerm_role_assignment" "add_devs_cog_svcs_user" {
  for_each             = { for idx, combo in local.rbac_assignments : "${combo.region}-${combo.admin}" => combo }
  scope                = azurerm_search_service.add_search[each.value.region].id
  role_definition_name = "Search Index Data Reader"
  principal_id         = each.value.admin
  description          = "Managed by Terraform - Grants read access to Azure Cognitive Search index data."
}

