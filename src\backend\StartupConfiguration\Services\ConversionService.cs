﻿using Backend.Models;
using Backend.Services;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureConversionService(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<IConversionService, ConversionService>(sp =>
            {
                return new ConversionService(config["Conversion:Endpoint"]!,
                                             config["Conversion:Key"]!);
            });
        }
    }
}
