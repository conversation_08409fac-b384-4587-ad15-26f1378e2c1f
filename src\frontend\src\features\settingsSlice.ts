import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { UserSettingsTypes } from "../interfaces";
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY } from "../constants/SettingsTabConstants";
import { USER_SETTINGS_ID } from "../constants/dbConstants";



const initialState: UserSettingsTypes = {
    id: USER_SETTINGS_ID,
    temperature: TEMPERATURE.defaultValue,
    topP: TOP_P.defaultValue,
    frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
    presencePenalty: PRESENCE_PENALTY.defaultValue,
    template:""
};


const settingSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    setSetting: (state, action: PayloadAction<UserSettingsTypes>) => {
        return { ...state, ...action.payload };
    },
  },
});

export const { setSetting } = settingSlice.actions;

export default settingSlice.reducer;
