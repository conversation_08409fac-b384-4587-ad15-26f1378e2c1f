import React, { useState, useRef, useEffect, ReactNode } from "react";
import { createPortal } from "react-dom";
import ReactMarkdown from "react-markdown";

interface TooltipProps {
  message: string;
  children: ReactNode;
  position?: "top" | "bottom" | "left" | "right";
  className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({ message, children, position = "right", className }) => {
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const [showTooltip, setShowTooltip] = useState(false);
  const triggerRef = useRef<HTMLDivElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (showTooltip && triggerRef.current && tooltipRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      let top = triggerRect.bottom + 5 + window.scrollY;
      let left = triggerRect.left + window.scrollX;

      if (position === "top") {
        top = triggerRect.top - tooltipRect.height - 5 + window.scrollY;
      } else if (position === "left") {
        left = triggerRect.left - tooltipRect.width - 5 + window.scrollX;
        top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2 + window.scrollY;
      } else if (position === "right") {
        left = triggerRect.right + 5 + window.scrollX;
        top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2 + window.scrollY;
      }

      if (left + tooltipRect.width > screenWidth + window.scrollX) {
        left = screenWidth + window.scrollX - tooltipRect.width - 10;
      }
      if (top + tooltipRect.height > screenHeight + window.scrollY) {
        top = screenHeight + window.scrollY - tooltipRect.height - 10;
      }

      setTooltipPosition({ top, left });
    }
  }, [showTooltip, position]);

  useEffect(() => {
    const handleTouchOutside = (event: TouchEvent) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setShowTooltip(false);
      }
    };

    if (showTooltip) {
      document.addEventListener("touchstart", handleTouchOutside);
    }
    return () => {
      document.removeEventListener("touchstart", handleTouchOutside);
    };
  }, [showTooltip]);

  return (
    <div
      ref={triggerRef}
      className="relative flex"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
      onTouchStart={() => setShowTooltip((prev) => !prev)}
    >
      {children}
      {showTooltip &&
        createPortal(
          <div
            ref={tooltipRef}
            className={`absolute z-10 bg-gallagher-blue-400 text-white p-2 rounded-sm shadow-md text-xs transition-opacity duration-200 ${className}`}
            style={{ top: `${tooltipPosition.top}px`, left: `${tooltipPosition.left}px` }}
          >
            <ReactMarkdown>{message}</ReactMarkdown>
          </div>,
          document.body
        )}
    </div>
  );
};

export default Tooltip;