﻿using Azure.Core;
using Backend.Models;
using Microsoft.Graph;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using System.ComponentModel;
using System.Text;

namespace Backend.Plugins
{
    public sealed class MSGraphPlugin
    {
        private static readonly HttpClient _client = new HttpClient();
        private static TokenCredential? _credential;
        private string token = "eyJ0eXAiOiJKV1QiLCJub25jZSI6ImZlOUg2OFhIRlNPUEI5SHNmSW9Fc0NwSm1FMXN1S282aVc4MFp4WUZzaVkiLCJhbGciOiJSUzI1NiIsIng1dCI6IkpETmFfNGk0cjdGZ2lnTDNzSElsSTN4Vi1JVSIsImtpZCI6IkpETmFfNGk0cjdGZ2lnTDNzSElsSTN4Vi1JVSJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EtzBUNKB8yVmpBRaIIWzdcvzK6z6dnTm07memuEScK-0mLLpwp1Pw1-wyt33YO7IB05C6eJsLPP8nY8GOqR_9nJ8rmZM4N24J4TToYkYfDPn-CEYQlS7-0I-b8GvSYfbjANnROplNjhoNMxYWL0tgUrMHtcXW4Jjf9fPIg2P9h5DFiF2-9Ehbw2OOAxSrw-DxDq1cbhRtjAR7lsdpjD4fnRY1GKo5lmpWBgYnM_PeEEXaiQoE7XMa-VpG-n7yvPwXcOVBJG98EtNeEMD6z5Q-WIAikC08t_BrAnbEsvSZG7LU4dtRAIPec4aFm2yGuTLA0AERlYdlpcmctvaD8-zng";

        public MSGraphPlugin(TokenCredential credential)
        {
            _credential = credential;
        }

        [KernelFunction("List_IT_Policies"), Description("Returns Gallagher's current Global IT Policies as markdown.")]
        public async Task<string> GetITPolicy()
        {
            var url = "https://graph.microsoft.com/v1.0/sites/ajg0.sharepoint.com,dcc8cb2a-5623-49ba-bb64-484e3f1357cf,10e5f21c-c81b-4958-9611-d44526c39d35/lists/e91ee054-09af-4d31-84a7-81e993f30be3/items?$filter=fields/ExpirationDate eq null&$expand=fields($select=Title,ItemNumber,CreateException)";
            var request = new HttpRequestMessage(HttpMethod.Get, url);

            //var tokenRequestContext = new TokenRequestContext(new[] { "https://graph.microsoft.com/.default" });
            //var token = await _credential!.GetTokenAsync(tokenRequestContext, default);
            //request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token.Token);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            // Send the request and get the response
            var response = await _client.SendAsync(request);

            // Parse the response
            string json = await response.Content.ReadAsStringAsync();
            dynamic parsedJson = JsonConvert.DeserializeObject(json)!;

            StringBuilder markdownBuilder = new StringBuilder();

            // Group items by the prefix of ItemNumber
            var groupedItems = ((IEnumerable<dynamic>)parsedJson!.value)
                .GroupBy(item => ((string)item.fields.ItemNumber).Split('.')[0])
                .OrderBy(group => double.Parse(group.Key));

            foreach (var group in groupedItems)
            {
                // Use the first item in the group as the policy title
                var firstItem = group.First();
                string policyTitle = (string)firstItem.fields.Title;
                string policyItemNo = formatNumber((string)firstItem.fields.ItemNumber);
                string policyLink = $"https://ajg0.sharepoint.com/teams/GO-apps-itpolicy/SitePages/viewpolicy.aspx#{policyItemNo}";

                markdownBuilder.AppendLine($"# {policyTitle} ({policyLink})");
                markdownBuilder.AppendLine("| Section | Link | Exceptions Allowed |");
                markdownBuilder.AppendLine("|---------|------|--------------------|");

                // Sort items by ItemNumber
                var sortedItems = group.OrderBy(item =>
                {
                    string itemNumberStr = (string)item.fields.ItemNumber;
                    if (double.TryParse(itemNumberStr, out double itemNumber))
                    {
                        return itemNumber;
                    }
                    else
                    {
                        return double.NaN;
                    }
                }).ToList();

                foreach (var item in sortedItems)
                {
                    string itemNumber = formatNumber((string)item.fields.ItemNumber);
                    string link = $"https://ajg0.sharepoint.com/teams/GO-apps-itpolicy/SitePages/viewpolicy.aspx#{itemNumber}";
                    string exception = (bool)item.fields.CreateException ? "✅" : "❌";

                    markdownBuilder.AppendLine($"| {itemNumber} | [{item.fields.Title}]({link}) | {exception} |");
                }

                markdownBuilder.AppendLine();
            }

            string markdown = markdownBuilder.ToString();
            return markdown;
        }

        [KernelFunction("SearchPolicyInfo"), Description("Searches and returns relevant policy information for a given query")]
        public async Task<SectionItem[]> SearchPolicyInfo(
            [Description("The search query.")] string query)
        {
            var sectionItems = new List<SectionItem>();
            var url = "https://graph.microsoft.com/v1.0/search/query";
            var queryString = $"{query} AND PATH:\\\"https://ajg0.sharepoint.com/teams/GO-apps-itpolicy/Lists/Policies\\\"";
            var jsonString = $@"
            {{
                ""requests"": [
                    {{
                        ""entityTypes"": [
                            ""listItem""
                        ],
                        ""query"": {{
                            ""queryString"": ""{queryString}""
                        }},
                        ""fields"": [
                            ""webUrl"",
                            ""createdDateTime""
                        ],
                        ""from"": 0,    
                        ""size"": 10
                    }}
                ]
            }}";

            var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content
            };

            //var tokenRequestContext = new TokenRequestContext(new[] { "https://graph.microsoft.com/.default" });
            //var token = await _credential!.GetTokenAsync(tokenRequestContext, default);
            //request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token.Token);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            // Send the request and get the response
            var response = await _client.SendAsync(request);

            // Parse the response
            string json = await response.Content.ReadAsStringAsync();
            dynamic parsedJson = JsonConvert.DeserializeObject(json)!;

            var date = new DateTime(2021, 1, 1);
            var requests = new StringBuilder();
            requests.Append("{ \"requests\": [");

            if (parsedJson!.value != null && parsedJson.value.Count > 0 &&
                parsedJson!.value[0].hitsContainers != null && parsedJson.value[0].hitsContainers.Count > 0 &&
                parsedJson!.value[0].hitsContainers[0].hits != null)
            {
                foreach (var hit in parsedJson.value[0].hitsContainers[0].hits)
                {
                    if ((DateTime)hit.resource.createdDateTime >= date)
                    {
                        string webUrl = hit.resource.webUrl;
                        string id = webUrl.Split('=').Last();
                        requests.Append($@"
                        {{
                            ""id"": ""{id}"",
                            ""method"": ""GET"",
                            ""url"": ""/sites/ajg0.sharepoint.com,dcc8cb2a-5623-49ba-bb64-484e3f1357cf,10e5f21c-c81b-4958-9611-d44526c39d35/lists/e91ee054-09af-4d31-84a7-81e993f30be3/items/{id}?$expand=fields($select=Title,ItemNumber,ItemBody,ExpirationDate,CreateException)""
                        }},");
                    }
                }
            } else
            {
                sectionItems.Add(new SectionItem("", "No policy information found."));
                return sectionItems.ToArray();
            }
            requests.Remove(requests.Length - 1, 1);
            requests.Append("]}");

            content = new StringContent(requests.ToString(), Encoding.UTF8, "application/json");
            url = "https://graph.microsoft.com/v1.0/$batch";
            request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content
            };
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            response = await _client.SendAsync(request);

            // Parse the response
            json = await response.Content.ReadAsStringAsync();
            parsedJson = JsonConvert.DeserializeObject(json)!;

            foreach (var item in parsedJson!.responses)
            {
                var record = item.body.fields;
                string title = (string)record.Title;
                string number = formatNumber((string)record.ItemNumber);
                string link = $"https://ajg0.sharepoint.com/teams/GO-apps-itpolicy/SitePages/viewpolicy.aspx#{number}";

                sectionItems.Add(new SectionItem(link, (string)record.ItemBody));
            }

            return sectionItems.ToArray();
        }

        private static string formatNumber(string number)
        {
            number = number.TrimEnd('.');
            string temp = number.Split('.')[1].PadLeft(2, '0');
            number = number.Split('.')[0] + "." + temp.Substring(temp.Length - 2);
            return number;
        }

        [KernelFunction, Description("Sends a GET request to the Microsoft Graph API and returns the content as json. If asked about people information, please use this function.")]
        public async Task<string> GraphAPI_GET(
            [Description("A valid Graph API url to run a query.")] string url)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, url);

            var tokenRequestContext = new TokenRequestContext(new[] { "https://graph.microsoft.com/.default" });
            var token = await _credential!.GetTokenAsync(tokenRequestContext, default);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token.Token);

            // Send the request and get the response
            var response = await _client.SendAsync(request);

            // Parse the response
            var responseBody = await response.Content.ReadAsStringAsync();
            return responseBody;
        }
    }
}
