﻿#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0001

using Backend.Models;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using Backend.Models.Context;
using System.Text;

namespace Backend.Services
{
    /// <summary>
    /// This class is used to interact with the OpenAI models. It provides methods to prompt 
    /// the AI directly or to prompt the AI with additional context from documentation.
    /// </summary>
    public class OpenAIService : IOpenAIService
    {
        private readonly Kernel _kernel;
        private readonly ISearchService _searchService;
        private readonly IChatCompletionService _chatService;
        private readonly ITextEmbeddingGenerationService _textEmbeddingService;

        public OpenAIService(Kernel kernel,
                             ISearchService searchService)
        {
            _kernel = kernel;
            _searchService = searchService;
            _chatService = _kernel.GetRequiredService<IChatCompletionService>();
            _textEmbeddingService = _kernel.GetRequiredService<ITextEmbeddingGenerationService>();
        }

        /// <summary>
        /// Prompts the AI about documentation in a workspace  
        /// </summary>
        public async IAsyncEnumerable<Content> PromptAIAboutDocumentation(
            Chat request,
            [EnumeratorCancellation] CancellationToken ct)
        {
            // Step 1 - Get the user's last or most recent question and use it to generate search parameters
            var question = request.GetUsersLastMessage();

            var vector = request.Settings!.Search!.RetrievalMode != RetrievalMode.Text
                ? (await _textEmbeddingService.GenerateEmbeddingsAsync(
                    new List<string> { question.Replace('\r', ' ') }, cancellationToken: CancellationToken.None))
                        .ToArray().Select(e => e.ToArray()).SelectMany(e => e).ToArray()
                : null;

            var query = await GenerateQueryAsync(request.Settings!.Search!.RetrievalMode, request.GetChatHistory(), ct);

            // Step 2 - Using the parameters and search service, search for relevant documentation
            var searchContent = await _searchService.QueryDocumentsAsync(
                request.Workspace!, request.Settings!.Search!, query, vector, ct);

            // Step 3 - Context Check 
            var history = request.GetChatHistory();
            history.AddSystemMessage(GetSearchContentMessage(searchContent));

            if (IsLargeContext(history))
            {
                history.RemoveAt(history.Count - 1);
                var context = SplitContent(searchContent, history);
                
                // Process the content separately in parallel
                var tasks = context.Select(async (searchContent) =>
                {
                    var newHistory = new ChatHistory(history);
                    newHistory.AddSystemMessage(GetSearchContentMessage(searchContent.Content));

                    ChatMessageContent response = await _chatService.GetChatMessageContentAsync(
                        newHistory, request.GetOpenAISettings(), kernel: _kernel, ct);

                    return (searchContent.Title, response.Content!);
                });

                var aggregatedResponses = await Task.WhenAll(tasks);
                AddRequestContent(request, FormatAggregatedResponses(aggregatedResponses));
            }
            else
            {
                AddRequestContent(request, GetSearchContentMessage(searchContent));
            }

            // Step 4 - Get the final AI's response
            await foreach (var response in PromptAI(request, ct, true))
            {
                yield return response;
            }
        }

        /// <summary>
        /// Prompts the AI and processes the response
        /// </summary>
        public async IAsyncEnumerable<Content> PromptAI(
            Chat request,
            [EnumeratorCancellation] CancellationToken ct,
            bool outputCitations = false)
        {
            var context = CurrentContext.User;
            IEnumerable<SectionItem>? searchContent = null;

            // Get the history, appending search content if necessary
            var history = request.GetChatHistory();

            while (true)
            {
                // Get the LLM's response
                ChatMessageContent response = await _chatService.GetChatMessageContentAsync(
                    history, request.GetOpenAISettings(), kernel: _kernel, ct);

                // Determine if the LLM decided to make any function calls
                IEnumerable<FunctionCallContent> functionCalls = FunctionCallContent.GetFunctionCalls(response);

                // If there are no functions to call, return the LLM's response
                if (!functionCalls.Any())
                {
                    if (!string.IsNullOrEmpty(response.Content) && outputCitations)
                    {
                        foreach (var result in GetTextWithCitations(response.Content!))
                        {
                            yield return result;
                            CurrentContext.User = context;
                        }
                    }
                    else if (!string.IsNullOrEmpty(response.Content))
                    {
                        yield return new Content { Type = cType.Text, Value = response.Content! };
                        CurrentContext.User = context;
                    }
                    else
                    {
                        yield return new Content { Type = cType.Text, Value = "I do not know." };
                        CurrentContext.User = context;
                        break;
                    }

                    if (request!.Settings!.SuggestFollowupQuestions == true)
                    {
                        yield return await GenerateFollowupQuestions(response.Content!, request.GetOpenAISettings(), ct);
                        CurrentContext.User = context;
                    }
                    break;
                }

                // Iterating over the requested function calls and invoking them.
                foreach (FunctionCallContent functionCall in functionCalls)
                {
                    history.Add(new() { Role = AuthorRole.Assistant, Items = [functionCall] });

                    // Invoking the function
                    FunctionResultContent functionResult = await functionCall.InvokeAsync(_kernel);

                    // Check if the function result is of type List<SectionItem>
                    if (functionResult.Result is SectionItem[] sectionItems)
                    {
                        searchContent = sectionItems;
                        history.Add(functionResult.ToChatMessage());
                    }
                    else
                    {
                        history.Add(functionResult.ToChatMessage());
                    }
                }

                if (searchContent != null) { outputCitations = true; }
            }
        }

        /// <summary>
        /// Splits content into seperate items, 
        /// in order to fit within context limits
        /// </summary>
        private List<(string Title, IEnumerable<SectionItem> Content)> SplitContent(IEnumerable<SectionItem> searchContent,
                                                                                    ChatHistory history)
        {
            var context = new List<(string Title, IEnumerable<SectionItem> Content)>();
            var groups = searchContent.GroupBy(item =>
            {
                var index = item.Title.IndexOf("#page=");
                return index >= 0 ? item.Title.Substring(0, index) : item.Title;
            });

            foreach (var group in groups)
            {
                var items = group.AsEnumerable();
                var queue = new Queue<(string Title, IEnumerable<SectionItem> Content)>();
                queue.Enqueue((group.Key, items));

                while (queue.Any())
                {
                    var (title, current) = queue.Dequeue();
                    var newHistory = new ChatHistory(history);
                    newHistory.AddSystemMessage(GetSearchContentMessage(current));

                    if (IsLargeContext(newHistory))
                    {
                        var size = Math.Max(1, current.Count() / 2);
                        foreach (var chunk in SplitSectionItems(current, size))
                        {
                            queue.Enqueue((title, chunk));
                        }
                    }
                    else
                    {
                        context.Add((title, current));
                    }
                }
            }
            return context;
        }

        /// <summary>
        /// Adds the content to the request
        /// </summary>
        private void AddRequestContent(Chat request, string value)
        {
            request.Messages.Add(new ChatItem
            {
                Role = Backend.Models.ChatRole.System,
                Content = new List<Backend.Models.Content>
                {
                    new Backend.Models.Content
                    {
                        Type = cType.Text,
                        Value = value
                    }
                }
            });
        }

        /// <summary>
        /// Splits the Section Items into smaller chunks
        /// </summary>
        private static IEnumerable<IEnumerable<SectionItem>> SplitSectionItems(IEnumerable<SectionItem> items,
                                                                               int size)
        {
            var itemList = items.ToList();
            for (int i = 0; i < itemList.Count; i += size)
            {
                yield return itemList.Skip(i).Take(size);
            }
        }

        /// <summary>
        /// Checks character length of the chat history
        /// </summary>
        private static bool IsLargeContext(ChatHistory history)
        {
            StringBuilder context = new StringBuilder();
            foreach (var item in history)
            {
                context.Append(item.Content!.ToString());
            }
            return context.Length > 225_000;
        }

        /// <summary>
        /// Formats the aggregated responses into a single string
        /// </summary>
        private static string FormatAggregatedResponses(IEnumerable<(string Title, string Response)> responses)
        {
            var formattedMessage = new StringBuilder();
            int documentNumber = 1;

            foreach (var (title, response) in responses)
            {
                formattedMessage.AppendLine($"## {title} ##");
                formattedMessage.AppendLine(response);
                formattedMessage.AppendLine($"## END {title} ##");
                documentNumber++;
            }

            formattedMessage.AppendLine();
            formattedMessage.AppendLine("The above are responses generated by separate LLMs in regards to the user's question, and contains citations.");
            formattedMessage.AppendLine("You are to combine these responses into a final response which addresses the user's question. Be sure to include the citations (denoted by `<!--` and `-->` tags).");
            formattedMessage.AppendLine("However, if no response has relevant information, apologize and answer the user's question by stating that you could not find any relevant information.");

            return formattedMessage.ToString();
        }

        /// <summary>
        /// Generates a search query based on the question
        /// </summary>
        /// <param name="mode"></param>
        /// <param name="question"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        private async Task<string> GenerateQueryAsync(RetrievalMode? mode, ChatHistory history, CancellationToken ct)
        {
            if (mode != RetrievalMode.Vector)
            {
                ct.ThrowIfCancellationRequested();


                var newHistory = new ChatHistory(history);
                newHistory.AddSystemMessage("""
                Above is a question asked by the user that needs to be answered by searching in a knowledge base (Azure AI Search).
                Generate a search query based on the the question asked by the user, to be used for a keyword search against the database.
                Do not include cited source filenames and document names e.g info.txt or doc.pdf in the search query terms.
                Do not include any text inside [] or <<>> in the search query terms.
                Do not include any special characters like '+'.
                """);

                var settings = new OpenAIPromptExecutionSettings
                {
                    Temperature = 0.01,
                    MaxTokens = 100,
                    TopP = 0.95,
                    FrequencyPenalty = 0,
                    PresencePenalty = 0
                };

                var result = await _chatService.GetChatMessageContentsAsync(
                    newHistory, settings, kernel: _kernel, cancellationToken: ct);
                return result[0].Content ?? throw new InvalidOperationException("Failed to get search query.");
            }
            return string.Empty;
        }

        /// <summary>
        /// Formats a collection of Section Items into a string
        /// </summary>
        private static string ContentToString(IEnumerable<SectionItem> contentList)
        {
            const string pageBreak = "\n<!-- PageBreak -->";
            return contentList.Any()
                ? string.Join("\r", contentList.Select((x) =>
                {
                    var content = x.Content;
                    if (!content.TrimEnd().EndsWith(pageBreak))
                    {
                        content += pageBreak;
                    }
                    return $"<!-- Page={x.Title} -->\n{content}";
                }))
                : "No source is available.";
        }

        /// <summary>
        /// Returns a message containing the search content + instructions
        /// </summary>
        private static string GetSearchContentMessage(IEnumerable<SectionItem> contentList)
        {
            return $$"""""
                ## DOCUMENTATION ##
                {{ContentToString(contentList)}}
                ## END DOCUMENTATION ##

                NOTE: The documentation above is split into pages, 
                      with each page beginning with a `<!-- Page="filepage" -->` tag 
                      and ending with a `<!-- PageBreak -->` tag.

                ## IMPORTANT INSTRUCTIONS ##

                First, identify the seperate pages.
                Next, for each page you reference, write the facts you wish to use to address the user's question, and then cite these facts using the page source's tag as the citation.
                    example:    Fact1Frompage2. Fact2FromPage2 <!-- Page=fileName#page=2 -->

                If you reference information spanning multiple pages, do not combine their citations all at the end! Facts MUST be grouped and cited page by page.
                    example: Fact1FromPage7. Fact2FromPage7 <!-- Page=fileName#page=7 -->. 
                             Fact3FromPage8 <!-- Page=fileName#page=8 -->. 
                             Fact4FromPage29, Fact5FromPage29, Fact6FromPage29 <!-- Page=fileName#page=29 -->.

                Unless specified otherwise, return tabular information in tabular form.

                If you think asking a clarifying question to the user would help, ask the question. 
                If no source is available, apologize and answer the question by stating that you could not find any relevant information. 
                If the question is not in English, answer in the language used in the question
                """"";
        }

        /// <summary>
        /// Reformats the content, returning a content object containing 
        /// the text with refrences in brackets (e.g. [1]), and a seperate 
        /// content object containing the citation details
        /// </summary>
        private static IEnumerable<Content> GetTextWithCitations(string content)
        {
            Regex regex = new Regex(@"<!-- Page=([^>]+) -->");
            MatchCollection matches = regex.Matches(content);

            Dictionary<string, int> citationDict = new Dictionary<string, int>();
            List<string> citations = new List<string>();
            List<string> citations2 = new List<string>();
            var modifiedText = content;

            foreach (Match match in matches)
            {
                string citation = match.Groups[1].Value;
                if (!citationDict.ContainsKey(citation))
                {
                    citationDict[citation] = citations.Count + 1;
                    citations.Add(citation);
                }

                // Replace the citation in the input text with its index
                int index = citationDict[citation];
                modifiedText = modifiedText.Replace(match.Value, $"[{index}]");
            }

            // Return the text
            yield return new Content { Type = cType.Text, Value = modifiedText };

            // Return the citations
            if (citations.Count != 0)
            {
                for (int i = 0; i < citations.Count; i++)
                {
                    citations2.Add($"{i + 1}. {citations[i]}");
                }
                yield return new Content { Type = cType.Citations, Value = citations2 };
            }
        }

        /// <summary>
        /// Generates follow-up questions based on the answer
        /// </summary>
        private async Task<Content> GenerateFollowupQuestions(string answer,
                                                             OpenAIPromptExecutionSettings settings,
                                                             CancellationToken ct)
        {
            if (string.IsNullOrWhiteSpace(answer))
            {
                return new Content
                {
                    Type = cType.Follow_Up_Questions,
                    Value = new List<string>()
                };
            }
            var followUpQuestionChat = new ChatHistory();

            followUpQuestionChat.AddSystemMessage(
                $$"""
                Generate three follow-up questions based on the answer you just generated.
                Ensure each generated question is brief and concise; no longer than 10 words each. 
                # Answer
                {{answer.ToString()}}

                # Format of the response
                Return the follow-up questions as a pipe delimited string, for example:
                What is the deductible?|What is the co-pay?|What is the out-of-pocket maximum?
                """);

            var response = await _chatService.GetChatMessageContentAsync(
                followUpQuestionChat, settings, kernel: _kernel, cancellationToken: ct);

            var questions = string.IsNullOrWhiteSpace(response.Content) ? new List<string>() : response.Content
                .Split("|", StringSplitOptions.RemoveEmptyEntries)
                .Select(q => q.Trim())
                .ToList();

            return new Content { Type = cType.Follow_Up_Questions, Value = questions };
        }
    }
}