
const apiBaseUrl = import.meta.env.VITE_API_URL; // Define a base URL for the API from the environment files
// console.log('API URL:', apiBaseUrl);

export const chatService = (
  workspace: string | null,
  settings: object,
  messages: { role: string; content: { type: string; value: string | object }[] }[],
  onDataChunk: (chunk: string) => void,
  token: string
) => {
  return new Promise((resolve, reject) => {
    fetch(`${apiBaseUrl}/Chat`, { // /api is the proxy endpoint (from vite.config.ts)
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token,
      },
      body: JSON.stringify({ workspace_id: workspace, settings, messages }), // Ensure the JSON structure matches what the backend expects
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        const reader = response.body?.getReader();
        const decoder = new TextDecoder("utf-8");

        function read() {
          reader!
            .read()
            .then(({ done, value }) => {
              // Asserts that reader is not undefined
              if (done) {
                resolve("Stream finished");
                return;
              }
              const chunk = decoder.decode(value, { stream: true });
              onDataChunk(chunk);
              read();
            })
            .catch(reject);
        }
        
        if (reader) {
          read();
        } else {
          reject("No response body");
        }
      })
      .catch(reject);
  });
};