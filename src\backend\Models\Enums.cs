﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// (System, Assistant, User, Tool)
    /// </summary>
    public enum ChatRole { System, Assistant, User, Tool }

    /// <summary>
    /// (Text, Vector, Hybrid)
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))] 
    public enum RetrievalMode { Text = 0, Vector, Hybrid }

    /// <summary>
    /// (False, True, Error)
    /// </summary>
    public enum Processed { False = 0, True = 1, Error = 2}

    /// <summary>
    /// (Text, Image, Follow_Up_Questions, Citations)
    /// </summary>
    public enum cType { Text = 0, Image = 1, Follow_Up_Questions = 3, Citations = 4 }
}
