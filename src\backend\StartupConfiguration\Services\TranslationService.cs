﻿using Azure.AI.Translation.Document;
using Azure.AI.Translation.Text;
using Azure.Storage.Blobs;
using Backend.Models;
using Backend.Services;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureTranslationService(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<ITranslationService, TranslationService>(sp =>
            {
                return new TranslationService(
                    sp.GetRequiredService<Dictionary<string, BlobServiceClient>>()
                        .ToDictionary(kvp => kvp.Key,
                                      kvp => kvp.Value.GetBlobContainerClient(config["AzureStorage:Translation"])),
                    config.GetValue<int>("AzureStorage:ExpirationPolicy"),
                    new DocumentTranslationClient(new Uri(config["Translation:Endpoint"]!), _azureCredential), 
                    new TextTranslationClient(_azureCredential, new Uri(config["Translation:Endpoint"]!)),
                    sp.GetRequiredService<Dictionary<string, BlobServiceClient>>()
                        .ToDictionary(kvp => kvp.Key,
                                      kvp => kvp.Value.Uri.ToString().TrimEnd('/') + '/' + config["AzureStorage:Translation"]));
            });
        }
    }
}
