import { ChatMessageProps, TransformedMessageProps } from "./types";
import { CHAT_MSSG_ROLE_ASSISTANT, CHAT_MSSG_ROLE_USER } from "../constants/constants";


export const chatMessagesTransform = (chatMessages: ChatMessageProps[]): TransformedMessageProps[] => {
    return chatMessages.map(message => {
        return {
            role: message.user === CHAT_MSSG_ROLE_USER ? CHAT_MSSG_ROLE_USER : CHAT_MSSG_ROLE_ASSISTANT,
            content: message.text
        };
    });
}


export const apiResponseMessagesTransform = (transformedMessages: TransformedMessageProps[]): ChatMessageProps[] => {
     
    return transformedMessages.map((message, index) => {
        return {
            user: message.role === CHAT_MSSG_ROLE_USER ? CHAT_MSSG_ROLE_USER : CHAT_MSSG_ROLE_ASSISTANT,
            text: message.content,
            messageId: index, // Add the messageId property
            isStreamComplete: true,
            workspaceId:  "",
            documentId: "",
            
        };
    });
};