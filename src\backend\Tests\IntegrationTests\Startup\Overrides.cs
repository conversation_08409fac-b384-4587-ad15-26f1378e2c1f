﻿using Azure.Storage.Blobs;
using Backend.Models;
using Backend.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace BackendIntegrationTests
{
    public class BackendWebAppFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
    {
        protected override IHost CreateHost(IHostBuilder builder)
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing authentication services
                services.RemoveAll(typeof(Microsoft.AspNetCore.Authentication.AuthenticationSchemeOptions));
                services.RemoveAll(typeof(Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerOptions));

                // Add in-memory implementations
                services.AddSingleton<ISearchService, InMemorySearch>();

                // Add a test authentication handler that bypasses authentication
                services.AddAuthentication("Test")
                    .AddScheme<AuthenticationSchemeOptions, TestAuthHandler>("Test", options => { });

                // Add a custom authorization policy that always succeeds
                services.AddSingleton<IAuthorizationHandler, AllowAnonymousAuthorizationHandler>();
                services.AddAuthorization(options =>
                {
                    options.AddPolicy("TestPolicy", policy =>
                    {
                        policy.Requirements.Add(new AllowAnonymousRequirement());
                    });
                });

                // Remove the existing IStorageService registration
                var storageServiceDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IStorageService));
                if (storageServiceDescriptor != null)
                {
                    services.Remove(storageServiceDescriptor);
                }

                // Add the in-memory blob container client
                services.AddSingleton(new Dictionary<string, BlobContainerClient>
                {
                    { "Default", new InMemoryBlobContainerClient() }
                });

                // Replace the BlobStorageService with one that uses in-memory blob clients
                services.AddSingleton<IStorageService, BlobStorageService>(sp =>
                {
                    var containerClients = sp.GetRequiredService<Dictionary<string, BlobContainerClient>>()
                        .ToDictionary(kvp => kvp.Key, kvp => (BlobContainerClient)kvp.Value);
                    var config = sp.GetRequiredService<IConfiguration>();
                    var taskQueue = sp.GetRequiredService<ITaskQueue>();
                    return new BlobStorageService(containerClients, config.GetValue<int>("AzureStorage:ExpirationPolicy"), taskQueue);
                });
            });

            return base.CreateHost(builder);
        }
    }

    public class TestAuthHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        public TestAuthHandler(IOptionsMonitor<AuthenticationSchemeOptions> options, ILoggerFactory logger, UrlEncoder encoder, ISystemClock clock)
            : base(options, logger, encoder, clock)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var claims = new[]
            {
            new Claim(ClaimTypes.Name, "userTest"),
            new Claim("xms_pdl", "NAM"),
            new Claim("OID", "userTest")
        };
            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, "Test");

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }
    }

    public class AllowAnonymousRequirement : IAuthorizationRequirement { }

    public class AllowAnonymousAuthorizationHandler : AuthorizationHandler<AllowAnonymousRequirement>
    {
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, AllowAnonymousRequirement requirement)
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }
    }
}
