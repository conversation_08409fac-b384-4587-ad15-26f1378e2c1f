import { DISCLAIMER_DATA, DISCLAIMER_HEADING, AJG_ONE_URL } from "../../../constants/constants";
import React, { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

const DisclaimerModal: React.FC = () => {
    const [cookieValue, setCookieValue] = useState<string | null>(null);

    useEffect(() => {
        const cookie = Cookies.get('disclaimerAccepted');
        setCookieValue(cookie || 'false');
    }, []);

    const handleAccept = () => {
        Cookies.set('disclaimerAccepted', 'true', { expires: 36500 });
        setCookieValue('true');
    };

    if (cookieValue === null) {
        return null; // Return null while the cookie value is being fetched
    }

    return (
        cookieValue === 'true' ? null : (
            <div className="fixed mt-10 inset-0 flex items-center justify-center bg-black/50 p-4 sm:p-6 md:p-8 z-10">
                <div className="bg-white dark:bg-zinc-800 dark:text-white p-4 sm:p-6 md:p-8 rounded-lg shadow-lg max-w-full sm:max-w-md md:max-w-[70%] lg:max-w-[70%] max-h-[90vh] flex flex-col">
                    <div className="overflow-y-auto flex-1">
                        <h2 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-4">
                            {DISCLAIMER_HEADING}
                        </h2>
                        {DISCLAIMER_DATA.map((data: string, ind: number) => (
                            <p className="mb-2 sm:mb-4" key={ind}>
                                {data}
                            </p>
                        ))}
                    </div>
                    <div className="flex justify-between mt-4">
                        <button
                            className="bg-white dark:bg-zinc-700 dark:text-white dark:border-gray-600 border-2 text-black px-4 py-2 rounded-sm hover:bg-gallagher-blue-400 hover:text-white hover:border-gallagher-blue-400"
                            onClick={handleAccept}
                        >
                            Accept
                        </button>
                        <a
                            href={AJG_ONE_URL}
                            className="bg-white dark:bg-zinc-700 dark:text-white dark:border-gray-600 border-2 text-black px-4 py-2 rounded-sm hover:bg-gallagher-dark-300 hover:text-white hover:border-gallagher-dark-300"
                        >
                            Decline
                        </a>
                    </div>
                </div>
            </div>
        )
    );
};

export default DisclaimerModal;