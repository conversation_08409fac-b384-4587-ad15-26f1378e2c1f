parameters:
    - name: scanDirectory
      type: string
      default: 'frontend'

steps:
    # Ensure that the Snyk executables have execution permissions.
    - script: |
          if [ -f "$(Agent.TempDirectory)/snyk-linux" ]; then
            chmod +x "$(Agent.TempDirectory)/snyk-linux"
          else
            echo "snyk-linux does not exist yet, it will be downloaded in the next step."
          fi

          if [ -f "$(Agent.TempDirectory)/snyk-to-html-linux" ]; then
            chmod +x "$(Agent.TempDirectory)/snyk-to-html-linux"
          else
            echo "snyk-to-html-linux does not exist yet, it will be downloaded in the next step."
          fi
      displayName: 'Ensure Snyk executables are executable'
      condition: always()

    - task: SnykSecurityScan@1
      inputs:
          serviceConnectionEndpoint: 'Snyk_Connection'
          testType: 'code'
          failOnIssues: true
          organization: 'corp-info-serv-and-appdev-ajg-corp'
          testDirectory: '$(Build.SourcesDirectory)/src/${{ parameters.scanDirectory }}'
      condition: always()
      displayName: SAST
      continueOnError: true

    # Moving the SAST reports (html and json)
    - script: |
          temp_directory=$(Agent.TempDirectory)
          if [ ! -d "$temp_directory/snyk-reports" ]; then
              mkdir -p "$temp_directory/snyk-reports"
          else
              echo "snyk-reports folder exist"
          fi
          json_file=$(ls "$temp_directory"/report*.json 2>/dev/null)
          html_json=$(ls "$temp_directory"/report*.html 2>/dev/null)
          echo $file
          if [ -n "$json_file" ]; then
              mv $temp_directory/report*.json $temp_directory/snyk-reports/SAST-report.json
          else
              echo "JSON-Report not generated"
          fi
          if [ -n "$html_json" ]; then
              mv $temp_directory/report*.html $temp_directory/snyk-reports/SAST-report.html
          else
              echo "HTML-Report not generated"
          fi
      displayName: 'Getting SAST Reports'
      condition: always()
      continueOnError: true

    - script: |
        corepack enable
        corepack prepare pnpm@9.12.1 --activate
      displayName: 'Install pnpm'

    - script: |
        cd $(Build.SourcesDirectory)/src/${{ parameters.scanDirectory }}
        pnpm install --no-frozen-lockfile
      displayName: 'Install Node Dependencies for Snyk'

    - task: SnykSecurityScan@1
      inputs:
          serviceConnectionEndpoint: 'Snyk_Connection'
          testType: 'app'
          monitorWhen: 'always'
          failOnIssues: true
          organization: 'corp-info-serv-and-appdev-ajg-corp'
          testDirectory: '$(Build.SourcesDirectory)/src/${{ parameters.scanDirectory }}'
          additionalArguments: '--all-projects -d'
      condition: always()
      displayName: SCA
      continueOnError: true

    # Moving the SCA reports (html and json)

    - script: |
          temp_directory=$(Agent.TempDirectory)
          if [ ! -d "$temp_directory/snyk-reports" ]; then
              mkdir -p "$temp_directory/snyk-reports"
          else
              echo "snyk-reports folder exist"
          fi
          json_file=$(ls "$temp_directory"/report*.json 2>/dev/null)
          html_json=$(ls "$temp_directory"/report*.html 2>/dev/null)
          echo $file
          if [ -n "$json_file" ]; then
              mv $temp_directory/report*.json $temp_directory/snyk-reports/SCA-report.json
          else
              echo "JSON-Report not generated"
          fi
          if [ -n "$html_json" ]; then
              mv $temp_directory/report*.html $temp_directory/snyk-reports/SCA-report.html
          else
              echo "HTML-Report not generated"
          fi
      displayName: 'Getting SCA Reports'
      condition: always()
      continueOnError: true

    - task: PublishPipelineArtifact@1
      displayName: 'Publish Snyk Reports'
      inputs:
          targetPath: '$(Agent.TempDirectory)/snyk-reports'
          artifact: 'snyk-report-frontend'
      condition: always()
      continueOnError: true
