import React, { useLayoutEffect, useState, useRef } from "react";
import { ToastProps } from "../../../interfaces";

const positionClasses: Record<ToastProps["position"], string> = {
  "top-left": "top-0 left-0 mt-16 ml-6",
  "top-center": "top-0 left-1/2 transform -translate-x-1/2 mt-16",
  "top-right": "top-0 right-0 mt-16 mr-6",
  "mid-left": "top-1/2 left-0 transform -translate-y-1/2 ml-6",
  "mid-center": "top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
  "mid-right": "top-1/2 right-0 transform -translate-y-1/2 mr-6",
  "bottom-left": "bottom-0 left-0 mb-16 ml-6",
  "bottom-center": "bottom-0 left-1/2 transform -translate-x-1/2 mb-16",
  "bottom-right": "bottom-0 right-0 mb-16 mr-6",
};

const Toast: React.FC<ToastProps> = ({
  position,
  className = "",
  title,
  text,
  icon,
  duration = 3,
  onClose,
  bgColor = "bg-gallagher-blue-400",
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startTimer = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);

    timeoutRef.current = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // Wait for animation before remove the toast
    }, duration * 1000);
  };

  useLayoutEffect(() => {
    setIsVisible(true);
    startTimer();

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [duration, onClose]);

  const handleMouseEnter = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current); // Pause timer
  };

  const handleMouseLeave = () => {
    startTimer(); // Resume the timer on mouse leave the toast area
  };

  return (
    <div
      className={`fixed p-3 ${bgColor} text-white text-sm rounded-lg shadow-lg z-50
        transition-all duration-300 ease-in-out flex items-center gap-2
        ${positionClasses[position]}
        ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4 pointer-events-none"}
        ${className}`}
      role="alert"
      aria-live="assertive"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {icon && <span className="text-lg" aria-label="icon">{icon}</span>}
      <div>
        {title && <div className="font-semibold">{title}</div>}
        <div>{text}</div>
      </div>
    </div>
  );
};

export default Toast;