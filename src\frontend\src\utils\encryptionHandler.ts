import { addData, getAllData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { AppDispatch, RootState } from "../store";
import { chatEncryption } from "./chatEncryption";
import { chatMessagesTransform } from "./chatMessagesTransform";
import { ChatMessageProps } from "./types";
import { addChatArray, updateChatArray } from "../features/AllChatHistorySlice";
import { updateHash } from "../db/chatDB";
import { recentChatTabsData } from "./recentChatTabsData";
import { AllChatState } from "../interfaces";

export const encryptionHandler = async (chatMessages: ChatMessageProps[], email: string, token: string, dispatch: AppDispatch, currentChatId: string | undefined, currentWorkspaceId: string, getState?: () => RootState) => {
    try {
        // Transform messages for encryption
        const transformedMessages = chatMessagesTransform(chatMessages);
        let workspaceId = currentWorkspaceId;

        // Encrypt the messages
        const encryptedMessage = await chatEncryption(transformedMessages, email, token);

        // Get all current data from the database
        let allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

        // Case 1: New chat (no current chat ID)
        if(!currentChatId && encryptedMessage){
            // Generate encrypted data with a unique ID
            const encryptedData = transformEncryptedData(encryptedMessage, workspaceId);
            const newData = [{ ...encryptedData }];

            // Add to the database (this will also remove the oldest chat if necessary)
            await addData(Stores.Users, newData);

            // Get updated data from the database
            const newDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

            // Sort by date (most recent first)
            const sortedData = [...newDbData].sort((a, b) =>
                new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            // Find the new chat (should be the most recent)
            const newChat = sortedData.find(data => data.hash === encryptedMessage);

            if (newChat) {
                // Add the new chat to the global state
                dispatch(addChatArray({key: newChat.id, chatArray: chatMessages}));

                // Set the current chat ID
                dispatch(setCurrentChatId(newChat.id));

                // 🔥 CRITICAL FIX: Update chat history in real-time
                await updateChatHistoryAfterNewChat(newChat.id, chatMessages, dispatch, getState);
            }
        }

        // Case 2: Update an existing chat
        else if (allDbData.length > 0 && currentChatId) {
            const chatExists = allDbData.some(data => data.id === currentChatId);

            // Get the workspace ID of the current chat
            for(const elem of allDbData){
                if(elem.id === currentChatId){
                    workspaceId = elem.workspaceId;
                }
            }

            if (chatExists) {
                // Update the hash of the existing chat
                await updateHash(Stores.Users, currentChatId, encryptedMessage ? encryptedMessage : "");

                // Update the chat in the global state
                dispatch(updateChatArray({key: currentChatId, chatArray: chatMessages}));

                // 🔥 CRITICAL FIX: Update chat history for existing chat updates too
                await updateChatHistoryAfterNewChat(currentChatId, chatMessages, dispatch, getState);
            }
        }

        // Case 3: Fallback for new chat creation
        else if (encryptedMessage) {
            const encryptedData = transformEncryptedData(encryptedMessage, workspaceId);
            const newData = [{ ...encryptedData }];
            await addData(Stores.Users, newData);
            const newDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

            if(newDbData.length > 0){
                const newChatId = newDbData[newDbData.length-1].id;
                dispatch(addChatArray({key: newChatId, chatArray: chatMessages}));
                dispatch(setCurrentChatId(newChatId));

                // 🔥 CRITICAL FIX: Update chat history for fallback case too
                await updateChatHistoryAfterNewChat(newChatId, chatMessages, dispatch, getState);
            }
        }

        // Finish the operation
        dispatch(setApiCallFlag(false));
    } catch (error) {
        console.error('Error in encryptionHandler:', error);
        dispatch(setApiCallFlag(false));
    }
};

function transformEncryptedData(encryptedMessage: string, workspaceId: string) {
    const date = new Date();
    const isoString = date.toISOString();
    const uniqueId = makeAlphanumeric(isoString);

    return {
        hash: encryptedMessage,
        date: isoString,
        id: uniqueId,
        workspaceId: workspaceId
    };
}

export function makeAlphanumeric(dateString: string) {
    return dateString.replace(/[^a-zA-Z0-9]/g, '');
}

/**
 * 🔥 CRITICAL FIX: Updates chat history in real-time after new chat creation or updates
 * This ensures the chat history sidebar reflects changes immediately without requiring a page refresh
 *
 * Following RTK Query patterns established in the project documentation
 */
async function updateChatHistoryAfterNewChat(
    chatId: string,
    chatMessages: ChatMessageProps[],
    dispatch: AppDispatch,
    getState?: () => RootState
): Promise<void> {
    try {
        // 🔥 FIX: Get the COMPLETE chat history from Redux store to avoid disappearing conversations
        // We need to access the current Redux state to get all existing chats
        let existingAllChat = {};

        if (getState) {
            const currentState = getState();
            existingAllChat = currentState.allChat?.allChat || {};
        }

        // Get database data for validation and sorting
        const allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

        if (allDbData.length === 0) {
            console.warn("No database data found for chat history update");
            return;
        }

        // 🔥 COMPLETE HISTORY: Create allChatHistory with ALL existing chats + the new/updated one
        const allChatHistory: AllChatState = {
            allChat: {
                ...existingAllChat,  // Keep all existing chats
                [chatId]: chatMessages  // Add/update the current chat
            }
        };

        // 🔥 REAL-TIME UPDATE: Call recentChatTabsData with COMPLETE history
        // This ensures all conversations remain visible while adding the new one
        await recentChatTabsData(allChatHistory, dispatch);


    } catch (error) {
        console.error("Error updating chat history after new chat:", error);
        // Don't throw the error to avoid breaking the main encryption flow
    }
}
