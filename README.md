# Introduction
GallagherAI - Application to chat with an OpenAI model including your own file(s).

## Build Status

| App | DEV | TEST | MAIN |
| --- | --- | --- | --- |
| Front End | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FFrontend-CD?branchName=dev)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1401&branchName=dev) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FFrontend-CD?branchName=test)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1401&branchName=test) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FFrontend-CD?branchName=main)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1401&branchName=main) |
| Back End | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FBackend-CD?branchName=dev)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1400&branchName=dev) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FBackend-CD?branchName=test)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1400&branchName=test) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FBackend-CD?branchName=main)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1400&branchName=main) |

## Architecture

GallagherAI is built as a modern web application with a clear separation between frontend and backend components.

### Frontend (React + TypeScript)

**Tech Stack:**
- **React 19** with TypeScript for type-safe UI development
- **Redux Toolkit + RTK Query** for state management and API integration
- **Vite** for fast development and optimized builds
- **TailwindCSS** for consistent styling
- **MSAL** for Microsoft Azure authentication

**Key Features:**
- Centralized state management with automatic API caching
- Type-safe development with comprehensive TypeScript integration
- Performance optimization through intelligent caching and background refetching
- Responsive design with modern UI components

📚 **[Frontend Documentation](src/frontend/docs/README.md)** - Comprehensive guides for frontend development

### Backend (.NET 8 Web API)

**Tech Stack:**
- **.NET 8** Web API with C# for robust server-side logic
- **Microsoft Semantic Kernel** for AI orchestration
- **Azure Services** integration (OpenAI, Cognitive Search, Blob Storage, Document Intelligence)
- **Microsoft Identity** for authentication and authorization
- **Swagger/OpenAPI** for API documentation

**Key Features:**
- RESTful API with streaming support for real-time chat
- Secure document processing and storage
- Multi-language translation capabilities
- Comprehensive workspace and document management
- Background task processing for long-running operations

📚 **[Backend API Documentation](src/backend/API_README.md)** - Complete API reference and endpoints

## Quick Start

### Frontend Development
```bash
cd src/frontend
pnpm install
pnpm run localhost
```
**[Frontend Setup Guide](src/frontend/README.md)** - Detailed setup instructions

### Backend Development
```bash
cd src/backend
dotnet restore
dotnet run
```
**[Backend API Documentation](src/backend/API_README.md)** - API endpoints and usage

## gitflow

For source code management, we are using a process very close to [gitflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow).

### Key branches
`main` - Official release history main branch.

`dev` - Integration branch for features.

`test` - Test branch that allows deploying to test environment. IaC team wants to use this branch also for infra needs. They look for this branch in their pipelines.

`feature/[branch-name]` - Branch off from `dev` for feature development.

`release/[branch-name]` - Branched from `dev` and used for last-minute updates before a release. Eg. updating the changelog. This will be merged back into `dev` branch

### Workflow

1. **Branch creation**: Create a new branch from `dev` when new feature work begins.
2. **Deploy to dev environment**: It's ok while developing to deploy to the dev environment from one's feature branch to test something. We can always deploy again from the `dev` branch to sync up the dev environment.
3. **Pull Requests**: Once a feature or bug is complete, submit a PR to `dev`. Suggest deleting the feature branch at this point.
4. **Review and CI**: At least one other developer needs to review the merge to `dev` branch.
5. **Test branch**: PR from `dev` branch to `test` branch when wanting to deploy to test environment.
6. **Release branch**: Create from `dev` branch to update any last minute issues like changelogs and readmes. PR this to `main` branch once ready. Also PR this to `dev` branch to keep it in sync. The release branch is not needed to be kept once merged into `main` branch.

![Release branch flow](docs/images/releaseflow-v2.png)

7. **Tags**: Once a deployment to production is complete from the `main` branch, tag the `main` branch following this pattern: yyyymmdd.

### How to prep for deployment to Production....easily

1. Create release branch from dev branch. Name it in this format: `release\[yyyymmdd]`
2. Update the CHANGELOG of the root with all changes going live.
3. Update the frontend 'updates' page located here: `/src/components/TabsSection/UpdatesTab.tsx`
4. Update the frontend version file so the 'red dot' shows up for the 'Updates' page: `src/frontend/public/version.json`
5. Check on dependencies on backend/frontend/infra to make sure we are using the latest versions.
6. Once everything is tested in DEV environment, deploy to TEST environment.
7. Work with the Change Manager on the Remedy CRQ that will be required to complete the deployment.
8. PR the Release branch to Main branch.
9. Main branch is used to deploy to production.
10. POST-DEPLOY, Tag the Main branch to a new tag.
11. POST-DEPLOY, PR the Release branch merge to Dev branch.
12. Delete the Release branch.