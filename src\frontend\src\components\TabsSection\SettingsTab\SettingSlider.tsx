import React, { useCallback } from "react";
import Tooltip from "../../Tooltip/Tooltip";
import { Info16Regular } from "@fluentui/react-icons";

interface SettingSliderProps {
  setting: {
    label: string;
    description: string;
    minValue: number;
    maxValue: number;
    step: number;
    minHint: string;
    maxHint: string;
  };
  value: number;
  onChange: (value: number) => void;
}

const SettingSlider: React.FC<SettingSliderProps> = ({ setting, value, onChange }) => {
  // Handle slider change with debounce
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value);
    onChange(newValue);
  }, [onChange]);

  return (
    <div>
      <div className="flex items-center mt-6">
        <h3 className="font-semibold">{setting.label}</h3>
        <Tooltip message={setting.description} position="bottom" className="w-[200px] absolute">
          <Info16Regular className="ml-2 text-black dark:text-white" />
        </Tooltip>
      </div>
      <input
        type="range"
        min={setting.minValue}
        max={setting.maxValue}
        step={setting.step}
        value={value}
        onChange={handleChange}
        className="w-full"
      />
      <div className="flex justify-between text-xs">
        <span>{setting.minValue}</span>
        <span className="font-semibold">{value.toFixed(2)}</span>
        <span>{setting.maxValue}</span>
      </div>
      <div className="flex justify-between text-xs">
        <span>{setting.minHint}</span>
        <span>{setting.maxHint}</span>
      </div>
    </div>
  );
};

export default SettingSlider;
