﻿using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using static Common.Common;

partial class Program
{
    private enum BlobType
    {
        Document,
        Workspace,
        User
    }

    private static BlobContainerClient? _blobClient;
    private static SearchClient? _searchClient;

    /// <summary>
    /// Main script
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    static async Task Main(string[] args)
    {
        Console.WriteLine("Initializing Storage Maintenance...");
        Initialize();

        try
        {
            foreach (var key in _blobClients!.Keys)
            {
                _blobClient = _blobClients[key];
                _searchClient = _searchClients![key];

                // Delete outdated documents
                Log(LogLevel.Information, $"Storage Maintenance ${key.ToString()}: Removing expired documents...");
                await RemoveBlobs(BlobType.Document);

                Console.WriteLine(_thresholdDate);

                // Delete outdated workspaces
                Log(LogLevel.Information, $"Storage Maintenance ${key.ToString()}: Removing expired workspaces...");
                await RemoveBlobs(BlobType.Workspace);

                // Delete outdated users
                Log(LogLevel.Information, $"Storage Maintenance ${key.ToString()}: Removing expired users...");
                await RemoveBlobs(BlobType.User);
            }
        }
        catch (Exception ex)
        {
            Log(LogLevel.Error, "Storage Maintenance: An error occurred during Storage Maintenance.", ex);
        }
    }

    /// <summary>
    /// Removes blobs from storage that are older than the threshold date, 
    /// along with their data in the search index.
    /// </summary>
    /// <param name="thresholdDate"></param>
    /// <param name="blobType"></param>
    /// <returns></returns>
    private static async Task RemoveBlobs(BlobType blobType)
    {
        var blobPages = _blobClient!.GetBlobsAsync(traits: BlobTraits.Metadata | BlobTraits.Tags).AsPages(pageSizeHint: 100);

        await foreach (var blobPage in blobPages)
        {
            foreach (var item in blobPage.Values)
            {
                if (item.Properties.LastModified <= _thresholdDate
                    && (blobType == BlobType.Document ?
                        !item.Metadata.ContainsKey("hdi_isfolder") :
                        item.Metadata.ContainsKey("hdi_isfolder"))
                    && (blobType == BlobType.User ?
                        !item.Name.Contains("/") :
                        item.Name.Contains("/"))
                    && (blobType != BlobType.Document ?
                        !await HasChildBlobs(item.Name) :
                        true))
                {
                    try
                    {
                        if (blobType == BlobType.Document) { await RemoveFromIndex(item.Name); }
                        await _blobClient.DeleteBlobAsync(item.Name);
                        Log(LogLevel.Information, $"Storage Maintenance: Removed {item.Name}");
                    }
                    catch (RequestFailedException ex) when (ex.Status == 429 || ex.Status == 503)
                    {
                        Log(LogLevel.Error, $"Storage Maintenance: Failed to remove blob {item.Name}: {ex.Message}", ex);
                        await Task.Delay(TimeSpan.FromSeconds(5));
                    }
                    catch (Exception ex)
                    {
                        Log(LogLevel.Error, $"Storage Maintenance: Failed to remove blob {item.Name}: {ex.Message}", ex);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Checks if a blob has any child blobs.
    /// </summary>
    /// <param name="blobName"></param>
    /// <returns></returns>
    private static async Task<bool> HasChildBlobs(string blobName)
    {
        await foreach (var item in _blobClient!.GetBlobsByHierarchyAsync(prefix: blobName + "/", delimiter: "/"))
        {
            if (item.IsPrefix || item.Blob != null)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Removes a document from the search index
    /// </summary>
    /// <param name="blob"></param>
    /// <returns></returns>
    private static async Task RemoveFromIndex(string blob)
    {
        try
        {
            var filter = $"sourcefile eq '{blob?.Replace("'", "''")}'";
            var documentsToDelete = new List<SearchDocument>();

            var response = await _searchClient!.SearchAsync<SearchDocument>("",
                new SearchOptions
                {
                    Filter = filter,
                    Size = 1_000
                });

            await foreach (var result in response.Value.GetResultsAsync())
            {
                documentsToDelete.Add(new SearchDocument
                {
                    ["id"] = result.Document["id"]
                });
            }

            if (documentsToDelete.Count > 0)
            {
                await _searchClient.DeleteDocumentsAsync(documentsToDelete);
            }
        }
        catch (Exception ex)
        {
            Log(LogLevel.Error, $"Storage Maintenance: Failed to remove blob from index {blob}.", ex);
        }
    }
}
